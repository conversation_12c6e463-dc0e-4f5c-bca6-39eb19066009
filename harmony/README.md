# harmony
This repository serves as the **backend** for Instrumentality.

`db` - Database connection and pooling logic

`prisma` - DB/Prisma schema definition and migration files

`src` - Main backend files
- `apis` - Client integration of third-party services (e.g. Supabase)
- `controllers` - Operations logic - routes trigger these
- `middleware` - Middleman code for handling route validation, handling, files, logging, and auth
- `routes` - Express API routes to access backend logic
- `types` - Enums, etc. relating to each model/controller category
- `utils` - Various util functions
- `validation` - Route validation schemas for request integrity checking

`supabase` - Linking between local and remote DB instances (primary method is still Prisma)

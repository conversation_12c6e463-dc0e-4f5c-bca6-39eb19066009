import * as dotenv from "dotenv";
import { Pool } from "pg";

dotenv.config();

// Use a single environment variable for the database URL
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  throw new Error("DATABASE_URL is not set in the environment variables");
}

// Create a new pool using the connection string
const pool = new Pool({
  connectionString: databaseUrl,
  // If you're using SSL (common for hosted databases), uncomment the following line:
  // ssl: { rejectUnauthorized: false }
});

export default pool;

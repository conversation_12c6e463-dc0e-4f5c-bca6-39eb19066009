// pool manages a set of reusable database connections
import pool from '../db/config';


/**
 * @param text - the string representing your SQL query
 * @param params - an optional array of parameters to pass to the SQL query
 * @returns 
 */
export async function query(text: string, params?: any[]) {
    // waits for a client from the connection pool  
    const client = await pool.connect();
    try {
        // client.query returns a promise that resolves to the result of the query
        const result = await client.query(text, params);
        return result;
    } finally {
        // whether query succeeds or fails, client is released back to pool
        // important for proper resource management
        client.release();
    }
}
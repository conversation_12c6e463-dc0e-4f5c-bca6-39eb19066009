# docker-compose.test.yml
version: '3.8'
services:
  test-db:
    image: postgres:15
    ports:
      - "5433:5432"  # Note: Using 5433 to avoid conflicts with local Postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: harmony_test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
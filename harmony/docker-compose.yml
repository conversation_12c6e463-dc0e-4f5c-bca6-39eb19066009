services:
  postgres:
    image: postgres:14
    container_name: test_db
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: test_db
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user"]
      interval: 5s
      retries: 5

  api:
    image: supabase/gotrue:v2.16.0 # Use a specific version for GoTrue
    container_name: supabase_api
    environment:
      APP_NAME: instrumentalitytest
      DATABASE_URL: ************************************************/test_db
      DB_HOST: test_db
      DB_PORT: 5433
      SITE_URL: http://localhost:3000
      JWT_SECRET: supersecret123
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_JWT_SECRET: supersecret123
    ports:
      - "3000:3000"
    depends_on:
      - postgres

  realtime:
    image: supabase/realtime:v2.16.0 # Use a specific version for Realtime
    container_name: supabase_realtime
    environment:
      DB_URL: ************************************************/test_db
      SECRET_KEY_BASE: supersecret123
      APP_NAME: instrumentalitytest
    ports:
      - "4000:4000"
    depends_on:
      - postgres

volumes:
  supabase_db:

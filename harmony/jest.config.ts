// jest.config.ts
import type { Config } from "@jest/types";

const config: Config.InitialOptions = {
  preset: "ts-jest", // Enables TypeScript support
  testEnvironment: "node", // Run tests in Node environment
  roots: ["<rootDir>/src"], // Look for tests in src directory
  testMatch: ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], // Pattern to find test files
  transform: {
    "^.+\\.ts$": ["ts-jest", { tsconfig: "tsconfig.json" }], // Transform TypeScript files
  },
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1", // Allows @ imports
  },
  setupFilesAfterEnv: ["<rootDir>/src/__tests__/setup.ts"], // Run setup before tests
  coverageDirectory: "coverage", // Where to output coverage reports
  coveragePathIgnorePatterns: [
    // What to exclude from coverage
    "/node_modules/",
    "/dist/",
    "/build/",
    "/__tests__/",
  ],
  globals: {},
};

export default config;

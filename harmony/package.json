{"name": "harmony", "version": "1.0.0", "main": "index.js", "repository": "https://github.com/InstrumentalityCo/harmony.git", "author": "SpaceRage <<EMAIL>>", "license": "MIT", "dependencies": {"@asteasolutions/zod-to-openapi": "^7.3.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@prisma/client": "^6.5.0", "@supabase/supabase-js": "^2.46.1", "archiver": "^7.0.1", "chalk": "^4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "fluent-ffmpeg": "^2.1.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "multer": "^1.4.5-lts.2", "node-fetch": "2.7.0", "node-localstorage": "^3.0.5", "node-wav": "^0.0.2", "pg": "^8.13.0", "react-datepicker": "^8.2.1", "socket.io": "^4.8.1", "tus-js-client": "^4.3.1", "zod": "^3.24.1"}, "scripts": {"dev": "npx nodemon --config nodemon.json", "build": "rimraf ./build && tsc && tsc-alias", "start": "ts-node build/index.js", "test:db:up": "docker-compose -f docker-compose.test.yml up -d", "test:db:down": "docker-compose -f docker-compose.test.yml down", "test": "yarn test:db:up && npx dotenv -e .env.test -- prisma migrate deploy && npx dotenv -e .env.test jest --runInBand --forceExit", "test:watch": "yarn test:db:up && npx dotenv -e .env.test -- prisma migrate deploy && npx dotenv -e .env.test jest --watch --runInBand", "test:coverage": "yarn test:db:up && npx dotenv -e .env.test jest --coverage --runInBand"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.8", "@types/multer": "^1.4.12", "@types/node": "^22.7.6", "@types/node-fetch": "2.6.11", "@types/node-localstorage": "^1.3.3", "@types/node-wav": "^0.0.4", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.7", "dotenv-cli": "^8.0.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prisma": "^6.5.0", "rimraf": "4", "strip-ansi": "^6.0.1", "supabase": "^2.2.1", "supertest": "^6.3.3", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}
/*
  Warnings:

  - You are about to drop the column `author_id` on the `Comment` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Comment` table. All the data in the column will be lost.
  - You are about to drop the column `project_id` on the `Comment` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Comment` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `file_type` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `s3_path` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `version_id` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `author_id` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `added_at` on the `ProjectUser` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `ProjectUser` table. All the data in the column will be lost.
  - You are about to drop the column `project_id` on the `ProjectUser` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `ProjectUser` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `ProjectUser` table. All the data in the column will be lost.
  - You are about to drop the column `assignee_id` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `author_id` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `due_date` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `project_id` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Version` table. All the data in the column will be lost.
  - You are about to drop the column `project_id` on the `Version` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Version` table. All the data in the column will be lost.
  - Added the required column `authorId` to the `Comment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `projectId` to the `Comment` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fileType` to the `File` table without a default value. This is not possible if the table is not empty.
  - Added the required column `s3Path` to the `File` table without a default value. This is not possible if the table is not empty.
  - Added the required column `versionId` to the `File` table without a default value. This is not possible if the table is not empty.
  - Added the required column `authorId` to the `Project` table without a default value. This is not possible if the table is not empty.
  - Added the required column `projectId` to the `ProjectUser` table without a default value. This is not possible if the table is not empty.
  - Added the required column `userId` to the `ProjectUser` table without a default value. This is not possible if the table is not empty.
  - Added the required column `assigneeId` to the `Task` table without a default value. This is not possible if the table is not empty.
  - Added the required column `authorId` to the `Task` table without a default value. This is not possible if the table is not empty.
  - Added the required column `projectId` to the `Task` table without a default value. This is not possible if the table is not empty.
  - Added the required column `projectId` to the `Version` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."Comment" DROP CONSTRAINT "Comment_author_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Comment" DROP CONSTRAINT "Comment_project_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."File" DROP CONSTRAINT "File_version_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Project" DROP CONSTRAINT "Project_author_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."ProjectUser" DROP CONSTRAINT "ProjectUser_project_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."ProjectUser" DROP CONSTRAINT "ProjectUser_user_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Task" DROP CONSTRAINT "Task_assignee_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Task" DROP CONSTRAINT "Task_author_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Task" DROP CONSTRAINT "Task_project_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."Version" DROP CONSTRAINT "Version_project_id_fkey";

-- AlterTable
ALTER TABLE "public"."Comment" DROP COLUMN "author_id",
DROP COLUMN "created_at",
DROP COLUMN "project_id",
DROP COLUMN "updated_at",
ADD COLUMN     "authorId" BIGINT NOT NULL,
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "projectId" BIGINT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."File" DROP COLUMN "created_at",
DROP COLUMN "file_type",
DROP COLUMN "s3_path",
DROP COLUMN "updated_at",
DROP COLUMN "version_id",
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "fileType" TEXT NOT NULL,
ADD COLUMN     "s3Path" TEXT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "versionId" BIGINT NOT NULL;

-- AlterTable
ALTER TABLE "public"."Project" DROP COLUMN "author_id",
DROP COLUMN "created_at",
DROP COLUMN "updated_at",
ADD COLUMN     "authorId" BIGINT NOT NULL,
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."ProjectUser" DROP COLUMN "added_at",
DROP COLUMN "created_at",
DROP COLUMN "project_id",
DROP COLUMN "updated_at",
DROP COLUMN "user_id",
ADD COLUMN     "addedAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "projectId" BIGINT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "userId" BIGINT NOT NULL;

-- AlterTable
ALTER TABLE "public"."Task" DROP COLUMN "assignee_id",
DROP COLUMN "author_id",
DROP COLUMN "created_at",
DROP COLUMN "due_date",
DROP COLUMN "project_id",
DROP COLUMN "updated_at",
ADD COLUMN     "assigneeId" BIGINT NOT NULL,
ADD COLUMN     "authorId" BIGINT NOT NULL,
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "dueDate" TIMESTAMP(3),
ADD COLUMN     "projectId" BIGINT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."User" DROP COLUMN "created_at",
DROP COLUMN "updated_at",
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."Version" DROP COLUMN "created_at",
DROP COLUMN "project_id",
DROP COLUMN "updated_at",
ADD COLUMN     "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "projectId" BIGINT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AddForeignKey
ALTER TABLE "public"."Project" ADD CONSTRAINT "Project_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ProjectUser" ADD CONSTRAINT "ProjectUser_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ProjectUser" ADD CONSTRAINT "ProjectUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Version" ADD CONSTRAINT "Version_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."File" ADD CONSTRAINT "File_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "public"."Version"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Comment" ADD CONSTRAINT "Comment_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Comment" ADD CONSTRAINT "Comment_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Task" ADD CONSTRAINT "Task_assigneeId_fkey" FOREIGN KEY ("assigneeId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Task" ADD CONSTRAINT "Task_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Task" ADD CONSTRAINT "Task_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

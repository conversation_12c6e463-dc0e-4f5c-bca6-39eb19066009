-- CreateTable
CREATE TABLE "Leaflet" (
    "id" BIGSERIAL NOT NULL,
    "userId" BIGINT NOT NULL,
    "name" TEXT NOT NULL,
    "s3Path" TEXT NOT NULL,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "metadata" JSON<PERSON>,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Leaflet_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Leaflet" ADD CONSTRAINT "Leaflet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

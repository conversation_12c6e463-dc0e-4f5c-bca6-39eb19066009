generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  // Connection for transaction pooling - normal ops
  url       = env("DATABASE_URL")
  // Connection for direct queries - for migrations
  directUrl = env("DIRECT_URL")
}

model User {
  id                    BigInt                    @id @default(autoincrement())
  sbid                  String                    @unique
  name                  String                    @unique
  email                 String                    @unique
  role                  String
  bio                   String?
  socials               Json?
  profileImg            String?
  termsPrivacyTime      DateTime                  @default(now())
  termsPrivacyContent   String                    @default("")
  ndaTime               DateTime                  @default(now())
  ndaContent            String                    @default("")
  createdAt             DateTime                  @default(now()) @db.Timestamp(6)
  updatedAt             DateTime                  @default(now()) @db.Timestamp(6)
  comments              Comment[]                 @relation()
  projects              Project[]                 @relation()
  projectUsers          ProjectUser[]             @relation()
  assignedTasks         Task[]                    @relation("TaskAssignee")
  authoredTasks         Task[]                    @relation("TaskAuthor")
  projectInvites        ProjectInvite[]
  authoredVersions      Version[]
  userAudioSamples      Leaflet[]
  messagesSent          Message[]                 @relation("UserMessages")
  conversationsAuthored Conversation[]            @relation("ConversationAuthor")
  conversations         ConversationParticipant[] @relation("UserConversations")
}

model Project {
  id             BigInt          @id @default(autoincrement())
  name           String
  authorId       BigInt
  description    String?
  genre          String
  tempo          Int?
  key            String?
  createdAt      DateTime        @default(now()) @db.Timestamp(6)
  updatedAt      DateTime        @default(now()) @db.Timestamp(6)
  comments       Comment[]       @relation()
  author         User            @relation(fields: [authorId], references: [id], onDelete: Cascade)
  contributors   ProjectUser[]   @relation()
  tasks          Task[]          @relation()
  versions       Version[]       @relation()
  projectInvites ProjectInvite[]
  conversations  Conversation[]  @relation()
}

model ProjectUser {
  id        BigInt   @id @default(autoincrement())
  projectId BigInt
  userId    BigInt
  role      String
  addedAt   DateTime @default(now()) @db.Timestamp(6)
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
}

model Version {
  id        BigInt   @id @default(autoincrement())
  projectId BigInt
  authorId  BigInt?
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  message   String?
  number    BigInt
  files     File[]   @relation()
  folders   Folder[] @relation()
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  author    User?    @relation(fields: [authorId], references: [id], onDelete: SetNull)
  tasks     Task[]   @relation()
}

model Folder {
  id        BigInt   @id @default(autoincrement())
  name      String
  versionId BigInt
  parentId  BigInt? // Self-reference for nested folders
  depth     Int      @default(0) // Track nesting level for efficient queries
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)

  // Relations
  version    Version  @relation(fields: [versionId], references: [id], onDelete: Cascade)
  parent     Folder?  @relation("FolderToFolder", fields: [parentId], references: [id], onDelete: Cascade)
  subFolders Folder[] @relation("FolderToFolder")
  files      File[]   @relation("FolderToFile")
  tasks      Task[]   @relation()

  // Indexes
  @@index([versionId])
  @@index([parentId])
  @@index([depth])
}

model File {
  id        BigInt   @id @default(autoincrement())
  name      String
  versionId BigInt
  folderId  BigInt? // Optional reference to parent folder
  s3Path    String
  fileType  String
  fileSize  BigInt
  metadata  Json?
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  version   Version  @relation(fields: [versionId], references: [id], onDelete: Cascade)
  folder    Folder?  @relation("FolderToFile", fields: [folderId], references: [id], onDelete: Cascade)
  tasks     Task[]   @relation()

  @@index([versionId])
  @@index([folderId])
}

model Comment {
  id        BigInt   @id @default(autoincrement())
  authorId  BigInt
  projectId BigInt
  content   String
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model Task {
  id          BigInt    @id @default(autoincrement())
  projectId   BigInt
  authorId    BigInt?
  assigneeId  BigInt?
  versionId   BigInt?
  folderId    BigInt?
  fileId      BigInt?
  title       String
  description String?
  category    String?
  status      String?
  priority    String?
  dueDate     DateTime?
  createdAt   DateTime  @default(now()) @db.Timestamp(6)
  updatedAt   DateTime  @default(now()) @db.Timestamp(6)
  assignee    User?     @relation("TaskAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  author      User?     @relation("TaskAuthor", fields: [authorId], references: [id], onDelete: SetNull)
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  version     Version?  @relation(fields: [versionId], references: [id], onDelete: SetNull)
  folder      Folder?   @relation(fields: [folderId], references: [id], onDelete: SetNull)
  file        File?     @relation(fields: [fileId], references: [id], onDelete: SetNull)
}

model ProjectInvite {
  id        BigInt   @id @default(autoincrement())
  projectId BigInt
  userId    BigInt // invitee
  status    String // ["pending", "accepted", "declined"]
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AuthSession {
  id        BigInt   @id @default(autoincrement())
  key       String   @unique
  value     String   @db.Text
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
}

model Leaflet {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  name      String
  s3Path    String
  featured  Boolean  @default(false)
  metadata  Json?
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Conversation {
  id          String   @id @default(uuid())
  authorId    BigInt
  createdAt   DateTime @default(now()) @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @db.Timestamp(6)
  name        String
  description String?
  projectId   BigInt?
  project     Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)

  author       User                      @relation("ConversationAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  participants ConversationParticipant[]
  messages     Message[]
}

model ConversationParticipant {
  id             String   @id @default(uuid())
  conversationId String
  userId         BigInt
  joinedAt       DateTime @default(now()) @db.Timestamp(6)
  lastSeenAt     DateTime @default(now()) @db.Timestamp(6)
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User         @relation("UserConversations", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
}

model Message {
  id             String   @id @default(uuid())
  content        String
  conversationId String
  senderId       BigInt
  createdAt      DateTime @default(now()) @db.Timestamp(6)
  updatedAt      DateTime @updatedAt @db.Timestamp(6)

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User         @relation("UserMessages", fields: [senderId], references: [id], onDelete: Cascade)
}

// src/apis/supabase/index.ts
import { createClient } from "@supabase/supabase-js";
import { configDotenv } from "dotenv";
import { DatabaseSessionStorage } from "../../utils/databaseSessionStorage";

configDotenv();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error("Missing Supabase credentials.");
}

// Create database session storage adapter
const sessionStorage = new DatabaseSessionStorage();

// Create a function to get a client for a specific user
export function getSupabaseClient(userId?: string) {
  const storageKey = userId ? `sb-auth-${userId}` : "sb-127-auth-token";

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase credentials.");
  }

  return createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: true,
      detectSessionInUrl: false,
      persistSession: true,
      storage: sessionStorage,
      storageKey: storageKey,
    },
  });
}

// Default client for unauthenticated operations
export const client = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    detectSessionInUrl: false,
    persistSession: false,
    storage: sessionStorage,
  },
});

// Export the functions that use the client
export function sbLogin(email: string, password: string) {
  return client.auth.signInWithPassword({ email, password });
}

export function sbRegister(email: string, password: string) {
  return client.auth.signUp({ email, password });
}

export function sbLogout() {
  return client.auth.signOut();
}

export async function sbGetSession() {
  return client.auth.getSession();
}

export async function sbDeleteUser(sbid: string) {
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase service key/url.");
  }

  const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: true,
      detectSessionInUrl: false,
      persistSession: false,
    },
  });

  try {
    const { error } = await serviceClient.auth.admin.deleteUser(sbid);
    if (error) {
      console.error("Error deleting user from Supabase:", error.message);
    }
  } catch (error) {
    console.error("Error deleting user from Supabase:", error);
  }
}

export async function sbRefreshSession(userId?: string, refreshToken?: string) {
  const supabase = userId ? getSupabaseClient(userId) : client;

  if (refreshToken) {
    return supabase.auth.refreshSession({ refresh_token: refreshToken });
  }

  // Default behavior - try to refresh with existing session
  return supabase.auth.refreshSession();
}

export async function sbSetSession(
  userId: string,
  session: {
    access_token: string;
    refresh_token: string;
  }
) {
  const userClient = getSupabaseClient(userId);
  return userClient.auth.setSession(session);
}

// Helper function to extract user ID from JWT
export function getUserIdFromToken(token: string): string | null {
  try {
    // More robust token parsing
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      Buffer.from(base64, "base64")
        .toString()
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );

    const payload = JSON.parse(jsonPayload);
    return payload.sub || null;
  } catch (e) {
    console.error("Error extracting user ID from token:", e);
    return null;
  }
}

// src/controllers/auth.ts
import { Request } from "express";
import rateLimit from "express-rate-limit";
import { prisma } from "..";
import {
  client,
  getSupabaseClient,
  getUserIdFromToken,
  sbLogin,
  sbLogout,
  sbRegister,
} from "../apis/supabase";
import { respond } from "../middleware/response.handler";

// Rate limiting for login attempts
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === "development" ? 1000 : 5, // 5 attempts per IP
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res.status(429).json({
      error: "Too many login attempts",
      message: "Please try again later",
    });
  },
});

// Rate limiting for registration attempts
export const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: process.env.NODE_ENV === "development" ? 1000 : 3, // 3 registration attempts per IP per hour
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res.status(429).json({
      error: "Too many registration attempts",
      message: "Please try again later",
    });
  },
});

// Apply this middleware to login routes

export const login = async (req: Request) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return respond.badRequest("Email and password are required");
    }

    // Initial authentication with the default client
    const { data, error } = await sbLogin(email, password);
    const user = data?.user;
    const session = data?.session;

    if (!user || !session) {
      return respond.unauthorized("Invalid credentials");
    }

    if (error) {
      return respond.error(error);
    }

    // Find user in database
    const dbUser = await prisma.user.findUnique({
      where: {
        sbid: user.id,
      },
    });

    if (!dbUser) {
      return respond.unauthorized("User not found");
    }

    // Create a user-specific client and transfer the session
    const userClient = getSupabaseClient(user.id);

    // Store the session in the user-specific storage
    await userClient.auth.setSession({
      access_token: session.access_token,
      refresh_token: session.refresh_token,
    });

    // Set HTTP-only cookie with the token
    req.res?.cookie("auth_token", session.access_token, {
      httpOnly: true,
      secure: true,
      sameSite: "none",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Also set the refresh token
    req.res?.cookie("refresh_token", session.refresh_token, {
      httpOnly: true,
      secure: true,
      sameSite: "none",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Return successful response with user data
    return respond.with({
      user: { ...user, ...dbUser },
      id: Number(dbUser.id),
    });
  } catch (error: any) {
    return respond.error(error);
  }
};

export const logout = async (req: Request) => {
  try {
    // Get user ID from request
    const userId = req.userId;

    if (userId) {
      // Get the user-specific client
      const userClient = getSupabaseClient(userId);

      // Sign out from Supabase
      await userClient.auth.signOut();

      // Remove the session from database directly
      const storageKey = `sb-auth-${userId}`;
      await prisma.authSession.deleteMany({
        where: {
          key: {
            startsWith: storageKey,
          },
        },
      });
    } else {
      // If no user ID, just use the default client
      await sbLogout();
    }

    // Clear the auth cookie
    req.res?.clearCookie("auth_token", {
      httpOnly: true,
      secure: true,
      sameSite: "none",
    });

    // Clear CSRF token cookie if it exists
    req.res?.clearCookie("csrf_token", {
      httpOnly: true,
      secure: true,
      sameSite: "none",
    });

    return respond.message("Logged out successfully");
  } catch (error: any) {
    return respond.error(error);
  }
};

export const register = async (req: Request) => {
  try {
    const {
      email,
      password,
      termsPrivacyTime,
      termsPrivacyContent,
      ndaTime,
      ndaContent,
    } = req.body;

    if (!email || !password) {
      return respond.badRequest("Email and password are required");
    }

    const { data, error } = await sbRegister(email, password);
    const user = data?.user;

    if (!user) {
      console.error("Error registering user:", error);
      return respond.unauthorized("Invalid credentials");
    }

    if (error) {
      console.error("Error registering user:", error);
      return respond.error(error);
    }

    let name = email.split("@")[0].toLowerCase();

    let existingUser = await prisma.user.findUnique({
      where: {
        name,
      },
    });

    // If exists, increment name
    if (existingUser) {
      let i = 1;
      while (existingUser) {
        name = `${name}-${i}`;
        existingUser = await prisma.user.findUnique({
          where: {
            name,
          },
        });
        i++;
      }
    }

    // Create user in database
    const dbUser = await prisma.user.create({
      data: {
        sbid: user.id,
        name,
        email,
        role: "user",
        termsPrivacyTime,
        termsPrivacyContent,
        ndaTime,
        ndaContent,
      },
    });

    // If we have a session, store it in the user-specific client
    if (data.session) {
      const userClient = getSupabaseClient(user.id);

      // Store the session in the user-specific storage
      await userClient.auth.setSession({
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
      });

      // Set HTTP-only cookie with the token
      req.res?.cookie("auth_token", data.session.access_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });
    }

    return respond.with({
      user: { ...user, ...dbUser },
      id: Number(dbUser.id),
    });
  } catch (error: any) {
    return respond.error(error);
  }
};

export const refreshToken = async (req: Request) => {
  try {
    // Get the refresh token from the cookie
    const refreshToken = req.cookies?.refresh_token;

    // If no refresh token is provided, check for it in the request body
    const bodyRefreshToken = req.body?.refresh_token;

    const tokenToUse = refreshToken || bodyRefreshToken;

    if (!tokenToUse) {
      console.log("No refresh token provided");
      return respond.unauthorized("No refresh token provided");
    }

    console.log(
      "Attempting to refresh with token:",
      tokenToUse.substring(0, 5) + "..."
    );

    // Try to use the refresh token directly with the default client
    try {
      const { data, error } = await client.auth.refreshSession({
        refresh_token: tokenToUse,
      });

      if (error) {
        console.error("Refresh error:", error);
        return respond.unauthorized("Failed to refresh session");
      }

      if (!data.session) {
        console.error("No session returned after refresh");
        return respond.unauthorized("Failed to refresh session");
      }

      // Get user ID from the new access token
      const userId = getUserIdFromToken(data.session.access_token);
      console.log("Refreshed session for user:", userId);

      // Update the session in user-specific storage to keep it alive
      if (userId) {
        try {
          const userClient = getSupabaseClient(userId);
          await userClient.auth.setSession({
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
          });
          console.log("Updated session storage for user:", userId);
        } catch (sessionError) {
          console.error("Error updating session storage:", sessionError);
        }
      }

      // Set the new tokens in cookies
      req.res?.cookie("auth_token", data.session.access_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      req.res?.cookie("refresh_token", data.session.refresh_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      return respond.with({
        message: "Session refreshed",
        data: {
          user: data.session.user,
        },
      });
    } catch (refreshError) {
      console.error("Error during refresh:", refreshError);
      return respond.unauthorized("Failed to refresh session");
    }
  } catch (error: any) {
    console.error("Refresh exception:", error);
    return respond.error(error);
  }
};

import { prisma, ValidatedRequest } from "@/.";
import { respond } from "@/middleware/response.handler";
import {
  createConversationSchema,
  createMessageSchema,
  deleteMessageSchema,
  getConversationSchema,
  getMessageSchema,
  updateConversationSchema,
  updateMessageSchema,
} from "@/validation/chat";
import { getIO } from "@/websocket";
import { z } from "zod";

type CreateConversationRequest = z.infer<typeof createConversationSchema>;

const createConversation = async (
  req: ValidatedRequest<CreateConversationRequest>
) => {
  try {
    const { name, description, projectId, participantIds, fromProfile } =
      req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.notFound("User not found");
    }

    // If fromProfile is true, check if a private conversation already exists
    if (fromProfile && participantIds && participantIds.length === 1) {
      const otherParticipantId = BigInt(
        participantIds.filter((id) => id !== Number(author.id))[0]
      );

      const existingConversation = await prisma.conversation.findFirst({
        where: {
          projectId: null, // Private conversations should not have a project
          participants: {
            every: {
              userId: {
                in: [author.id, otherParticipantId],
              },
            },
          },
        },
        include: {
          participants: {
            include: {
              user: true,
            },
          },
        },
      });

      // Verify it's exactly a 2-person conversation
      if (
        existingConversation &&
        existingConversation.participants.length === 2
      ) {
        return respond.with(existingConversation);
      }
    }

    const conversation = await prisma.conversation.create({
      data: {
        name,
        description,
        projectId: projectId ? Number(projectId) : null,
        authorId: author.id,
      },
    });

    if (!conversation) {
      return respond.error("Failed to create conversation");
    }

    // Add  participants if provided (including the author)
    if (participantIds && participantIds.length > 0) {
      const participantData = participantIds.map((userId) => ({
        conversationId: conversation.id,
        userId,
      }));
      await prisma.conversationParticipant.createMany({
        data: participantData,
      });
    }

    return respond.with(conversation);
  } catch (error) {
    console.error("Error creating conversation:", error);
    return respond.error("Internal server error");
  }
};

type GetConversationRequest = z.infer<typeof getConversationSchema>;

const getConversation = async (
  req: ValidatedRequest<GetConversationRequest> & {
    query?: { page?: string; pageSize?: string };
  }
) => {
  try {
    const { id } = req.params;
    // Pagination query params: page (default 1), pageSize (default 20)

    const page = req.query?.page ? Number(req.query.page) : 1;
    const pageSize = req.query?.pageSize ? Number(req.query.pageSize) : 20;
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const conversation = await prisma.conversation.findUnique({
      where: {
        id,
      },
      include: {
        author: true,
        participants: {
          include: {
            user: true,
          },
        },
        project: true,
      },
    });

    if (!conversation) {
      return respond.notFound("Conversation not found");
    }

    // Get paginated messages for the conversation
    const [messages, totalMessages] = await Promise.all([
      prisma.message.findMany({
        where: { conversationId: id },
        orderBy: { createdAt: "desc" },
        skip,
        take,
        include: {
          sender: true, // Include sender details
        },
      }),
      prisma.message.count({
        where: { conversationId: id },
      }),
    ]);

    const reversedMessages = messages.reverse(); // Reverse to show oldest first

    return respond.with({
      ...conversation,
      messages: reversedMessages,
      messagesPagination: {
        page,
        pageSize,
        total: totalMessages,
        totalPages: Math.ceil(totalMessages / pageSize),
      },
      hasMoreMessages: totalMessages > skip + take,
    });
  } catch (error) {
    console.error("Error fetching conversation:", error);
    return respond.error("Internal server error");
  }
};

type UpdateConversationRequest = z.infer<typeof updateConversationSchema>;

const updateConversation = async (
  req: ValidatedRequest<UpdateConversationRequest>
) => {
  try {
    const { id } = req.params;
    const { name, description, participantIds, participantLastSeen } = req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Check if user is in the conversation
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });
    if (!user) {
      return respond.notFound("User not found");
    }
    const conversationParticipant =
      await prisma.conversationParticipant.findFirst({
        where: {
          conversationId: id,
          userId: user.id,
        },
      });
    if (!conversationParticipant) {
      return respond.error("User is not a participant in this conversation");
    }

    // If participantLastSeen is true, update lastSeenAt for user and return
    if (participantLastSeen) {
      await prisma.conversationParticipant.update({
        where: {
          conversationId_userId: {
            conversationId: id,
            userId: user.id,
          },
        },
        data: {
          lastSeenAt: new Date(),
        },
      });
      return respond.message("Last seen updated successfully");
    }

    const conversation = await prisma.conversation.update({
      where: {
        id,
      },
      data: {
        name,
        description,
        updatedAt: new Date(), // Update the timestamp
      },
      include: {
        messages: true,
        author: true,
        participants: {
          include: {
            user: true,
          },
        },
        project: true,
      },
    });

    if (!conversation) {
      return respond.notFound("Conversation not found");
    }

    // Update participants if provided
    if (participantIds && participantIds.length > 0) {
      // First, remove existing participants not in the new list
      const existingParticipantIds = conversation.participants.map(
        (p) => p.userId
      );
      await prisma.conversationParticipant.deleteMany({
        where: {
          conversationId: conversation.id,
          userId: {
            notIn: participantIds,
          },
        },
      });
      // Then, add new participants
      const newParticipants = participantIds.filter(
        (id) => !existingParticipantIds.includes(BigInt(id))
      );
      if (newParticipants.length > 0) {
        const participantData = newParticipants.map((userId) => ({
          conversationId: conversation.id,
          userId,
        }));
        await prisma.conversationParticipant.createMany({
          data: participantData,
        });
      }
    }

    return respond.with(conversation);
  } catch (error) {
    console.error("Error updating conversation:", error);
    return respond.error("Internal server error");
  }
};

type DeleteConversationRequest = z.infer<typeof getConversationSchema>;

const deleteConversation = async (
  req: ValidatedRequest<DeleteConversationRequest>
) => {
  try {
    const { id } = req.params;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const conversation = await prisma.conversation.delete({
      where: {
        id,
      },
    });

    if (!conversation) {
      return respond.notFound("Conversation not found");
    }

    // Check if the user is the author of the conversation
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });
    if (!user) {
      return respond.notFound("User not found");
    }
    if (conversation.authorId !== user.id) {
      return respond.error(
        "User is not authorized to delete this conversation"
      );
    }
    return respond.message("Conversation deleted successfully");
  } catch (error) {
    console.error("Error deleting conversation:", error);
    return respond.error("Internal server error");
  }
};

const getUnreadMessagesCount = async (req: ValidatedRequest<{}>) => {
  try {
    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });
    if (!user) {
      return respond.notFound("User not found");
    }
    // Get the count of unread messages for the user
    const participant = await prisma.conversationParticipant.findMany({
      where: {
        userId: user.id,
      },
      select: {
        conversation: {
          select: {
            messages: {
              select: {
                createdAt: true, // Select only the message createdAt timestamp
              },
            },
          },
        },
        lastSeenAt: true, // Include lastSeenAt to determine unread messages
      },
    });

    // Get unread count per conversation
    const unreadCount = participant.reduce((count, p) => {
      const lastSeenAt = p.lastSeenAt || new Date(0); // Default to epoch if not set
      const unreadMessages = p.conversation.messages.filter(
        (message) => message.createdAt > lastSeenAt
      );
      return count + unreadMessages.length;
    }, 0);

    return respond.with({ unreadCount });
  } catch (error) {
    console.error("Error fetching unread messages count:", error);
    return respond.error("Internal server error");
  }
};

// Export the conversation controller
const conversationController = {
  createConversation,
  getConversation,
  updateConversation,
  deleteConversation,
  getUnreadMessagesCount,
};

// Messages

type CreateMessageRequest = z.infer<typeof createMessageSchema>;

const createMessage = async (req: ValidatedRequest<CreateMessageRequest>) => {
  try {
    const { conversationId, content } = req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.notFound("User not found");
    }

    // Check if the conversation exists and the user is a participant
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: conversationId,
      },
      include: {
        participants: {
          where: {
            userId: author.id, // Ensure the author is a participant
          },
        },
      },
    });
    if (!conversation) {
      return respond.notFound("Conversation not found");
    }

    const message = await prisma.message.create({
      data: {
        content,
        conversationId: conversationId,
        senderId: author.id, // Use senderId to track who sent the message
      },
      include: {
        conversation: true, // Include conversation details
        sender: true, // Include sender details
      },
    });

    if (!message) {
      return respond.error("Failed to create message");
    }

    await prisma.conversation.update({
      where: {
        id: conversationId,
      },
      data: {
        updatedAt: new Date(), // Update the timestamp of the conversation
      },
    });

    const io = getIO();
    if (io) {
      // Emit the new message to the participants of the conversation
      const conversationId = message.conversationId;
      const conversation = await prisma.conversation.findUnique({
        where: {
          id: conversationId,
        },
        include: {
          participants: {
            include: {
              user: true, // Include user details for participants
            },
          },
        },
      });
      if (!conversation) {
        return respond.notFound("Conversation not found");
      }
      // Emit to the specific conversation room
      conversation.participants.forEach((participant) => {
        const participantId = Number(participant.user.id);
        if (participantId !== Number(req.user?.id)) {
          io.to(String(participantId)).emit("message", { conversationId });
        }
      });

      // Update the last seen timestamp for the author in the conversation
      await prisma.conversationParticipant.update({
        where: {
          conversationId_userId: {
            conversationId: conversationId,
            userId: author.id,
          },
        },
        data: {
          lastSeenAt: new Date(), // Update the last seen timestamp
        },
      });
    }
    return respond.with(message);
  } catch (error) {
    console.error("Error creating message:", error);
    return respond.error("Internal server error");
  }
};

type GetMessageRequest = z.infer<typeof getMessageSchema>;

const getMessage = async (req: ValidatedRequest<GetMessageRequest>) => {
  try {
    const { id } = req.params;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const message = await prisma.message.findUnique({
      where: {
        id,
      },
      include: {
        conversation: true, // Include conversation details
        sender: true, // Include sender details
      },
    });

    if (!message) {
      return respond.notFound("Message not found");
    }
    return respond.with(message);
  } catch (error) {
    console.error("Error fetching message:", error);
    return respond.error("Internal server error");
  }
};

type UpdateMessageRequest = z.infer<typeof updateMessageSchema>;

const updateMessage = async (req: ValidatedRequest<UpdateMessageRequest>) => {
  try {
    const { id } = req.params;
    const { content } = req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const message = await prisma.message.update({
      where: {
        id,
      },
      data: {
        content,
        updatedAt: new Date(), // Update the timestamp
      },
      include: {
        conversation: true, // Include conversation details
        sender: true, // Include sender details
      },
    });

    if (!message) {
      return respond.notFound("Message not found");
    }
    return respond.with(message);
  } catch (error) {
    console.error("Error updating message:", error);
    return respond.error("Internal server error");
  }
};

type DeleteMessageRequest = z.infer<typeof deleteMessageSchema>;

const deleteMessage = async (req: ValidatedRequest<DeleteMessageRequest>) => {
  try {
    const { id } = req.params;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      return respond.unauthorized("User not authenticated");
    }

    const message = await prisma.message.delete({
      where: {
        id,
      },
    });

    if (!message) {
      return respond.notFound("Message not found");
    }
    return respond.message("Message deleted successfully");
  } catch (error) {
    console.error("Error deleting message:", error);
    return respond.error("Internal server error");
  }
};

const messageController = {
  createMessage,
  getMessage,
  updateMessage,
  deleteMessage,
};

export const chatController = {
  conversationController,
  messageController,
};

import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { respond } from "../middleware/response.handler";
import {
  createCommentSchema,
  deleteCommentSchema,
  getCommentSchema,
  updateCommentSchema,
} from "../validation/comment";
import { getIO } from "../websocket";

type CreateCommentRequest = z.infer<typeof createCommentSchema>;

const createComment = async (req: ValidatedRequest<CreateCommentRequest>) => {
  try {
    const { content, projectId } = req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      // User is not authenticated
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.error("User not found");
    }

    // Check if the project exists
    const project = await prisma.project.findUnique({
      where: {
        id: Number(projectId),
      },
    });
    if (!project) {
      return respond.notFound("Project not found");
    }
    // Check if the user is a member of the project
    const isMember = await prisma.projectUser.findUnique({
      where: {
        projectId_userId: {
          projectId: Number(projectId),
          userId: author.id,
        },
      },
    });

    if (!isMember) {
      return respond.error("User is not a member of the project");
    }

    const comment = await prisma.comment.create({
      data: {
        content,
        projectId,
        authorId: author?.id,
      },
    });

    const io = getIO();
    if (io) {
      const sanitizedProjectId = Number(projectId);
      // Emit a comment event to the project room
      io.to(`project_${sanitizedProjectId}`).emit("comment", {
        projectId: sanitizedProjectId,
      });
    }
    return respond.with(comment);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to create comment");
  }
};

type GetCommentRequest = z.infer<typeof getCommentSchema>;

const getComment = async (req: ValidatedRequest<GetCommentRequest>) => {
  try {
    const { id } = req.params;

    // Check if the comment exists
    const comment = await prisma.comment.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        author: true,
      },
    });

    if (!comment) {
      return respond.notFound("Comment not found");
    }

    return respond.with(comment);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve comment");
  }
};

type UpdateCommentRequest = z.infer<typeof updateCommentSchema>;

const updateComment = async (req: ValidatedRequest<UpdateCommentRequest>) => {
  try {
    const { id } = req.params;
    const { content } = req.body;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      // User is not authenticated
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.error("User not found");
    }

    // Check if the comment exists and belongs to the user
    const comment = await prisma.comment.findUnique({
      where: {
        id: Number(id),
        authorId: author.id, // Ensure the comment belongs to the user
      },
    });

    if (!comment) {
      return respond.notFound("Comment not found");
    }

    const updatedComment = await prisma.comment.update({
      where: {
        id: Number(id),
      },
      data: {
        content: content ?? comment.content,
        updatedAt: new Date(), // Update the timestamp
      },
    });

    const io = getIO();
    if (io) {
      const sanitizedProjectId = Number(comment.projectId);
      // Emit a comment event to the project room
      io.to(`project_${sanitizedProjectId}`).emit("comment", {
        projectId: sanitizedProjectId,
      });
    }

    return respond.with(updatedComment);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update comment");
  }
};

type DeleteCommentRequest = z.infer<typeof deleteCommentSchema>;

const deleteComment = async (req: ValidatedRequest<DeleteCommentRequest>) => {
  try {
    const { id } = req.params;

    // Check if the user is authenticated
    if (!req.user || !req.user.id) {
      // User is not authenticated
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.error("User not found");
    }

    // Check if the comment exists and belongs to the user
    const comment = await prisma.comment.findUnique({
      where: {
        id: Number(id),
        authorId: author.id, // Ensure the comment belongs to the user
      },
    });

    if (!comment) {
      return respond.notFound("Comment not found");
    }

    await prisma.comment.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.with("Comment deleted successfully");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete comment");
  }
};

export const commentController = {
  createComment,
  getComment,
  updateComment,
  deleteComment,
};

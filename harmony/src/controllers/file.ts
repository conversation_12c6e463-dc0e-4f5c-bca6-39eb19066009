// harmony/src/controllers/file.ts

import * as fs from "fs";
import * as tus from "tus-js-client";
import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { client as sbClient } from "../apis/supabase";
import { respond } from "../middleware/response.handler";
import { decodePath, encodePath } from "../utils/pathEncoding";
import { getWaveformFromUrl } from "../utils/waveform";
import {
  createFileSchema,
  deleteFileSchema,
  getFileContentsSchema,
  getFileSchema,
  updateFileSchema,
} from "../validation/file";
import { getIO } from "../websocket";

type CreateFileRequest = z.infer<typeof createFileSchema>;

// Logic for creating a file
const createFile = async (req: ValidatedRequest<CreateFileRequest>) => {
  try {
    const {
      name,
      versionId,
      folderId,
      s3Path,
      fileType,
      fileSize,
      metadata,
      independent,
    } = req.body;

    // Perform user, version, and permission checks in parallel
    const [user, version] = await Promise.all([
      prisma.user.findUnique({
        where: { sbid: req.user?.id },
        select: { id: true }, // Only select needed fields
      }),
      prisma.version.findUnique({
        where: { id: Number(versionId) },
        select: { projectId: true, number: true }, // Only select needed fields
      }),
    ]);

    // Early validation checks
    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    if (!version) {
      return respond.notFound("Version not found");
    }

    // Check project permissions and folder in parallel if needed
    const queries = [
      prisma.projectUser.findFirst({
        where: {
          projectId: version.projectId,
          userId: Number(user.id),
        },
        select: { id: true }, // Only select needed fields
      }) as any,
    ];

    // Only query for folder if folderId is provided
    if (folderId) {
      queries.push(
        prisma.folder.findUnique({
          where: {
            id: Number(folderId),
            versionId: Number(versionId),
          },
          select: { id: true, name: true, parentId: true }, // Only select needed fields
        }) as any // Type assertion to avoid type mismatch in Promise.all
      );
    }

    // Execute permission check and optional folder check in parallel
    const [projectUser, folder] = await Promise.all(queries);

    // Validate project permissions
    if (!projectUser) {
      return respond.unauthorized(
        "User cannot create files in this project, as they are not part of this project."
      );
    }

    // Validate folder if folderId was provided
    if (folderId && !folder) {
      return respond.notFound(
        "Folder not found or does not belong to this version."
      );
    }

    // Handle file upload if a file is present
    let resS3Path = "";
    let resFileSize = 0;
    let uploadedFileInfo = null;

    if (req.file) {
      // Generate a unique path for the file
      const uniqueFilename = `${req.file.originalname}`;

      if (s3Path) {
        resS3Path = encodePath(s3Path);
      } else {
        if (independent && folderId && folder) {
          let createdS3Path = "";
          let currentFolder = folder;
          while (currentFolder) {
            createdS3Path = `${currentFolder.name}/${createdS3Path}`;
            if (currentFolder.parentId) {
              const newFolder = await prisma.folder.findUnique({
                where: { id: Number(currentFolder.parentId) },
              });
              if (!newFolder) return respond.notFound("Folder not found");
              currentFolder = newFolder;
            } else {
              break;
            }
          }
          createdS3Path = createdS3Path.slice(0, -1); // Removes trailing slash
          resS3Path = encodePath(
            `projects/${version.projectId}/versions/${version.number}/${createdS3Path}/${uniqueFilename}`
          );
        } else {
          resS3Path = encodePath(
            `projects/${version.projectId}/versions/${version.number}/${uniqueFilename}`
          );
        }
      }

      // Use the helper function to upload the file
      try {
        uploadedFileInfo = await uploadLargeFile(
          "projects",
          resS3Path,
          req.file,
          Number(user.id).toString() // Pass the user ID
        );
        resFileSize = req.file.size;
      } catch (error: Error | any) {
        return respond.error(`File upload failed: ${error.message}`);
      }
    } else {
      // Handle case where no file is uploaded
      return respond.badRequest("No file uploaded");
    }

    const newCreatedFile = await prisma.file.create({
      data: {
        name,
        versionId,
        folderId: folderId ? Number(folderId) : null,
        s3Path: resS3Path || s3Path, // Use the uploaded path or fallback to provided s3Path
        fileType: fileType || req.file?.mimetype || "application/octet-stream", // Fallback to mimetype if not provided
        fileSize: resFileSize || fileSize || req.file?.size || 0, // Use the uploaded file size or fallback to provided fileSize
        metadata,
      },
      include: {
        version: true,
        folder: true,
      },
    });

    await prisma.project.update({
      where: { id: Number(version.projectId) },
      data: {
        updatedAt: new Date(),
      },
    });

    return respond.with(newCreatedFile);
  } catch (error) {
    console.error("File creation error:", error);
    if (error instanceof Error) {
      return respond.error(`Failed to create file: ${error.message}`);
    }
    return respond.error("Failed to create file: Unknown error");
  }
};

type GetFileRequest = z.infer<typeof getFileSchema>;

const getFile = async (req: ValidatedRequest<GetFileRequest>) => {
  try {
    const { id } = req.params;

    const file = await prisma.file.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        version: true,
        folder: true,
      },
    });

    if (!file) {
      return respond.notFound("File not found");
    }

    return respond.with(file);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve file");
  }
};

type GetFileContentsRequest = z.infer<typeof getFileContentsSchema>;

const getFileContents = async (
  req: ValidatedRequest<GetFileContentsRequest>
) => {
  try {
    const { id } = req.params;

    const file = await prisma.file.findUnique({
      where: { id: Number(id) },
    });

    if (!file) {
      return respond.notFound("File not found");
    }

    // Create signed URL with longer expiry
    const { data, error } = await sbClient.storage
      .from("projects")
      .createSignedUrl(file.s3Path, 3600); // 1 hour expiry

    if (error) {
      console.error("Error creating signed URL:", error.message);
      return respond.error(`Failed to access file: ${error.message}`);
    }

    // Calculate waveform from the signed URL (assuming it's a WAV file)
    let waveform: number[] = [];
    try {
      waveform = await getWaveformFromUrl(data.signedUrl, 200);
    } catch (err) {
      console.error("Waveform calculation failed:", err);
      // fallback: empty array
      waveform = [];
    }

    // Return the signed URL, file info, and waveform
    return respond.with({
      signedUrl: data.signedUrl,
      fileName: decodePath(file.name),
      fileType: file.fileType,
      waveform,
    });
  } catch (error) {
    console.error("Error getting file contents:", error);
    return respond.error("Failed to get file contents");
  }
};

type UpdateFileRequest = z.infer<typeof updateFileSchema>;

const updateFile = async (req: ValidatedRequest<UpdateFileRequest>) => {
  try {
    const { id } = req.params;
    const { name, fileType, metadata, folderId } = req.body;

    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const originalFile = await prisma.file.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!originalFile) {
      return respond.notFound("Original file not found.");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(originalFile.versionId) },
    });

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: version?.projectId,
        userId: user.id,
      },
    });

    if (!projectUser) {
      return respond.unauthorized(
        "User cannot update files in this project, as they are not part of this project."
      );
    }

    const newUpdatedFile = await prisma.file.update({
      where: {
        id: Number(id),
      },
      data: {
        name,
        fileType,
        metadata,
        folderId,
        updatedAt: new Date(),
      },
    });

    return respond.with(newUpdatedFile);
  } catch (error) {
    // Handle errors
    console.error(error);
    return respond.error("Failed to update file");
  }
};

type DeleteFileRequest = z.infer<typeof deleteFileSchema>;

const deleteFile = async (req: ValidatedRequest<DeleteFileRequest>) => {
  try {
    const { id } = req.params;
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });
    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const file = await prisma.file.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!file) {
      return respond.notFound("File not found");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(file.versionId) },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const project = await prisma.project.findUnique({
      where: {
        id: version.projectId,
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: project.id,
        userId: user.id,
      },
    });
    if (!projectUser) {
      return respond.unauthorized(
        "User cannot delete files in this project, as they are not part of this project."
      );
    }

    // Delete the file from Supabase Storage if it exists
    const { error: deleteError } = await sbClient.storage
      .from("projects")
      .remove([encodePath(file.s3Path)]);
    if (deleteError) {
      console.error(
        "Error deleting file from Supabase Storage:",
        deleteError.message
      );
      return respond.error(
        `Failed to delete file from storage: ${deleteError.message}`
      );
    }

    await prisma.file.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.message("File deleted successfully");
  } catch (error) {
    // Handle errors
    console.error(error);
    return respond.error("Failed to delete file");
  }
};

export async function uploadLargeFile(
  bucketName: string,
  path: string,
  file: Express.Multer.File,
  userId: string
) {
  return new Promise(async (resolve, reject) => {
    try {
      // Emit start event immediately
      const io = getIO();
      const uploadId = `${userId}_${Date.now()}`;

      // Start preparing the upload in parallel with other operations
      let fileSource;
      let fileBuffer;

      // Emit start event immediately - don't wait for any processing
      io.to(userId).emit("upload-progress", {
        id: uploadId,
        fileName: file.originalname,
        progress: 0,
        status: "started",
        bytesTotal: file.size,
      });

      // Get session token in parallel with file preparation
      const sessionPromise = (async () => {
        // Use cached token if available to avoid auth calls
        if ((global as any).supabaseSessionCache?.accessToken) {
          const cachedToken = (global as any).supabaseSessionCache.accessToken;
          const timestamp = (global as any).supabaseSessionCache.timestamp;
          // Use cached token if it's less than 50 minutes old (tokens last 60 min)
          if (Date.now() - timestamp < 50 * 60 * 1000) {
            return cachedToken;
          }
        }

        const {
          data: { session },
        } = await sbClient.auth.getSession();
        const token = session?.access_token;

        // Cache the token for future uploads
        (global as any).supabaseSessionCache = {
          accessToken: token,
          timestamp: Date.now(),
        };

        return token;
      })();

      // For small files, use direct upload without chunking
      if (file.size < 10 * 1024 * 1024) {
        // 10MB threshold
        // Prepare file data - use buffer if available, otherwise read from disk
        const fileData = file.buffer || fs.readFileSync(file.path);

        // Get session token (from parallel promise)
        const sessionToken = await sessionPromise;

        // Use direct upload for small files
        const { data, error } = await sbClient.storage
          .from(bucketName)
          .upload(path, fileData, {
            contentType: file.mimetype || "application/octet-stream",
            upsert: true,
          });

        if (error) {
          io.to(userId).emit("upload-progress", {
            id: uploadId,
            fileName: file.originalname,
            progress: 0,
            status: "error",
            error: error.message,
          });
          return reject(error);
        }

        const publicUrl = sbClient.storage.from(bucketName).getPublicUrl(path);
        io.to(userId).emit("upload-progress", {
          id: uploadId,
          fileName: file.originalname,
          progress: 100,
          status: "completed",
          url: publicUrl.data.publicUrl,
        });
        return resolve(publicUrl.data.publicUrl);
      }

      // For larger files, prepare the stream in parallel with auth
      if (file.path && file.path !== "" && fs.existsSync(file.path)) {
        // Use a larger buffer size for reading from disk
        fileSource = fs.createReadStream(file.path, {
          highWaterMark: 2 * 1024 * 1024, // 2MB buffer size for faster reads
        });
      } else {
        const { Readable } = require("stream");
        fileSource = new Readable({
          highWaterMark: 2 * 1024 * 1024, // 2MB buffer size
        });
        fileSource.push(file.buffer);
        fileSource.push(null);
      }

      // Get session token (from parallel promise)
      const sessionToken = await sessionPromise;

      // Optimize tus upload configuration for Supabase
      const upload = new tus.Upload(fileSource, {
        endpoint: `${process.env.SUPABASE_URL}/storage/v1/upload/resumable`,
        retryDelays: [0, 1000, 3000, 5000], // Progressive retry delays
        headers: {
          authorization: `Bearer ${sessionToken}`,
          "x-upsert": "true",
        },
        uploadDataDuringCreation: true, // Start uploading immediately during creation
        removeFingerprintOnSuccess: true,
        metadata: {
          bucketName: bucketName,
          objectName: path,
          contentType: file.mimetype || "application/octet-stream",
          cacheControl: "3600",
        },
        uploadSize: file.size,
        chunkSize: 20 * 1024 * 1024, // Optimal chunk size for Supabase (20MB)
        storeFingerprintForResuming: true, // Enable resumable uploads
        onError: function (error: Error) {
          console.error("Upload failed:", error);
          io.to(userId).emit("upload-progress", {
            id: uploadId,
            fileName: file.originalname,
            progress: 0,
            status: "error",
            error: error.message,
          });
          reject(error);
        },
        onProgress: function (bytesUploaded: number, bytesTotal: number) {
          const percentage = Math.floor((bytesUploaded / bytesTotal) * 100);
          // Only emit progress updates at 5% intervals to reduce overhead
          if (percentage % 5 === 0 || percentage === 100) {
            io.to(userId).emit("upload-progress", {
              id: uploadId,
              fileName: file.originalname,
              progress: percentage,
              status: "uploading",
              bytesUploaded,
              bytesTotal,
            });
          }
        },
        onSuccess: function () {
          const publicUrl = sbClient.storage
            .from(bucketName)
            .getPublicUrl(path);
          io.to(userId).emit("upload-progress", {
            id: uploadId,
            fileName: file.originalname,
            progress: 100,
            status: "completed",
            url: publicUrl.data.publicUrl,
          });
          resolve(publicUrl.data.publicUrl);
        },
      });

      // Start upload immediately
      upload.start();
    } catch (error) {
      console.error("Error during upload:", error);
      reject(error);
    }
  });
}

export const fileController = {
  createFile,
  getFile,
  getFileContents,
  updateFile,
  deleteFile,
};

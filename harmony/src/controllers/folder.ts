// harmony/src/controllers/folder.ts
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import { Readable } from "stream";
import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { client as sbClient } from "../apis/supabase";
import { respond } from "../middleware/response.handler";
import { encodePath } from "../utils/pathEncoding";
import {
  createFolderSchema,
  deleteFolderSchema,
  getFolderAncestorsSchema,
  getFolderContentsSchema,
  getFolderSchema,
  updateFolderSchema,
  uploadFolderSchema,
} from "../validation/folder";
import { fileController } from "./file";

type CreateFolderRequest = z.infer<typeof createFolderSchema>;
const createFolder = async (req: ValidatedRequest<CreateFolderRequest>) => {
  try {
    const { name, versionId, parentId } = req.body;

    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(versionId) },
      include: {
        project: true,
      },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: version.projectId,
        userId: Number(user.id),
      },
    });

    if (!projectUser) {
      return respond.unauthorized(
        "User cannot create folders in this project, as they are not part of this project."
      );
    }

    let depth = 0;
    if (parentId) {
      const parentFolder = await prisma.folder.findUnique({
        where: { id: Number(parentId) },
      });

      if (!parentFolder) {
        return respond.notFound("Parent folder not found");
      }

      depth = parentFolder.depth + 1;
    }

    const newFolder = await prisma.folder.create({
      data: {
        name,
        versionId,
        parentId,
        depth,
      },
      include: {
        version: true,
        parent: true,
      },
    });

    return respond.with(newFolder);
  } catch (error) {
    console.error("Folder creation error:", error);
    if (error instanceof Error) {
      return respond.error(`Failed to create folder: ${error.message}`);
    }
    return respond.error("Failed to create folder: Unknown error");
  }
};

type UploadFolderRequest = z.infer<typeof uploadFolderSchema>;

const uploadFolder = async (req: ValidatedRequest<UploadFolderRequest>) => {
  try {
    const { name, versionId, parentId } = req.body;
    const files = req.files as Express.Multer.File[];
    const paths = req.body.paths as string[];

    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });
    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(versionId) },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: version.projectId,
        userId: user.id,
      },
    });
    if (!projectUser) {
      return respond.unauthorized(
        "This user is not allowed to upload folders to this project."
      );
    }

    // Create root folder - only look for existing folder if there's a parentId
    let rootFolder = parentId
      ? await prisma.folder.findFirst({
          where: {
            name,
            versionId,
            parentId,
          },
        })
      : null;

    if (!rootFolder) {
      // If parentId given, calculate the depth
      let depth = 0;
      if (parentId) {
        const parentFolder = await prisma.folder.findUnique({
          where: { id: Number(parentId) },
        });

        if (!parentFolder) {
          return respond.notFound("Parent folder not found");
        }

        depth = parentFolder.depth + 1;
      }

      rootFolder = await prisma.folder.create({
        data: {
          name,
          versionId,
          depth,
          parentId,
        },
      });
    }

    // If there is a parent id, create a preceding s3 path
    let precedingS3Path = "";
    let currentFolder = rootFolder;
    while (currentFolder) {
      precedingS3Path =
        currentFolder.name + (precedingS3Path ? `/${precedingS3Path}` : "");
      if (currentFolder.parentId) {
        const newFolder = await prisma.folder.findUnique({
          where: { id: Number(currentFolder.parentId) },
        });
        if (!newFolder) return respond.notFound("Folder not found");
        currentFolder = newFolder;
      } else {
        break;
      }
    }

    // Create a folder cache with mutex locks
    const folderCache = new Map<string, any>();
    const folderLocks = new Map<string, Promise<void>>();
    folderCache.set("", rootFolder);

    // Function to create or get folder with locking mechanism
    const getOrCreateFolder = async (
      folderName: string,
      parentFolder: any,
      currentPath: string
    ) => {
      // Wait for any existing lock on this path
      const existingLock = folderLocks.get(currentPath);
      if (existingLock) {
        await existingLock;
      }

      // Create a new lock
      let lockResolve: () => void;
      const lockPromise = new Promise<void>((resolve) => {
        lockResolve = resolve;
      });
      folderLocks.set(currentPath, lockPromise);

      try {
        // Check cache first
        let subfolder = folderCache.get(currentPath);
        if (subfolder) {
          return subfolder;
        }

        // Find existing folder
        subfolder = await prisma.folder.findFirst({
          where: {
            name: folderName,
            versionId,
            parentId: parentFolder.id,
          },
        });

        if (!subfolder) {
          // Create new folder if it doesn't exist
          subfolder = await prisma.folder.create({
            data: {
              name: folderName,
              versionId,
              parentId: parentFolder.id,
              depth: (parentFolder.depth || 0) + 1,
            },
          });
        }

        // Update cache
        folderCache.set(currentPath, subfolder);
        return subfolder;
      } finally {
        // Release the lock
        lockResolve!();
        folderLocks.delete(currentPath);
      }
    };

    // Process files sequentially within their folder paths
    const filePromises = files.map(async (file, index) => {
      try {
        if (
          file.originalname === ".DS_Store" ||
          file.originalname.endsWith("/.DS_Store")
        ) {
          return null;
        }

        const relativePath = paths[index] || file.originalname;
        const pathParts = relativePath.split("/").slice(1);
        const fileName = pathParts.pop() || relativePath;

        let currentFolder = rootFolder;
        let currentPath = "";

        // Process each folder in the path
        for (const folderName of pathParts) {
          if (!folderName) continue;

          currentPath += (currentPath ? "/" : "") + folderName;
          currentFolder = await getOrCreateFolder(
            folderName,
            currentFolder,
            currentPath
          );
        }

        // Build the full path using folder names
        const fullPath = [];
        let tempFolder = currentFolder;

        while (tempFolder) {
          // Skip adding the root folder name if we're going to use precedingS3Path
          if (precedingS3Path && tempFolder.id === rootFolder.id) {
            break;
          }

          fullPath.unshift(tempFolder.name);
          if (tempFolder.parentId) {
            const parentFolder = folderCache.get(
              fullPath.slice(0, -1).join("/") || ""
            );
            if (parentFolder) {
              tempFolder = parentFolder;
            } else {
              const locatedFolder = await prisma.folder.findUnique({
                where: { id: tempFolder.parentId },
              });
              if (!locatedFolder) break;
              tempFolder = locatedFolder;
            }
          } else {
            break;
          }
        }

        const folderPath = fullPath.join("/");
        // Combine paths, avoiding duplication of the root folder name
        const s3Path = precedingS3Path
          ? encodePath(
              `projects/${version.projectId}/versions/${
                version.number
              }/${precedingS3Path}${
                folderPath ? `/${folderPath}` : ""
              }/${fileName}`
            )
          : encodePath(
              `projects/${version.projectId}/versions/${version.number}/${folderPath}/${fileName}`
            );

        const result = await fileController.createFile({
          body: {
            name: fileName,
            versionId,
            folderId: currentFolder.id,
            s3Path,
            fileType: file.mimetype || "application/octet-stream",
            fileSize: file.size,
            metadata: {},
          },
          user: req.user,
          file,
        } as any);

        return result.data;
      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        return null;
      }
    });

    const results = await Promise.all(filePromises);
    const uploadedFiles = results.filter(Boolean);

    return respond.with({
      folder: rootFolder,
      files: uploadedFiles,
      totalUploaded: uploadedFiles.length,
      totalFailed: results.length - uploadedFiles.length,
    });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to upload folder");
  }
};

type GetFolderRequest = z.infer<typeof getFolderSchema>;

const getFolder = async (req: ValidatedRequest<GetFolderRequest>) => {
  try {
    const { id } = req.params;

    const folder = await prisma.folder.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        version: true,
        parent: true,
        subFolders: true,
        files: true,
      },
    });

    if (!folder) {
      return respond.notFound("Folder not found");
    }

    return respond.with(folder);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve folder");
  }
};

type GetFolderAncestorsRequest = z.infer<typeof getFolderAncestorsSchema>;

// Get all ancestors of a folder
const getFolderAncestors = async (
  req: ValidatedRequest<GetFolderAncestorsRequest>
) => {
  try {
    const { id } = req.params;
    const ancestors = [];

    let currentFolder = await prisma.folder.findUnique({
      where: {
        id: Number(id),
      },
    });

    // Traverse up the tree
    while (currentFolder && currentFolder.parentId) {
      const parent = await prisma.folder.findUnique({
        where: {
          id: currentFolder.parentId,
        },
      });

      if (parent) {
        ancestors.unshift(parent); // Add to the beginning
        currentFolder = parent;
      } else {
        break;
      }
    }

    return respond.with(ancestors);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve folder ancestors");
  }
};

type UpdateFolderRequest = z.infer<typeof updateFolderSchema>;

const updateFolder = async (req: ValidatedRequest<UpdateFolderRequest>) => {
  try {
    const { id } = req.params;
    const { name, parentId } = req.body;

    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const originalFolder = await prisma.folder.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        version: true,
      },
    });

    if (!originalFolder) {
      return respond.notFound("Original folder not found.");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(originalFolder.versionId) },
      include: {
        project: true,
      },
    });

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: version?.projectId,
        userId: user.id,
      },
    });

    if (!projectUser) {
      return respond.unauthorized(
        "User cannot update folders in this project, as they are not part of this project."
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (name) updateData.name = name;

    // Update parentId and depth if needed
    if (parentId !== undefined) {
      // Prevent circular references
      if (parentId !== null) {
        const newParent = await prisma.folder.findUnique({
          where: { id: Number(parentId) },
        });

        if (!newParent) {
          return respond.notFound("New parent folder not found");
        }

        // Check for circular reference
        let checkFolder = newParent;
        while (checkFolder.parentId) {
          if (Number(checkFolder.parentId) === Number(id)) {
            return respond.error("Cannot make folder its own descendant");
          }

          const nextFolder = await prisma.folder.findUnique({
            where: { id: checkFolder.parentId },
          });

          if (!nextFolder) break;
          checkFolder = nextFolder;
        }

        updateData.parentId = Number(parentId);
        updateData.depth = newParent.depth + 1;
      } else {
        updateData.parentId = null;
        updateData.depth = 0;
      }
    }

    // Update the folder
    const updatedFolder = await prisma.folder.update({
      where: {
        id: Number(id),
      },
      data: updateData,
    });

    // If depth changed, we need to update all descendant depths
    if (
      updateData.depth !== undefined &&
      updateData.depth !== originalFolder.depth
    ) {
      const depthDifference = updateData.depth - originalFolder.depth;

      // Find all descendants
      const descendants = await findAllDescendants(Number(id));

      // Update each descendant's depth
      for (const descendant of descendants) {
        await prisma.folder.update({
          where: { id: descendant.id },
          data: { depth: descendant.depth + depthDifference },
        });
      }
    }

    return respond.with(updatedFolder);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update folder");
  }
};

// Helper function to find all descendants of a folder
async function findAllDescendants(
  folderId: number
): Promise<Array<{ id: number; depth: number }>> {
  const descendants = [];

  // Get direct children
  const children = await prisma.folder.findMany({
    where: { parentId: folderId },
  });

  // Add children to descendants
  descendants.push(...children);

  // Recursively get descendants of each child
  for (const child of children) {
    const childDescendants = await findAllDescendants(Number(child.id));
    descendants.push(...childDescendants);
  }

  return descendants.map((d) => ({
    id: Number(d.id),
    depth: d.depth,
  }));
}

type DeleteFolderRequest = z.infer<typeof deleteFolderSchema>;

const deleteFolder = async (req: ValidatedRequest<DeleteFolderRequest>) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!user) {
      return respond.unauthorized("User not authorized or not found.");
    }

    const folder = await prisma.folder.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        version: true,
        files: true,
        subFolders: {
          include: {
            files: true,
          },
        },
      },
    });

    if (!folder) {
      return respond.notFound("Folder not found");
    }

    const version = await prisma.version.findUnique({
      where: { id: Number(folder.versionId) },
      include: {
        project: true,
      },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const project = await prisma.project.findUnique({
      where: {
        id: version.projectId,
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: project.id,
        userId: user.id,
      },
    });

    if (!projectUser) {
      return respond.unauthorized(
        "User cannot delete folders in this project, as they are not part of this project."
      );
    }

    // Get all files in this folder and its subfolders
    const allFiles = await prisma.file.findMany({
      where: {
        folderId: Number(id),
      },
    });

    // Collect all descendant folders to get their files
    const descendants = await findAllDescendants(Number(id));
    const descendantIds = descendants.map((d) => d.id);

    const descendantFiles = await prisma.file.findMany({
      where: {
        folderId: {
          in: descendantIds,
        },
      },
    });

    // Combine all files
    const filesToDelete = [...allFiles, ...descendantFiles];

    // Import the Supabase client
    const { client: sbClient } = await import("../apis/supabase");

    // Delete all files from storage
    for (const file of filesToDelete) {
      try {
        const { error: deleteError } = await sbClient.storage
          .from("projects")
          .remove([encodePath(file.s3Path)]);

        if (deleteError) {
          console.error(
            `Error deleting file ${file.id} from Supabase Storage:`,
            deleteError.message
          );
        }
      } catch (error) {
        console.error(`Error deleting file ${file.id}:`, error);
        // Continue with other files even if one fails
      }
    }

    // Delete the folder (cascade will handle subfolders and files)
    await prisma.folder.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.message("Folder deleted successfully");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete folder");
  }
};

type GetFolderContentsRequest = z.infer<typeof getFolderContentsSchema>;

const getFolderContents = async (
  req: ValidatedRequest<GetFolderContentsRequest>
) => {
  try {
    const { id } = req.params;

    const folder = await prisma.folder.findUnique({
      where: { id: Number(id) },
      include: {
        files: true,
        subFolders: {
          include: {
            files: true,
          },
        },
      },
    });

    if (!folder) {
      return respond.notFound("Folder not found");
    }

    // Create a new ZIP file
    const zip = new JSZip();

    // Helper function to recursively add files from folders
    const addFolderToZip = async (
      currentFolder: any,
      parentPath: string = ""
    ) => {
      // Add files in current folder
      for (const file of currentFolder.files) {
        const { data, error } = await sbClient.storage
          .from("projects")
          .download(file.s3Path);

        if (error) {
          console.error(`Error downloading file ${file.name}:`, error);
          continue;
        }

        const arrayBuffer = await data.arrayBuffer();
        const filePath = parentPath ? `${parentPath}/${file.name}` : file.name;
        zip.file(filePath, arrayBuffer);
      }

      // Recursively process subfolders
      if (currentFolder.subFolders) {
        for (const subFolder of currentFolder.subFolders) {
          const newPath = parentPath
            ? `${parentPath}/${subFolder.name}`
            : subFolder.name;
          await addFolderToZip(subFolder, newPath);
        }
      }
    };

    // Start adding files to zip
    await addFolderToZip(folder);

    // Generate zip file
    const zipBuffer = await zip.generateAsync({
      type: "nodebuffer",
      compression: "DEFLATE",
      compressionOptions: { level: 5 },
    });

    // Set response headers
    req.res?.setHeader("Content-Type", "application/zip");
    req.res?.setHeader(
      "Content-Disposition",
      `attachment; filename="${folder.name}.zip"`
    );

    // Create readable stream from buffer and pipe to response
    const readable = Readable.from(zipBuffer);
    readable.pipe(req.res!);

    return { isStreaming: true };
  } catch (error) {
    console.error("Error getting folder contents:", error);
    return respond.error("Failed to get folder contents");
  }
};

export const folderController = {
  createFolder,
  uploadFolder,
  getFolder,
  getFolderAncestors,
  updateFolder,
  deleteFolder,
  getFolderContents,
};

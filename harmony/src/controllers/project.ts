// src/controllers/project.ts

import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { client as sbClient } from "../apis/supabase";
import { respond } from "../middleware/response.handler";
import { ProjectInviteStatus } from "../types/projectInvite";
import { decodePath } from "../utils/pathEncoding";
import {
  addContributorSchema,
  createProjectSchema,
  deleteProjectSchema,
  downloadProjectSchema,
  getAllCommentsByProjectSchema,
  getAllTasksByProjectSchema,
  getAllVersionsbyProjectSchema,
  getProjectConversationsSchema,
  getProjectSchema,
  removeContributorSchema,
  updateProjectSchema,
} from "../validation/project";
import { getProjectInvitesSchema } from "../validation/projectInvite";

type CreateProjectRequest = z.infer<typeof createProjectSchema>;

// Logic for creating a project
const createProject = async (req: ValidatedRequest<CreateProjectRequest>) => {
  try {
    const { name, genre, description, tempo, key } = req.body;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!author) {
      return respond.error("User not found");
    }

    const project = await prisma.project.create({
      data: {
        name,
        authorId: author.id,
        genre,
        description,
        tempo,
        key,
      },
    });

    await prisma.projectUser.create({
      data: {
        projectId: project.id,
        userId: author.id,
        role: "owner",
      },
    });

    await prisma.version.create({
      data: {
        projectId: project.id,
        authorId: author.id,
        number: 1,
        message: "Initial version",
      },
    });

    // Create a conversation for the project
    await prisma.conversation.create({
      data: {
        projectId: project.id,
        authorId: author.id,
        name: project.name,
        description:
          "Project-linked conversation. Edit participants in project contributors.",
        participants: {
          create: {
            userId: author.id,
          },
        },
      },
    });

    return respond.with({ id: project.id });
  } catch (error) {
    console.error("Project creation error:", error);
    if (error instanceof Error) {
      return respond.error(`Failed to create project: ${error.message}`);
    }
    return respond.error("Failed to create project: Unknown error");
  }
};

type GetProjectRequest = z.infer<typeof getProjectSchema>;

const getProject = async (req: ValidatedRequest<GetProjectRequest>) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        contributors: {
          include: {
            user: true,
          },
        },
        author: true,
        tasks: true,
        comments: true,
        versions: {
          orderBy: {
            createdAt: "desc",
          },
          include: {
            author: true,
          },
        },
        conversations: {
          include: {
            participants: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    return respond.with(project);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve project");
  }
};

type GetAllVersionsRequest = z.infer<typeof getAllVersionsbyProjectSchema>;
const getAllVersionsByProject = async (
  req: ValidatedRequest<GetAllVersionsRequest>
) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id: Number(id) },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    const allVersions = await prisma.version.findMany({
      where: { projectId: project.id },
      orderBy: {
        createdAt: "desc",
      },
    });
    return respond.with(allVersions);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve versions");
  }
};

type getAllTasksRequest = z.infer<typeof getAllTasksByProjectSchema>;
const getAllTasksByProject = async (
  req: ValidatedRequest<getAllTasksRequest>
) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id: Number(id) },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }
    const allTasks = await prisma.task.findMany({
      where: { projectId: project.id },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        assignee: true,
        author: true,
      },
    });
    return respond.with(allTasks);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve tasks for this project");
  }
};

type GetAllCommentsRequest = z.infer<typeof getAllCommentsByProjectSchema>;

const getAllCommentsByProject = async (
  req: ValidatedRequest<GetAllCommentsRequest>
) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id: Number(id) },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    const allComments = await prisma.comment.findMany({
      where: { projectId: project.id },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        author: true,
      },
    });

    return respond.with(allComments);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve comments for this project");
  }
};

type UpdateProjectRequest = z.infer<typeof updateProjectSchema>;

const updateProject = async (req: ValidatedRequest<UpdateProjectRequest>) => {
  try {
    const { id } = req.params;
    const { name, genre, description, tempo, key } = req.body;

    // Verify user is the project owner
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!user) {
      return respond.unauthorized("User not found");
    }

    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: Number(id),
        userId: user.id,
        role: "owner",
      },
    });

    if (!projectUser) {
      return respond.unauthorized("User is not the owner of this project");
    }

    const project = await prisma.project.update({
      where: {
        id: Number(id),
      },
      data: {
        name,
        genre,
        description,
        tempo,
        key,
        updatedAt: new Date(),
      },
    });
    if (!project) {
      return respond.notFound("Project not found");
    }

    return respond.with(project);
  } catch (error) {
    // Handle errors
    console.error(error);
    return respond.error("Failed to update project");
  }
};

type DeleteProjectRequest = z.infer<typeof deleteProjectSchema>;

const deleteProject = async (req: ValidatedRequest<DeleteProjectRequest>) => {
  try {
    // Validate input
    // Do project deletion logic here
    // Return successful response
    const { id } = req.params;
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });
    if (!user) {
      return respond.unauthorized("User not found");
    }
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
    });
    if (!project) {
      return respond.notFound("Project not found");
    }
    // Confirm ownership
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: project.id,
        userId: user.id,
        role: "owner",
      },
    });
    if (!projectUser) {
      return respond.unauthorized("User is not the owner of this project");
    }
    await prisma.projectUser.deleteMany({
      where: {
        projectId: project.id,
      },
    });
    await prisma.project.delete({
      where: {
        id: Number(id),
      },
    });
    return respond.message("Project deleted successfully");
  } catch (error) {
    // Handle errors
    console.error(error);
    return respond.error("Failed to delete project");
  }
};

type AddContributorRequest = z.infer<typeof addContributorSchema>;

const addContributor = async (req: ValidatedRequest<AddContributorRequest>) => {
  try {
    const { id } = req.params;
    const { userName } = req.body;
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });
    if (!user) {
      return respond.unauthorized("User not found");
    }
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
    });
    if (!project) {
      return respond.notFound("Project not found");
    }
    // Confirm ownership
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: project.id,
        userId: user.id,
        role: "owner",
      },
    });
    if (!projectUser) {
      return respond.unauthorized("User is not the owner of this project");
    }

    // Find user from name
    const addedUser = await prisma.user.findUnique({
      where: {
        name: userName,
      },
    });

    if (!addedUser) {
      return respond.notFound("User not found");
    }

    const res = await prisma.projectUser.create({
      data: {
        projectId: project.id,
        userId: addedUser.id,
        role: "contributor",
      },
    });

    return respond.with(res);
  } catch (error: any) {
    console.error(error);
    return respond.error("Failed to add contributor");
  }
};

type RemoveContributorRequest = z.infer<typeof removeContributorSchema>;

const removeContributor = async (
  req: ValidatedRequest<RemoveContributorRequest>
) => {
  try {
    const { id } = req.params;
    const { userName: contributorName } = req.body;
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });
    if (!user) {
      return respond.unauthorized("User not found");
    }
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
    });
    if (!project) {
      return respond.notFound("Project not found");
    }
    // Confirm ownership
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: project.id,
        userId: user.id,
        role: "owner",
      },
    });
    if (!projectUser && user.name !== contributorName) {
      return respond.unauthorized("Not authorized to remove this contributor");
    }

    // Find user from name
    const addedUser = await prisma.user.findUnique({
      where: {
        name: contributorName,
      },
    });

    if (!addedUser) {
      return respond.notFound("User not found");
    }

    await prisma.projectUser.delete({
      where: {
        projectId_userId: {
          projectId: project.id,
          userId: addedUser.id,
        },
      },
    });

    // Remove the user from project-linked conversation
    const conversation = await prisma.conversation.findFirst({
      where: {
        projectId: project.id,
      },
    });
    if (conversation) {
      await prisma.conversationParticipant.delete({
        where: {
          conversationId_userId: {
            conversationId: conversation.id,
            userId: addedUser.id,
          },
        },
      });
    }

    return respond.with({ success: true });
  } catch (error: any) {
    console.error(error);
    return respond.error("Failed to remove contributor");
  }
};

type GetProjectInvitesRequest = z.infer<typeof getProjectInvitesSchema>;

/**
 * Get all invites for a project
 * Only project owners or admins can view all invites
 */
const getProjectInvites = async (
  req: ValidatedRequest<GetProjectInvitesRequest>
) => {
  try {
    const { id } = req.params;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Check if the project exists
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    // Check if the current user has permission to view invites
    if (project.authorId !== currentUser.id) {
      return respond.unauthorized(
        "You don't have permission to view invites for this project"
      );
    }

    // Get all invites for the project within the last 24 hours
    const invites = await prisma.projectInvite.findMany({
      where: {
        projectId: Number(id),
        status: ProjectInviteStatus.PENDING, // Only get pending invites
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            profileImg: true,
          },
        },
      },
    });

    return respond.with(invites);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get project invites");
  }
};

type DownloadProjectRequest = z.infer<typeof downloadProjectSchema>;

/**
 * Download all files and folders in a project as a zip file
 */
const downloadProject = async (
  req: ValidatedRequest<DownloadProjectRequest>
) => {
  try {
    const { id } = req.params;
    const { versionId } = req.query as { versionId?: string };

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Check if the project exists
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        versions: {
          orderBy: {
            number: "desc",
          },
          take: 1,
        },
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    // Check if the user has access to the project
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: Number(id),
        userId: currentUser.id,
      },
    });

    if (!projectUser) {
      return respond.unauthorized("You don't have access to this project");
    }

    // Get the specified version or use the latest version
    let version;
    if (versionId) {
      version = await prisma.version.findUnique({
        where: {
          id: Number(versionId),
          projectId: Number(id),
        },
      });

      if (!version) {
        return respond.notFound("Specified version not found for this project");
      }
    } else {
      // Get the latest version
      version = project.versions[0];
      if (!version) {
        return respond.notFound("No versions found for this project");
      }
    }

    // Get all folders and files for this version
    const folders = await prisma.folder.findMany({
      where: { versionId: version.id },
      include: { files: true },
    });

    const rootFiles = await prisma.file.findMany({
      where: {
        versionId: version.id,
        folderId: null,
      },
    });

    // Build folder structure map
    const folderMap = new Map();
    folders.forEach((folder) => {
      folderMap.set(folder.id, folder);
    });

    // Helper function to get folder path
    const getFolderPath = (folder: any): string => {
      let path = folder.name;
      let currentFolder = folder;

      while (currentFolder.parentId) {
        currentFolder = folderMap.get(currentFolder.parentId);
        if (!currentFolder) break;
        path = `${currentFolder.name}/${path}`;
      }

      return path;
    };

    // Use archiver for streaming
    const archiver = require("archiver");

    // Set response headers
    req.res?.setHeader("Content-Type", "application/zip");
    req.res?.setHeader(
      "Content-Disposition",
      `attachment; filename="${project.name.replace(/\s/g, "_")}.zip"`
    );

    // Create a zip archive
    const archive = archiver("zip", {
      zlib: { level: 5 }, // Compression level
    });

    // Pipe archive data to the response
    archive.pipe(req.res!);

    // Handle archive warnings and errors
    archive.on("warning", (err: any) => {
      if (err.code === "ENOENT") {
        console.warn("Archive warning:", err);
      } else {
        console.error("Archive error:", err);
        throw err;
      }
    });

    archive.on("error", (err: any) => {
      console.error("Archive error:", err);
      throw err;
    });

    // Process folders and files
    for (const folder of folders) {
      const folderPath = getFolderPath(folder);

      // Create empty directory in the archive
      archive.append(null, { name: `${folderPath}/` });

      // Add files in this folder
      for (const file of folder.files) {
        try {
          // Create a signed URL instead of downloading the file
          const { data: signedUrlData, error: signedUrlError } =
            await sbClient.storage
              .from("projects")
              .createSignedUrl(file.s3Path, 60); // 60 seconds expiry

          if (signedUrlError) {
            console.error(
              `Error creating signed URL for ${file.name}:`,
              signedUrlError
            );
            continue;
          }

          // Use node-fetch to stream the file
          const fetch = require("node-fetch");
          const response = await fetch(signedUrlData.signedUrl);

          if (!response.ok) {
            console.error(
              `Error fetching file ${file.name}: ${response.statusText}`
            );
            continue;
          }

          // Use decoded name for the file in the archive
          const decodedName = decodePath(file.name);
          archive.append(response.body, {
            name: `${folderPath}/${decodedName}`,
          });
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
        }
      }
    }

    // Add root files
    for (const file of rootFiles) {
      try {
        const { data: signedUrlData, error: signedUrlError } =
          await sbClient.storage
            .from("projects")
            .createSignedUrl(file.s3Path, 60);

        if (signedUrlError) {
          console.error(
            `Error creating signed URL for ${file.name}:`,
            signedUrlError
          );
          continue;
        }

        const fetch = require("node-fetch");
        const response = await fetch(signedUrlData.signedUrl);

        if (!response.ok) {
          console.error(
            `Error fetching file ${file.name}: ${response.statusText}`
          );
          continue;
        }

        archive.append(response.body, { name: file.name });
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
      }
    }

    // Finalize the archive (this is important!)
    await archive.finalize();

    return { isStreaming: true };
  } catch (error) {
    console.error("Error downloading project:", error);
    return respond.error("Failed to download project");
  }
};

type GetProjectConversationsRequest = z.infer<
  typeof getProjectConversationsSchema
>;

const getProjectConversations = async (
  req: ValidatedRequest<GetProjectConversationsRequest>
) => {
  try {
    const { id } = req.params;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Check if the project exists
    const project = await prisma.project.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    // Check if the user has access to the project
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        projectId: Number(id),
        userId: currentUser.id,
      },
    });

    if (!projectUser) {
      return respond.unauthorized("You don't have access to this project");
    }

    // Get all conversations for this project
    const conversations = await prisma.conversation.findMany({
      where: { projectId: Number(id) },
      include: {
        messages: {
          orderBy: { createdAt: "desc" },
          take: 1,
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                profileImg: true,
              },
            },
          },
        },
      },
    });

    return respond.with(conversations);
  } catch (error) {
    console.error("Error getting project conversations:", error);
    return respond.error("Failed to get project conversations");
  }
};

export const projectController = {
  createProject,
  getProject,
  getAllTasksByProject,
  getAllVersionsByProject,
  getAllCommentsByProject,
  updateProject,
  deleteProject,
  addContributor,
  removeContributor,
  getProjectInvites,
  downloadProject,
  getProjectConversations,
};

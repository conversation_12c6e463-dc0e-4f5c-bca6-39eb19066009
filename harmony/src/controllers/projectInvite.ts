// src/controllers/projectInvite.ts

import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { respond } from "../middleware/response.handler";
import { ProjectInviteStatus } from "../types/projectInvite";
import {
  acceptProjectInviteSchema,
  createProjectInviteSchema,
  declineProjectInviteSchema,
  revokeProjectInviteSchema,
} from "../validation/projectInvite";
import { getIO } from "../websocket";

type CreateProjectInviteRequest = z.infer<typeof createProjectInviteSchema>;

/**
 * Create a new project invite
 * Only project owners or contributors with appropriate permissions can send invites
 */
const createProjectInvite = async (
  req: ValidatedRequest<CreateProjectInviteRequest>
) => {
  try {
    const { projectId, userId } = req.body;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Check if the project exists
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
      },
    });

    if (!project) {
      return respond.notFound("Project not found");
    }

    if (currentUser.id !== project.authorId) {
      return respond.unauthorized(
        "You don't have permission to invite users to this project"
      );
    }

    // Check if the user to invite exists
    const userToInvite = await prisma.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!userToInvite) {
      return respond.notFound("User to invite not found");
    }

    // Check if the user is already a contributor
    const existingContributor = await prisma.projectUser.findFirst({
      where: {
        projectId,
        userId,
      },
    });

    if (existingContributor) {
      return respond.error("User is already a contributor to this project");
    }

    // Check if there's already a pending invite
    const existingInvite = await prisma.projectInvite.findFirst({
      where: {
        projectId,
        userId,
        status: ProjectInviteStatus.PENDING,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        },
      },
    });

    if (existingInvite) {
      return respond.error("An invite is already pending for this user");
    }

    // Create the invite
    const invite = await prisma.projectInvite.create({
      data: {
        projectId,
        userId,
        status: ProjectInviteStatus.PENDING,
      },
    });

    const io = getIO();
    if (io) {
      const sanitizedUserId = Number(userId);
      // Emit a invite event to the recipient's room
      io.to(String(sanitizedUserId)).emit("invite");
    }

    return respond.with(invite);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to create project invite");
  }
};

type AcceptProjectInviteRequest = z.infer<typeof acceptProjectInviteSchema>;

/**
 * Accept a project invite
 * Adds the user to the project contributors and updates invite status
 */
const acceptProjectInvite = async (
  req: ValidatedRequest<AcceptProjectInviteRequest>
) => {
  try {
    const { id } = req.params;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Find the invite
    const invite = await prisma.projectInvite.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!invite) {
      return respond.notFound("Invite not found");
    }

    // Check if the invite belongs to the current user
    if (invite.userId !== currentUser.id) {
      return respond.unauthorized("This invite does not belong to you");
    }

    // Check if the invite is still pending
    if (invite.status !== ProjectInviteStatus.PENDING) {
      return respond.error(`Invite has already been ${invite.status}`);
    }

    // Update the invite status
    await prisma.projectInvite.update({
      where: {
        id: Number(id),
      },
      data: {
        status: ProjectInviteStatus.ACCEPTED,
        updatedAt: new Date(),
      },
    });

    // Add the user to the project
    await prisma.projectUser.create({
      data: {
        projectId: invite.projectId,
        userId: invite.userId,
        role: "contributor", // Default role for invited users
      },
    });

    // Add to linked conversation - if any
    const conversation = await prisma.conversation.findFirst({
      where: {
        projectId: invite.projectId,
      },
    });

    if (conversation) {
      await prisma.conversationParticipant.create({
        data: {
          conversationId: conversation.id,
          userId: invite.userId,
        },
      });
    }

    return respond.with({ success: true, message: "Project invite accepted" });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to accept project invite");
  }
};

type DeclineProjectInviteRequest = z.infer<typeof declineProjectInviteSchema>;

/**
 * Decline a project invite
 * Updates the invite status to declined
 */
const declineProjectInvite = async (
  req: ValidatedRequest<DeclineProjectInviteRequest>
) => {
  try {
    const { id } = req.params;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Find the invite
    const invite = await prisma.projectInvite.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!invite) {
      return respond.notFound("Invite not found");
    }

    // Check if the invite belongs to the current user
    if (invite.userId !== currentUser.id) {
      return respond.unauthorized("This invite does not belong to you");
    }

    // Check if the invite is still pending
    if (invite.status !== ProjectInviteStatus.PENDING) {
      return respond.error(`Invite has already been ${invite.status}`);
    }

    // Update the invite status
    await prisma.projectInvite.update({
      where: {
        id: Number(id),
      },
      data: {
        status: ProjectInviteStatus.DECLINED,
        updatedAt: new Date(),
      },
    });

    return respond.with({ success: true, message: "Project invite declined" });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to decline project invite");
  }
};

type RevokeProjectInviteRequest = z.infer<typeof revokeProjectInviteSchema>;

/**
 * Revoke a project invite
 * Only project owners or admins can revoke invites
 */
const revokeProjectInvite = async (
  req: ValidatedRequest<RevokeProjectInviteRequest>
) => {
  try {
    const { id } = req.params;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Find the invite
    const invite = await prisma.projectInvite.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        project: true,
      },
    });

    if (!invite) {
      return respond.notFound("Invite not found");
    }

    // Check if the current user has permission to revoke invites
    if (invite.project.authorId !== currentUser.id) {
      return respond.unauthorized(
        "You don't have permission to revoke invites for this project"
      );
    }

    // Delete the invite
    await prisma.projectInvite.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.with({ success: true, message: "Project invite revoked" });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to revoke project invite");
  }
};

export const projectInviteController = {
  createProjectInvite,
  acceptProjectInvite,
  declineProjectInvite,
  revokeProjectInvite,
};

// src/controllers/task.ts

import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { respond } from "../middleware/response.handler";
import { TaskStatus } from "../types/task";
import {
  createTaskSchema,
  deleteTaskSchema,
  getTaskSchema,
  updateTaskSchema,
} from "../validation/task";
import { getIO } from "../websocket";

// TODO: ADD PROJECT COLLABORATOR VERIFICATION FOR EACH CONTROLLER

type CreateTaskRequest = z.infer<typeof createTaskSchema>;

const createTask = async (req: ValidatedRequest<CreateTaskRequest>) => {
  try {
    const {
      projectId,
      title,
      assigneeId,
      description,
      category,
      status,
      priority,
      dueDate,
    } = req.body;
    const author = await prisma.user.findUnique({
      where: {
        sbid: req.user?.id,
      },
    });

    if (!author) {
      return respond.error("Author user not found");
    }

    const project = await prisma.project.findUnique({
      where: {
        id: Number(projectId),
      },
    });

    if (!project) {
      return respond.error("Project not found");
    }
    const newCreatedTask = await prisma.task.create({
      data: {
        title,
        projectId: Number(projectId),
        authorId: author.id,
        assigneeId: Number(assigneeId),
        description,
        category,
        status: status || TaskStatus.NOT_STARTED,
        priority,
        dueDate,
      },
      include: {
        author: true,
        assignee: true,
      },
    });

    const io = getIO();
    if (io) {
      const sanitizedProjectId = Number(projectId);
      // Emit a task event to the project room
      io.to(`project_${sanitizedProjectId}`).emit("task", {
        projectId: sanitizedProjectId,
      });
    }

    return respond.with(newCreatedTask);
  } catch (error) {
    // Handle errors
    console.error(error);
    return respond.error("Failed to create task");
  }
};

type GetTaskRequest = z.infer<typeof getTaskSchema>;

const getTask = async (req: ValidatedRequest<GetTaskRequest>) => {
  try {
    const { id } = req.params;

    const task = await prisma.task.findUnique({
      where: {
        id: Number(id),
      },
      include: {
        author: true,
        assignee: true,
        project: true,
      },
    });

    if (!task) {
      return respond.notFound("Task not found");
    }

    return respond.with(task);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve task");
  }
};

type UpdateTaskRequest = z.infer<typeof updateTaskSchema>;

const updateTask = async (req: ValidatedRequest<UpdateTaskRequest>) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      assigneeId,
      category,
      status,
      priority,
      dueDate,
      versionId,
      folderId,
      fileId,
    } = req.body;

    const currentTask = await prisma.task.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!currentTask) {
      return respond.notFound("Task not found");
    }

    const newUpdatedTask = await prisma.task.update({
      where: {
        id: Number(id),
      },
      data: {
        title,
        description,
        assigneeId: assigneeId === 0 ? null : assigneeId,
        category,
        status,
        priority,
        dueDate,
        updatedAt: new Date(),
        versionId: versionId || null,
        folderId: folderId || null,
        fileId: fileId || null,
      },
      include: {
        author: true,
        assignee: true,
        project: true,
      },
    });

    const io = getIO();
    if (io) {
      const sanitizedProjectId = Number(currentTask.projectId);
      // Emit a task event to the project room
      io.to(`project_${sanitizedProjectId}`).emit("task", {
        projectId: sanitizedProjectId,
      });
    }

    return respond.with(newUpdatedTask);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update task");
  }
};

type DeleteTaskRequest = z.infer<typeof deleteTaskSchema>;

const deleteTask = async (req: ValidatedRequest<DeleteTaskRequest>) => {
  try {
    const { id } = req.params;

    // Verify the task exists
    const task = await prisma.task.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!task) {
      return respond.notFound("Task not found");
    }

    // Delete the task
    await prisma.task.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.message("Task deleted successfully");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete task");
  }
};

// Update the export to include all controllers
export const taskController = {
  createTask,
  getTask,
  updateTask,
  deleteTask,
};

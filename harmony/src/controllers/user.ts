// src/controllers/user.ts

import { z } from "zod";
import { prisma, ValidatedRequest } from "..";
import { client as sbClient, sbDeleteUser } from "../apis/supabase";
import { respond } from "../middleware/response.handler";
import { ProjectInviteStatus } from "../types/projectInvite";
import { encodePath } from "../utils/pathEncoding";
import { getWaveformFromUrl } from "../utils/waveform";
import { getUserInvitesSchema } from "../validation/projectInvite";
import {
  createLeafletSchema,
  deleteLeafletSchema,
  deleteUserSchema,
  getAllUsersSchema,
  getLeafletContentSchema,
  getLeafletSchema,
  getUserConversationsSchema,
  getUserLeafletsSchema,
  getUserProjectsSchema,
  getUserSchema,
  searchUsersSchema,
  updateLeafletSchema,
  updateUserSchema,
} from "../validation/user";
import { uploadLargeFile } from "./file";

type GetAllUsersRequest = z.infer<typeof getAllUsersSchema>;

export const getAllUsers = async (
  req: ValidatedRequest<GetAllUsersRequest>
) => {
  try {
    const { page } = req.query;
    const pageSize = 12;
    const skip = (page - 1) * pageSize;

    // Get users with their project relationships in a single query
    const users = await prisma.user.findMany({
      skip,
      take: pageSize,
      select: {
        id: true,
        name: true,
        email: true,
        profileImg: true,
        bio: true,
        projectUsers: {
          select: {
            project: {
              select: {
                genre: true,
              },
            },
          },
        },
      },
    });

    // Process the data in memory to extract top genres
    const usersWithTopGenres = users.map((user) => {
      // Count genres
      const genreCounts: Record<string, number> = {};
      user.projectUsers.forEach((pu) => {
        const genre = pu.project.genre;
        if (genre) {
          genreCounts[genre] = (genreCounts[genre] || 0) + 1;
        }
      });

      // Sort and get top 3
      const topGenres = Object.entries(genreCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([genre, count]) => ({ genre, count }));

      // Return user with top genres but without the projectUsers data
      const { projectUsers, ...userData } = user;
      return { ...userData, topGenres };
    });

    // Get total count of users for pagination
    const totalUsers = await prisma.user.count();

    return respond.with({
      users: usersWithTopGenres,
      totalPages: Math.ceil(totalUsers / pageSize),
      currentPage: page,
      totalUsers,
    });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve users");
  }
};

type SearchUsersRequest = z.infer<typeof searchUsersSchema>;

export const searchUsers = async (
  req: ValidatedRequest<SearchUsersRequest>
) => {
  try {
    const { q, includeTopGenres } = req.query;

    // Base select object without projectUsers
    const baseSelect = {
      id: true,
      name: true,
      email: true,
      profileImg: true,
      bio: true,
    };

    // Add projectUsers only if includeTopGenres is true
    const select = includeTopGenres
      ? {
          ...baseSelect,
          projectUsers: {
            select: {
              project: {
                select: {
                  genre: true,
                },
              },
            },
          },
        }
      : baseSelect;

    const users = await prisma.user.findMany({
      where: {
        name: {
          contains: q,
          mode: "insensitive",
        },
      },
      select,
      take: 12,
    });

    if (includeTopGenres) {
      // Process the data in memory to extract top genres
      const usersWithTopGenres = users.map((user) => {
        // Count genres
        const genreCounts: Record<string, number> = {};
        (user as any).projectUsers?.forEach(
          (pu: { project: { genre: string | null } }) => {
            const genre = pu.project.genre;
            if (genre) {
              genreCounts[genre] = (genreCounts[genre] || 0) + 1;
            }
          }
        );

        // Sort and get top 3
        const topGenres = Object.entries(genreCounts)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 3)
          .map(([genre, count]) => ({ genre, count }));

        // Return user with top genres but without the projectUsers data
        const { projectUsers, ...userData } = user as any;
        return { ...userData, topGenres };
      });

      return respond.with(usersWithTopGenres);
    }

    return respond.with(users);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to search users");
  }
};

type GetUserProjectsRequest = z.infer<typeof getUserProjectsSchema>;

export const getUserProjects = async (
  req: ValidatedRequest<GetUserProjectsRequest>
) => {
  try {
    const { name } = req.params;

    const projectUsers = await prisma.projectUser.findMany({
      where: {
        user: {
          name,
        },
      },
      include: {
        project: {
          include: {
            contributors: {
              include: {
                user: true,
              },
            },
            author: true,
          },
        },
      },
      orderBy: {
        project: {
          updatedAt: "desc",
        },
      },
    });

    const projects =
      projectUsers.map((projectUser) => projectUser.project) || [];

    return respond.with(projects);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve user projects");
  }
};

type GetUserRequest = z.infer<typeof getUserSchema>;

export const getUser = async (req: ValidatedRequest<GetUserRequest>) => {
  try {
    const { name } = req.params;

    const user = await prisma.user.findUnique({
      where: {
        name,
      },
    });

    if (!user) {
      return respond.notFound("User not found");
    }

    // Exclude id and sbid
    const sanitizedUser = {
      id: Number(user.id),
      email: user.email,
      name: user.name,
      role: user.role,
      bio: user.bio,
      socials: user.socials,
      profileImg: user.profileImg,
      createdAt: user.createdAt,
    };

    return respond.with(sanitizedUser);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve user");
  }
};

type UpdateUserRequest = z.infer<typeof updateUserSchema>;

export const updateUser = async (req: ValidatedRequest<UpdateUserRequest>) => {
  try {
    const { name: oldName } = req.params;
    const { email, name, role, bio, profileImg, socials } = req.body;

    const user = await prisma.user.update({
      where: {
        name: oldName,
      },
      data: {
        email,
        name,
        role,
        bio,
        profileImg,
        updatedAt: new Date(),
        socials,
      },
    });

    const sanitizedUser = {
      email: user.email,
      name: user.name,
      role: user.role,
      bio: user.bio,
      profileImg: user.profileImg,
      createdAt: user.createdAt,
      socials: user.socials,
    };

    return respond.with(sanitizedUser);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update user");
  }
};

type DeleteUserRequest = z.infer<typeof deleteUserSchema>;

export const deleteUser = async (req: ValidatedRequest<DeleteUserRequest>) => {
  try {
    const { name } = req.params;
    const { email, password } = req.body;

    if (email && password) {
      const { data, error } = await sbClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return respond.unauthorized("Invalid credentials");
      }

      if (data?.user?.id !== req.user?.id) {
        return respond.unauthorized("Invalid credentials");
      }
    }

    const { sbid } = await prisma.user.delete({
      where: {
        name,
        email,
      },
    });

    await sbDeleteUser(sbid);

    return respond.with("User deleted");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete user");
  }
};

type GetUserInvitesRequest = z.infer<typeof getUserInvitesSchema>;

/**
 * Get all invites for the current user
 */
const getUserInvites = async (req: ValidatedRequest<GetUserInvitesRequest>) => {
  try {
    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    // Get all invites for the user within the last 24 hours
    const invites = await prisma.projectInvite.findMany({
      where: {
        userId: currentUser.id,
        status: ProjectInviteStatus.PENDING, // Only get pending invites
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        },
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            genre: true,
            author: {
              select: {
                id: true,
                name: true,
                profileImg: true,
              },
            },
          },
        },
      },
    });

    return respond.with(invites);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get user invites");
  }
};

type CreateLeafletRequest = z.infer<typeof createLeafletSchema>;

const createLeaflet = async (req: ValidatedRequest<CreateLeafletRequest>) => {
  try {
    const { name, featured, metadata } = req.body;

    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }

    // Get the current user
    const currentUser = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });

    if (!currentUser) {
      return respond.error("User not found");
    }

    let s3Path = "";

    if (req.file) {
      s3Path = encodePath(
        `users/${currentUser.id}/audio/${req.file.originalname}`
      );

      await uploadLargeFile(
        "users",
        s3Path,
        req.file,
        Number(currentUser.id).toString()
      );
    }

    const audioSample = await prisma.leaflet.create({
      data: {
        userId: currentUser.id,
        name,
        s3Path,
        featured,
        metadata,
      },
    });

    return respond.with(audioSample);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to create audio sample");
  }
};

type GetUserLeafletsRequest = z.infer<typeof getUserLeafletsSchema>;

const getUserLeaflets = async (
  req: ValidatedRequest<GetUserLeafletsRequest>
) => {
  try {
    const { name } = req.params;

    const user = await prisma.user.findUnique({
      where: {
        name,
      },
    });

    if (!user) {
      return respond.notFound("User not found");
    }

    const audioSamples = await prisma.leaflet.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        featured: "desc",
      },
    });

    return respond.with(audioSamples);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get user audio samples");
  }
};

type GetLeafletRequest = z.infer<typeof getLeafletSchema>;

const getLeaflet = async (req: ValidatedRequest<GetLeafletRequest>) => {
  try {
    const { id } = req.params;

    const audioSample = await prisma.leaflet.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!audioSample) {
      return respond.notFound("Audio sample not found");
    }

    return respond.with(audioSample);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get user audio sample");
  }
};

type UpdateLeafletRequest = z.infer<typeof updateLeafletSchema>;

const updateLeaflet = async (req: ValidatedRequest<UpdateLeafletRequest>) => {
  try {
    const { id } = req.params;
    const { name, featured, metadata } = req.body;

    // Check if new name exists in this user's samples already
    // If not, update the s3Path
    if (name) {
      const audioSample = await prisma.leaflet.findUnique({
        where: {
          id: Number(id),
        },
      });

      if (!audioSample) {
        return respond.notFound("Audio sample not found");
      }

      const user = await prisma.user.findUnique({
        where: {
          id: audioSample.userId,
        },
      });

      if (!user) {
        return respond.error("User not found");
      }

      const existingSample = await prisma.leaflet.findFirst({
        where: {
          userId: user.id,
          name,
        },
      });

      if (existingSample) {
        return respond.error("Audio sample with this name already exists");
      }

      const newS3Path = encodePath(`users/${user.id}/audio/${name}`);

      await prisma.leaflet.update({
        where: {
          id: Number(id),
        },
        data: {
          s3Path: newS3Path,
        },
      });

      await sbClient.storage.from("users").move(audioSample.s3Path, newS3Path);
    }

    const audioSample = await prisma.leaflet.update({
      where: {
        id: Number(id),
      },
      data: {
        name,
        featured,
        metadata,
      },
    });

    return respond.with(audioSample);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update user audio sample");
  }
};

type DeleteLeafletRequest = z.infer<typeof deleteLeafletSchema>;

const deleteLeaflet = async (req: ValidatedRequest<DeleteLeafletRequest>) => {
  try {
    const { id } = req.params;
    const audioSample = await prisma.leaflet.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!audioSample) {
      return respond.notFound("Audio sample not found");
    }

    await sbClient.storage.from("users").remove([audioSample.s3Path]);

    await prisma.leaflet.delete({
      where: {
        id: Number(id),
      },
    });

    return respond.with("Audio sample deleted");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete user audio sample");
  }
};

type GetLeafletContentRequest = z.infer<typeof getLeafletContentSchema>;

const getLeafletContent = async (
  req: ValidatedRequest<GetLeafletContentRequest>
) => {
  try {
    const { id } = req.params;

    const audioSample = await prisma.leaflet.findUnique({
      where: {
        id: Number(id),
      },
    });

    if (!audioSample) {
      return respond.notFound("Audio sample not found");
    }

    // Create signed URL with longer expiry
    const { data, error } = await sbClient.storage
      .from("users")
      .createSignedUrl(audioSample.s3Path, 3600); // 1 hour expiry

    if (error) {
      console.error("Error creating signed URL:", error.message);
      return respond.error(`Failed to access audio sample: ${error.message}`);
    }

    // Calculate waveform from the signed URL (assuming it's a WAV file)
    let waveform: number[] = [];
    try {
      waveform = await getWaveformFromUrl(data.signedUrl, 200);
    } catch (err) {
      console.error("Waveform calculation failed:", err);
      // fallback: empty array
      waveform = [];
    }

    // Return the signed URL and audio sample info
    return respond.with({
      signedUrl: data.signedUrl,
      fileName: audioSample.name,
      waveform,
    });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get user audio sample content");
  }
};

const uploadProfileImage = async (
  req: ValidatedRequest<{
    params: { name: string };
    file: Express.Multer.File;
  }>
) => {
  try {
    if (!req.file) {
      return respond.badRequest("No file uploaded");
    }
    if (!req.user?.id) {
      return respond.unauthorized("User not authenticated");
    }
    const user = await prisma.user.findUnique({
      where: {
        sbid: req.user.id,
      },
    });
    if (!user) {
      return respond.notFound("User not found");
    }
    const s3Path = encodePath(`${user.name}/profile/${req.file.originalname}`);
    // if the user already has a profile image, delete it
    if (user.profileImg) {
      const existingS3Path = encodePath(
        `${user.name}/profile/${user.profileImg.split("/").pop()}`
      );
      await sbClient.storage.from("users-public").remove([existingS3Path]);
    }
    // Upload the new profile image
    const publicUrl = (await uploadLargeFile(
      "users-public",
      s3Path,
      req.file,
      Number(user.id).toString()
    )) as string;

    if (!publicUrl) {
      return respond.error("Failed to upload profile image");
    }

    const updatedUser = await prisma.user.update({
      where: {
        sbid: user.sbid,
      },
      data: {
        profileImg: publicUrl,
        updatedAt: new Date(),
      },
    });
    return respond.with({
      profileImg: updatedUser.profileImg,
      message: "Profile image uploaded successfully",
    });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to upload profile image");
  }
};

type GetUserConversationsRequest = z.infer<typeof getUserConversationsSchema>;

const getUserConversations = async (
  req: ValidatedRequest<GetUserConversationsRequest>
) => {
  try {
    const { name } = req.params;
    const { page = 1, type } = req.query;

    const user = await prisma.user.findUnique({
      where: {
        name,
      },
    });

    if (!user) {
      return respond.notFound("User not found");
    }

    const pageSize = 10;
    const skip = (page - 1) * pageSize;

    const conversations = await prisma.conversation.findMany({
      where: {
        participants: {
          some: {
            userId: user.id,
          },
        },
        projectId:
          type === "linked"
            ? { not: null }
            : type === "created"
            ? null
            : undefined,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1, // Get the latest message
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                profileImg: true,
              },
            },
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
      skip,
      take: pageSize,
    });
    return respond.with({
      conversations: conversations,
      total: conversations.length,
      hasMore: conversations.length === pageSize,
    });
  } catch (error) {
    console.error(error);
    return respond.error("Failed to get user conversations");
  }
};

export const userController = {
  getAllUsers,
  getUserProjects,
  getUser,
  updateUser,
  deleteUser,
  searchUsers,
  getUserInvites,
  createLeaflet,
  getUserLeaflets,
  getUserConversations,
  getLeaflet,
  updateLeaflet,
  deleteLeaflet,
  getLeafletContent,
  uploadProfileImage,
};

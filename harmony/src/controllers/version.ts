// src/controllers/version.ts

import { Prisma, PrismaClient } from "@prisma/client";
import { z } from "zod";
import { ValidatedRequest } from "..";
import { client as sbClient } from "../apis/supabase"; // Importing Supabase client for file handling
import { respond } from "../middleware/response.handler";
import { encodePath } from "../utils/pathEncoding";
import {
  createVersionSchema,
  deleteVersionSchema,
  getAllFilesByVersionSchema,
  getAllFoldersByVersionSchema,
  getVersionSchema,
  updateVersionSchema,
} from "../validation/version";

const prisma = new PrismaClient();

type CreateVersionRequest = z.infer<typeof createVersionSchema>;
type GetVersionRequest = z.infer<typeof getVersionSchema>;
type GetAllFoldersByVersionRequest = z.infer<
  typeof getAllFoldersByVersionSchema
>;
type GetAllFilesByVersionRequest = z.infer<typeof getAllFilesByVersionSchema>;
type UpdateVersionRequest = z.infer<typeof updateVersionSchema>;
type DeleteVersionRequest = z.infer<typeof deleteVersionSchema>;

const createVersion = async (req: ValidatedRequest<CreateVersionRequest>) => {
  try {
    const { projectId, message } = req.body;

    // Get user and project in parallel
    const [author, project, versionCount] = await Promise.all([
      prisma.user.findUnique({
        where: { sbid: req.user?.id },
      }),
      prisma.project.findUnique({
        where: { id: Number(projectId) },
      }),
      prisma.version.count({
        where: { projectId: Number(projectId) },
      }),
    ]);

    if (!project) {
      return respond.notFound(`Project with id ${projectId} not found`);
    }

    // Create the new version
    const version = await prisma.version.create({
      data: {
        number: versionCount + 1,
        projectId: projectId,
        authorId: author?.id,
        message,
      },
    });

    // If this is the first version, just return it
    if (versionCount === 0) {
      return respond.with(version);
    }

    // Get previous version
    const previousVersion = await prisma.version.findFirst({
      where: {
        projectId: Number(projectId),
        number: versionCount,
      },
      include: {
        folders: true,
      },
    });

    if (!previousVersion) {
      return respond.with(version);
    }

    // Create a map to track folder ID mappings
    const folderIdMap = new Map<number, number>();

    // Get all folders from previous version and sort by depth
    const previousFolders = await prisma.folder.findMany({
      where: { versionId: previousVersion.id },
      orderBy: { depth: "asc" },
    });

    // Batch create folders using Prisma's createMany
    // First create all root folders (no parent)
    const rootFolders = previousFolders.filter((f) => !f.parentId);
    if (rootFolders.length > 0) {
      const createdRootFolders = await prisma.$transaction(
        rootFolders.map((folder) =>
          prisma.folder.create({
            data: {
              name: folder.name,
              versionId: Number(version.id),
              depth: folder.depth,
            },
          })
        )
      );

      // Map old IDs to new IDs
      rootFolders.forEach((folder, index) => {
        folderIdMap.set(
          Number(folder.id),
          Number(createdRootFolders[index].id)
        );
      });
    }

    // Then create child folders level by level
    for (
      let depth = 1;
      depth <= Math.max(...previousFolders.map((f) => f.depth), 0);
      depth++
    ) {
      const depthFolders = previousFolders.filter((f) => f.depth === depth);
      if (depthFolders.length > 0) {
        const createdDepthFolders = await prisma.$transaction(
          depthFolders.map((folder) =>
            prisma.folder.create({
              data: {
                name: folder.name,
                versionId: Number(version.id),
                parentId: folder.parentId
                  ? folderIdMap.get(Number(folder.parentId))
                  : null,
                depth: folder.depth,
              },
            })
          )
        );

        // Map old IDs to new IDs
        depthFolders.forEach((folder, index) => {
          folderIdMap.set(
            Number(folder.id),
            Number(createdDepthFolders[index].id)
          );
        });
      }
    }

    // Get all files from previous version
    const previousFiles = await prisma.file.findMany({
      where: { versionId: previousVersion.id },
    });

    // Process files in parallel batches of 10
    const batchSize = 10;
    const fileBatches = [];

    for (let i = 0; i < previousFiles.length; i += batchSize) {
      const batch = previousFiles.slice(i, i + batchSize);
      fileBatches.push(batch);
    }

    await Promise.all(
      fileBatches.map((batch) =>
        Promise.all(
          batch.map(async (file) => {
            try {
              // Map the folder ID to the new version's folder ID
              const newFolderId = file.folderId
                ? folderIdMap.get(Number(file.folderId)) || null
                : null;

              // Create new S3 path for the file in the new version
              const newS3Path = file.s3Path.replace(
                `/versions/${previousVersion.number}/`,
                `/versions/${version.number}/`
              );

              // Copy the file in Supabase Storage directly
              const { data, error } = await sbClient.storage
                .from("projects")
                .copy(file.s3Path, newS3Path);

              if (error) {
                console.error(`Error copying file ${file.name}:`, error);
                return;
              }

              // Create the file record in the database
              await prisma.file.create({
                data: {
                  name: file.name,
                  versionId: Number(version.id),
                  folderId: newFolderId,
                  s3Path: newS3Path,
                  fileType: file.fileType,
                  fileSize: file.fileSize,
                  metadata:
                    file.metadata === null ? Prisma.JsonNull : file.metadata,
                },
              });
            } catch (error) {
              console.error(`Error copying file ${file.name}:`, error);
            }
          })
        )
      )
    );

    // Update project timestamp
    await prisma.project.update({
      where: { id: Number(projectId) },
      data: { updatedAt: new Date() },
    });

    return respond.with(version);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to create version");
  }
};

const getVersion = async (req: ValidatedRequest<GetVersionRequest>) => {
  try {
    const { id } = req.params;

    const version = await prisma.version.findUnique({
      where: { id: Number(id) },
      include: {
        files: true,
      },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    return respond.with(version);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve version");
  }
};

const getAllFilesByVersion = async (
  req: ValidatedRequest<GetAllFilesByVersionRequest>
) => {
  try {
    const { id } = req.params;

    const whereClause: any = {
      versionId: Number(id), // This should filter files by versionId, not version by id
    };

    const version = await prisma.version.findUnique({
      where: { id: Number(id) },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const allFiles = await prisma.file.findMany({
      where: whereClause,
      include: {
        version: true,
        folder: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return respond.with(allFiles);
  } catch (error) {
    console.error(error);
    return respond.error(
      "Failed to retrieve all files for this version of the prjoect"
    );
  }
};

const getAllFoldersByVersion = async (
  req: ValidatedRequest<GetAllFoldersByVersionRequest>
) => {
  try {
    const { id } = req.params;

    // Get folders for the version, ordered hierarchically
    const folders = await prisma.folder.findMany({
      where: {
        versionId: Number(id),
      },
      include: {
        subFolders: true,
        files: true,
      },
    });
    return respond.with(folders);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to retrieve folders");
  }
};

const updateVersion = async (req: ValidatedRequest<UpdateVersionRequest>) => {
  try {
    const { id } = req.params;
    const { message } = req.body;

    const version = await prisma.version.findUnique({
      where: { id: Number(id) },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    const newUpdatedVersion = await prisma.version.update({
      where: { id: Number(id) },
      data: {
        message,
        updatedAt: new Date(), // Update the timestamp
      },
    });

    return respond.with(newUpdatedVersion);
  } catch (error) {
    console.error(error);
    return respond.error("Failed to update version");
  }
};

const deleteVersion = async (req: ValidatedRequest<DeleteVersionRequest>) => {
  try {
    const { id } = req.params;

    const version = await prisma.version.findUnique({
      where: { id: Number(id) },
    });

    if (!version) {
      return respond.notFound("Version not found");
    }

    // Delete s3 files
    const files = await prisma.file.findMany({
      where: { versionId: Number(id) },
    });

    for (const file of files) {
      const { error: deleteError } = await sbClient.storage
        .from("projects")
        .remove([encodePath(file.s3Path)]);

      if (deleteError) {
        console.error(
          `Error deleting file ${file.id} from Supabase Storage:`,
          deleteError.message
        );
      }
    }

    await prisma.version.delete({
      where: { id: Number(id) },
    });

    return respond.message("Version deleted successfully");
  } catch (error) {
    console.error(error);
    return respond.error("Failed to delete version");
  }
};

export const versionController = {
  createVersion,
  getVersion,
  updateVersion,
  deleteVersion,
  getAllFilesByVersion,
  getAllFoldersByVersion,
};

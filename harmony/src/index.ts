// harmony/src/index.ts

import {
  extendZodWithOpenApi,
  OpenApiGeneratorV3,
  OpenAPIRegistry,
} from "@asteasolutions/zod-to-openapi";
import { PrismaClient } from "@prisma/client";
import cookieParser from "cookie-parser";
import cors from "cors";
import * as dotenv from "dotenv";
import express, { Request, Response } from "express";
import fs from "fs";
import http from "http";
import https from "https";
import swaggerUi from "swagger-ui-express";
import { z } from "zod";
import { client as sbClient } from "./apis/supabase";
import { generateCsrfToken, verifyCsrf } from "./middleware/csrf";
import { Logger } from "./middleware/logger";
import { cleanupExpiredSessions } from "./utils/sessionCleanup";
import { initializeWebsockets } from "./websocket";

export type ValidatedRequest<
  T extends { params?: unknown; body?: unknown; query?: unknown }
> = Request<
  T["params"] extends object ? T["params"] : unknown,
  any,
  T["body"] extends object ? T["body"] : unknown,
  T["query"] extends object ? T["query"] : unknown
>;

extendZodWithOpenApi(z);

export const registry = new OpenAPIRegistry();

import { SecuritySchemeObject } from "openapi3-ts/oas30";
import { authenticateUser } from "./middleware/authenticator";
import {
  routesAuth,
  routesChat,
  routesComment,
  routesFile,
  routesFolder,
  routesProject,
  routesProjectInvite,
  routesTask,
  routesUser,
  routesVersion,
} from "./routes";

dotenv.config();

export const prisma = new PrismaClient();

// Add global type definition
declare global {
  namespace NodeJS {
    interface Global {
      supabaseSessionCache?: {
        accessToken: string;
        timestamp: number;
      };
    }
  }
}

// Initialize the cache
(global as NodeJS.Global).supabaseSessionCache = undefined;

// CORS Configuration
const ALLOWED_ORIGINS = [
  "http://localhost:3000", // Local development
  "http://localhost:3200", // Local development
  "https://localhost:3000", // Local development with HTTPS
  "https://localhost:3200", // Local development with HTTPS
  "https://your-frontend-app.com", // Production frontend
  "https://staging.your-app.com", // Staging environment,
  process.env.FRONTEND_URL,
];

const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (
      ALLOWED_ORIGINS.indexOf(origin) !== -1 ||
      process.env.NODE_ENV === "development"
    ) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "X-CSRF-Token", // Add this header
  ],
  credentials: true, // Allow credentials (cookies, authorization headers, etc)
  maxAge: 86400, // Cache preflight requests for 24 hours
};

const app = express();

async function main() {
  const logger = new Logger();
  const port = process.env.PORT || 3200;

  app.use(express.json({ limit: "100mb" }));
  app.use(express.urlencoded({ limit: "100mb", extended: true }));
  app.use(cors(corsOptions));
  app.use(cookieParser()); // Add this line to parse cookies
  app.use(logger.middleware());

  app.get("/csrf-token", (req, res) => {
    const token = generateCsrfToken(req, res);
    res.json({ csrfToken: token });
  });

  app.use("/auth", routesAuth); // No CSRF for auth routes
  app.use("/chat", authenticateUser, verifyCsrf, routesChat);
  app.use("/comments", authenticateUser, verifyCsrf, routesComment);
  app.use("/projects", authenticateUser, verifyCsrf, routesProject);
  app.use("/users", authenticateUser, verifyCsrf, routesUser);
  app.use("/tasks", authenticateUser, verifyCsrf, routesTask);
  app.use("/versions", authenticateUser, verifyCsrf, routesVersion);
  app.use("/files", authenticateUser, verifyCsrf, routesFile);
  app.use("/folders", authenticateUser, verifyCsrf, routesFolder);
  app.use(
    "/project-invites",
    authenticateUser,
    verifyCsrf,
    routesProjectInvite
  );

  const generator = new OpenApiGeneratorV3(registry.definitions);
  const docs = generator.generateDocument({
    info: {
      title: "Harmony API",
      version: "1.0.0",
    },
    servers: [
      {
        url: "http://localhost:3200",
      },
    ],
    openapi: "3.0.0",
  });

  const initAuth = async () => {
    const {
      data: { session },
    } = await sbClient.auth.getSession();
    if (session) {
      console.log("Session exists:", session);
    }
    const {
      data: { user },
    } = await sbClient.auth.getUser();
    if (user) {
      console.log("User exists:", user);
    }
    await sbClient.auth.refreshSession();
  };

  app.use(
    "/docs",
    swaggerUi.serve,
    swaggerUi.setup({
      ...docs,
      components: {
        securitySchemes: {
          BearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          } as SecuritySchemeObject,
        },
      },
    })
  );

  app.get("/", (req: Request, res: Response) => {
    res.send("Not found.");
  });

  const isDevHttps =
    process.env.NODE_ENV === "development" && process.env.HTTPS === "true";

  let options: any = {};

  if (isDevHttps) {
    options = {
      key: fs.readFileSync(process.env.SSL_KEY_FILE!),
      cert: fs.readFileSync(process.env.SSL_CRT_FILE!),
    };
  }

  const server = isDevHttps
    ? https.createServer(options, app)
    : http.createServer(app);
  initializeWebsockets(server);

  // Clean up expired sessions on startup and every 24 hours
  cleanupExpiredSessions();
  setInterval(cleanupExpiredSessions, 24 * 60 * 60 * 1000);

  server.listen(port, () => {
    console.log("Initializing Supabase Auth...");
    initAuth()
      .then(() => {
        console.log("Supabase Auth initialized.");
      })
      .catch((error) => {
        console.error("Error initializing Supabase Auth:", error);
      });
    console.log(`App listening on port ${port}.`);
    console.log(`Docs available at /docs`);
  });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    // Wait for logs to flush
    // process.exit(1);
  });

export default app;

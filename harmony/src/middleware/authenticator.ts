// src/middleware/authenticator.ts

import { User } from "@supabase/supabase-js";
import { NextFunction, Request, Response } from "express";
import {
  client,
  getSupabaseClient,
  getUserIdFromToken,
  sbRefreshSession,
} from "../apis/supabase";
import { generateCsrfToken } from "./csrf";

// Extend Express Request type to include user and userId
declare global {
  namespace Express {
    interface Request {
      user?: User;
      userId?: string;
    }
  }
}

// Helper function to check if a JWT token is expired
function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const expiryTime = payload.exp * 1000; // Convert to milliseconds
    return Date.now() >= expiryTime;
  } catch (e) {
    console.error("Error checking token expiry:", e);
    return true; // If we can't parse the token, assume it's expired
  }
}

export const authenticateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // First check for cookie
    const token = req.cookies?.auth_token;

    // Fall back to Authorization header if no cookie
    const authHeader = req.headers.authorization;
    let authToken = token;

    if (!authToken && authHeader) {
      // Check for proper Bearer token format
      if (!authHeader.startsWith("Bearer ")) {
        res.status(401).json({
          error: "Invalid authorization format",
          message: "Authorization header must start with 'Bearer '",
        });
        return;
      }

      authToken = authHeader.split(" ")[1];
    }

    // If we have a token, extract the user ID and use it for the client
    let userId: string | null = null;
    if (authToken) {
      userId = getUserIdFromToken(authToken);
    }

    // Use the appropriate client based on user ID
    const supabase = userId ? getSupabaseClient(userId) : client;

    // If we have a token, check if it's expired
    if (authToken && isTokenExpired(authToken) && userId) {
      console.log("Token expired, attempting to refresh...");
      const { data: refreshData, error: refreshError } = await sbRefreshSession(
        userId
      );

      if (refreshError || !refreshData.session) {
        console.error("[Auth Refresh Error]:", refreshError);
        res.status(401).json({
          error: "Authentication failed",
          message: "Session expired. Please log in again.",
          redirectUrl: "/login",
        });
        return;
      }

      // Extract user ID from the new token
      const newUserId = getUserIdFromToken(refreshData.session.access_token);

      // Set new token in cookie
      res.cookie("auth_token", refreshData.session.access_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      // Update refresh token cookie as well
      res.cookie("refresh_token", refreshData.session.refresh_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      // Generate and set a new CSRF token
      const csrfToken = generateCsrfToken(req, res);

      // Use the refreshed session's user
      if (!newUserId) {
        console.error("[Auth Refresh Error]: No user ID found");
        res.status(401).json({
          error: "Authentication failed",
          message: "Invalid token",
          redirectUrl: "/login",
        });
        return;
      }

      req.user = refreshData.session.user;
      req.userId = newUserId;
      next();
      return;
    }

    if (!authToken) {
      // Try to get session from Supabase directly
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        res.status(401).json({
          error: "Authentication required",
          message: "No valid authentication found",
          redirectUrl: "/login",
        });
        return;
      }

      // Extract user ID from the token
      const sessionUserId = getUserIdFromToken(data.session.access_token);

      // Check if the session token is expired
      if (isTokenExpired(data.session.access_token) && sessionUserId) {
        console.log(
          "Persisted session token expired, attempting to refresh..."
        );
        const { data: refreshData, error: refreshError } =
          await sbRefreshSession(sessionUserId);

        if (refreshError || !refreshData.session) {
          console.error("[Auth Refresh Error]:", refreshError);
          res.status(401).json({
            error: "Authentication failed",
            message: "Session expired. Please log in again.",
            redirectUrl: "/login",
          });
          return;
        }

        // Extract user ID from the new token
        const newUserId = getUserIdFromToken(refreshData.session.access_token);

        // Set new token in cookie
        res.cookie("auth_token", refreshData.session.access_token, {
          httpOnly: true,
          secure: true,
          sameSite: "none",
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });

        // Update refresh token cookie as well
        res.cookie("refresh_token", refreshData.session.refresh_token, {
          httpOnly: true,
          secure: true,
          sameSite: "none",
          maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        });

        // Generate and set a new CSRF token
        const csrfToken = generateCsrfToken(req, res);

        // Use the refreshed session's user
        if (!newUserId) {
          console.error("[Auth Refresh Error]: No user ID found");
          res.status(401).json({
            error: "Authentication failed",
            message: "Invalid token",
            redirectUrl: "/login",
          });
          return;
        }

        req.user = refreshData.session.user;
        req.userId = newUserId;
        next();
        return;
      }

      // Set the session token in cookie for future requests
      res.cookie("auth_token", data.session.access_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      // Generate and set a new CSRF token
      const csrfToken = generateCsrfToken(req, res);

      // Use the session's user
      if (!sessionUserId) {
        console.error("[Auth SB Error]: No user ID found");
        res.status(401).json({
          error: "Authentication failed",
          message: "Invalid token",
          redirectUrl: "/login",
        });
        return;
      }

      req.user = data.session.user;
      req.userId = sessionUserId;
      next();
      return;
    }

    // Verify the token with Supabase
    const { data, error } = await supabase.auth.getUser(authToken);

    if (error && userId) {
      // Token might be expired, try to refresh
      const { data: refreshData, error: refreshError } = await sbRefreshSession(
        userId
      );

      if (refreshError || !refreshData.session) {
        console.error("[Auth Refresh Error]:", refreshError);
        res.status(401).json({
          error: "Authentication failed",
          message: "Session expired. Please log in again.",
          redirectUrl: "/login",
        });
        return;
      }

      // Extract user ID from the new token
      const newUserId = getUserIdFromToken(refreshData.session.access_token);

      // Set new token in cookie
      res.cookie("auth_token", refreshData.session.access_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      // Update refresh token cookie as well
      res.cookie("refresh_token", refreshData.session.refresh_token, {
        httpOnly: true,
        secure: true,
        sameSite: "none",
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      // Generate and set a new CSRF token
      const csrfToken = generateCsrfToken(req, res);

      // Use the refreshed session's user
      if (!newUserId) {
        console.error("[Auth Refresh Error]: No user ID found");
        res.status(401).json({
          error: "Authentication failed",
          message: "Invalid token",
          redirectUrl: "/login",
        });
        return;
      }

      req.user = refreshData.session.user;
      req.userId = newUserId;
      next();
      return;
    }

    if (!data.user) {
      console.error("[Auth SB Error]: No user found");
      res.status(401).json({
        error: "Authentication failed",
        message: "Invalid token",
        redirectUrl: "/login",
      });
      return;
    }

    // Generate and set a new CSRF token on successful authentication
    const csrfToken = generateCsrfToken(req, res);

    // Add the user object to the request
    if (!userId) {
      console.error("[Auth SB Error]: No user ID found");
      res.status(401).json({
        error: "Authentication failed",
        message: "Invalid token",
        redirectUrl: "/login",
      });
      return;
    }

    req.user = data.user;
    req.userId = userId;
    next();
  } catch (error) {
    console.error("[Auth Middleware Error]:", error);
    res.status(500).json({
      error: "Internal server error",
      message: "An error occurred during authentication",
    });
  }
};

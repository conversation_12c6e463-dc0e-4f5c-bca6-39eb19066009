import crypto from "crypto";
import { NextFunction, Request, Response } from "express";

// Generate a CSRF token
export const generateCsrfToken = (req: Request, res: Response) => {
  const token = crypto.randomBytes(16).toString("hex");

  const domain = process.env.COOKIE_DOMAIN || undefined;

  // Try to set the cookie as usual
  res.cookie("csrf_token", token, {
    httpOnly: false, // Readable by JavaScript
    secure: true,
    sameSite: "none", // Try "lax" instead of "none"
    maxAge: 24 * 60 * 60 * 1000, // 1 day
    domain: domain,
    path: "/",
  });

  // Also return the token in the response body for localStorage fallback
  return token;
};

// Middleware to verify CSRF token
export const verifyCsrf = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Skip for GET, HEAD, OPTIONS requests (they should be safe)
  if (["GET", "HEAD", "OPTIONS"].includes(req.method)) {
    next();
    return;
  }

  const csrfCookie = req.cookies.csrf_token;
  const csrfHeader = req.headers["x-csrf-token"];

  if (!csrfHeader) {
    res.status(403).json({
      error: "CSRF verification failed",
      message: "Missing CSRF token in header",
    });
    return;
  }

  // If cookie exists, verify it matches the header
  // If cookie doesn't exist (cookies disabled), just check that header is present
  if (csrfCookie && csrfCookie !== csrfHeader) {
    res.status(403).json({
      error: "CSRF verification failed",
      message: "Invalid CSRF token",
    });
    return;
  }

  next();
};

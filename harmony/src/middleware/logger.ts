// src/middleware/logger.ts

import chalk from "chalk";
import { NextFunction, Request, Response } from "express";

interface AxiomConfig {
  apiToken: string;
  dataset: string;
  enabled: boolean;
}

export class Logger {
  private axiomConfig: AxiomConfig;

  constructor(axiomConfig?: Partial<AxiomConfig>) {
    this.axiomConfig = {
      apiToken: process.env.AXIOM_API_TOKEN || "",
      dataset: process.env.AXIOM_DATASET || "harmony_logs",
      enabled: process.env.AXIOM_ENABLED === "true",
      ...axiomConfig,
    };
  }

  private async sendToAxiom(logData: any) {
    if (!this.axiomConfig.enabled) return;

    try {
      const response = await fetch(
        `https://api.axiom.co/v1/datasets/${this.axiomConfig.dataset}/ingest`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.axiomConfig.apiToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify([logData]),
        }
      );

      if (!response.ok) {
        console.error(
          chalk.red(`Failed to send logs to Axiom: ${response.statusText}`)
        );
      }
    } catch (error) {
      console.error(chalk.red("Error sending logs to Axiom:"), error);
    }
  }

  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();

      // Store the original res.end to modify it later
      const originalEnd = res.end;
      let statusCode: number = 500;

      // Override res.end to capture the status code
      res.end = function (chunk?: any, encoding?: any, callback?: any): any {
        statusCode = res.statusCode;
        originalEnd.call(this, chunk, encoding, callback);
      };

      try {
        await new Promise((resolve) => {
          res.on("finish", resolve);
          next();
        });

        const responseTime = Date.now() - startTime;
        const timestamp = new Date().toISOString();
        const method = req.method.padEnd(6);

        // Get the full API route with actual parameter values
        const baseUrl = req.baseUrl || "";
        let routePath = req.route?.path || req.path || req.url;

        // Replace route parameters with actual values
        if (routePath && req.params && Object.keys(req.params).length > 0) {
          Object.entries(req.params).forEach(([key, value]) => {
            routePath = routePath.replace(`:${key}`, value as string);
          });
        }

        const fullRoute = baseUrl + routePath;
        const status = statusCode?.toString() || "???";
        const time = `${responseTime}ms`.padStart(5);

        // Color the status code based on the response
        let coloredStatus = status;
        if (statusCode) {
          if (statusCode < 300) coloredStatus = chalk.green(status);
          else if (statusCode < 400) coloredStatus = chalk.cyan(status);
          else if (statusCode < 500) coloredStatus = chalk.yellow(status);
          else coloredStatus = chalk.red(status);
        }

        // Prepare log data for Axiom
        const logData = {
          timestamp,
          method: req.method,
          path: fullRoute,
          statusCode,
          responseTime,
          query: req.query,
          body: req.body,
          error: statusCode >= 400 ? res.statusMessage : undefined,
        };

        // Send to Axiom
        await this.sendToAxiom(logData);

        // Continue with console logging
        console.log(
          `${chalk.gray(`[${timestamp}]`)} ${chalk.bold(
            method
          )} ${fullRoute} ${coloredStatus} ${chalk.blue(time)}`
        );

        // Log error details if the response status is 4xx or 5xx
        if (statusCode >= 400) {
          console.log(chalk.red("  Error:"), res.statusMessage);
        }

        // Optionally log query parameters if they exist
        if (Object.keys(req.query).length > 0) {
          console.log(chalk.gray("  Query:"), req.query);
        }

        // Optionally log request body if it exists
        if (req.body && Object.keys(req.body).length > 0) {
          console.log(chalk.gray("  Body:"), req.body);
        }
      } catch (error) {
        console.error(
          chalk.red("Error:"),
          error instanceof Error ? error.message : "Unknown error"
        );
        next(error);
      }
    };
  }

  static async log(message: string, level: "info" | "warn" | "error" = "info") {
    const timestamp = new Date().toISOString();

    // Prepare log data for Axiom
    const logData = {
      timestamp,
      level,
      message,
    };

    // If using static method, create temporary instance to send to Axiom
    const logger = new Logger();
    await logger.sendToAxiom(logData);

    // Continue with console logging
    switch (level) {
      case "info":
        console.log(
          `${chalk.gray(timestamp)} ${chalk.blue("INFO")} ${message}`
        );
        break;
      case "warn":
        console.log(
          `${chalk.gray(timestamp)} ${chalk.yellow("WARN")} ${message}`
        );
        break;
      case "error":
        console.log(
          `${chalk.gray(timestamp)} ${chalk.red("ERROR")} ${message}`
        );
        break;
    }
  }
}

// harmony/src/middleware/response.handler.ts

import { NextFunction, Request, Response } from "express";
import { ValidatedRequest } from ".."; // Assuming ValidatedRequest is imported correctly
import { fileMiddleware, folderMiddleware } from "./upload.handler";

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  status?: number;
  error?: string | Error;
}

// Add a new type to indicate streaming responses
export interface StreamingResponse {
  isStreaming: boolean;
}

// Extend ValidatedRequest to include res property for cookie handling
declare global {
  namespace Express {
    interface Request {
      res?: Response;
    }
  }
}

// Type for controller functions with a single schema type argument
export type ControllerFunction<T> = (
  req: ValidatedRequest<
    T extends { params?: unknown; body?: unknown; query?: unknown } ? T : any
  >
) => Promise<ApiResponse | StreamingResponse> | ApiResponse | StreamingResponse;

// Response handler middleware creator - updated to attach res to req
export const handler = <T>(controller: ControllerFunction<T>) => {
  return async (
    req: ValidatedRequest<
      T extends { params?: unknown; body?: unknown; query?: unknown } ? T : any
    >,
    res: Response,
    next: NextFunction
  ) => {
    try {
      // Attach res to req for cookie handling
      req.res = res;

      const rawResult = await controller(req);
      // If it's a streaming response, return early as the controller handles the response
      if ("isStreaming" in rawResult && rawResult.isStreaming) {
        return;
      }

      // Cast result to ApiResponse to access its properties
      const result = rawResult as ApiResponse;

      // Handle normal API responses
      const statusCode = result.status || (result.error ? 400 : 200);
      const response = {
        success: !result.error,
        data: result.data,
        message: result.message,
        error:
          result.error instanceof Error ? result.error.message : result.error,
      };
      res.status(statusCode).json(formatResponse(response));
    } catch (error) {
      next(error);
    }
  };
};

// Global error handling middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error("Error:", err);

  // Determine if this is a known error type or an unknown error
  const statusCode = "status" in err ? (err as any).status : 500;
  const message = err.message || "Internal server error";

  const response = {
    success: false,
    error:
      process.env.NODE_ENV === "production" && statusCode === 500
        ? "Internal server error"
        : message,
    status: statusCode,
  };

  res.status(statusCode).json(response);
};

// Helper functions to create standardized responses
export const respond = {
  with: <T>(data: T, status = 200): ApiResponse<T> => ({
    data,
    status,
  }),

  message: (message: string, status = 200): ApiResponse => ({
    message,
    status,
  }),

  error: (error: string | Error, status = 400): ApiResponse => ({
    error,
    status,
  }),

  created: <T>(data: T, message = "Resource created"): ApiResponse<T> => ({
    data,
    message,
    status: 201,
  }),

  notFound: (message = "Resource not found"): ApiResponse => ({
    error: message,
    status: 404,
  }),

  badRequest: (message = "Bad request"): ApiResponse => ({
    error: message,
    status: 400,
  }),

  unauthorized: (message = "Unauthorized"): ApiResponse => ({
    error: message,
    status: 401,
  }),
};

function convertBigIntsToNumbers(obj: any): any {
  if (typeof obj === "bigint") {
    return Number(obj); // Convert BigInt to number
  }

  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (obj instanceof Date) {
    return obj; // Ensure dates are not modified
  }

  if (Array.isArray(obj)) {
    return obj.map(convertBigIntsToNumbers);
  }

  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      convertBigIntsToNumbers(value),
    ])
  );
}
function formatResponse(obj: any): any {
  const formatted = convertBigIntsToNumbers(obj);
  return formatted;
}

export const withFileUpload = <T>(controller: ControllerFunction<T>) => {
  return async (
    req: ValidatedRequest<
      T extends { params?: unknown; body?: unknown; query?: unknown } ? T : any
    >,
    res: Response,
    next: NextFunction
  ) => {
    // Apply multer middleware first
    fileMiddleware(req as Request, res, async (err) => {
      if (err) {
        const response = {
          success: false,
          error: err.message,
          status: 400,
        };
        return res.status(400).json(formatResponse(response));
      }

      try {
        // Now req.file is available, proceed with controller
        const result = (await controller(req as any)) as ApiResponse;
        const statusCode = result.status || (result.error ? 400 : 200);
        const response = {
          success: !result.error,
          data: result.data,
          message: result.message,
          error:
            result.error instanceof Error ? result.error.message : result.error,
        };
        res.status(statusCode).json(formatResponse(response));
      } catch (error) {
        next(error);
      }
    });
  };
};

export const withFolderUpload = <T>(controller: ControllerFunction<T>) => {
  return async (
    req: ValidatedRequest<
      T extends { params?: unknown; body?: unknown; query?: unknown } ? T : any
    >,
    res: Response,
    next: NextFunction
  ) => {
    // Apply folder middleware first (uses multer's array upload)
    folderMiddleware(req as Request, res, async (err) => {
      if (err) {
        const response = {
          success: false,
          error: err.message,
          status: 400,
        };
        return res.status(400).json(formatResponse(response));
      }

      try {
        // Now req.files is available, proceed with controller
        const result = (await controller(req as any)) as ApiResponse;
        const statusCode = result.status || (result.error ? 400 : 200);
        const response = {
          success: !result.error,
          data: result.data,
          message: result.message,
          error:
            result.error instanceof Error ? result.error.message : result.error,
        };
        res.status(statusCode).json(formatResponse(response));
      } catch (error) {
        next(error);
      }
    });
  };
};

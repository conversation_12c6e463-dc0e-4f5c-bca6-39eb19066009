// harmony/src/middleware/upload.handler.ts

import fs from "fs";
import multer from "multer";
import os from "os";
import path from "path";

// Configure storage to use disk instead of memory
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Create a temporary directory for uploads
    const tempDir = path.join(os.tmpdir(), "harmony-uploads");
    fs.mkdirSync(tempDir, { recursive: true });
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // Generate a unique filename
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1e9)}-${
      file.originalname
    }`;
    cb(null, uniqueName);
  },
});

// Create the multer instance with configuration
export const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit for each file
  },
});

// Single file upload middleware
export const fileMiddleware = upload.single("file");

// Folder upload middleware
export const folderMiddleware = upload.array("files", 30);

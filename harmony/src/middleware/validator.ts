// harmony/src/middleware/validator.ts

import { NextFunction, Request, Response } from "express";
import { AnyZodObject, z, ZodObject } from "zod";

// Original ValidateTarget type
type ValidateTarget = ZodObject<{
  body?: AnyZodObject;
  query?: AnyZodObject;
  params?: AnyZodObject;
}>;

// Create validation middleware factory
export const validate = (schema: ValidateTarget) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { body, query, params } = schema.shape;

      if (body) {
        req.body = await body.parseAsync(req.body);
      }
      if (query) {
        req.query = await query.parseAsync(req.query);
      }
      if (params) {
        req.params = await params.parseAsync(req.params);
      }
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          status: "error",
          message: "Validation failed",
          errors: error.errors.map((err) => ({
            path: err.path.join("."),
            message: err.message,
          })),
        });
      } else {
        res.status(500).json({
          status: "error",
          message: "Internal server error",
        });
      }
    }
  };
};

// Helper to create a properly typed schema
export const createRequestSchema = <T extends Record<string, AnyZodObject>>(
  shape: T
) => {
  return z.object(shape);
};

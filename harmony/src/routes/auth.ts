import { Router } from "express";
import { registry } from "..";
import {
  login,
  loginLimiter,
  logout,
  refreshToken,
  register,
  registerLimiter,
} from "../controllers/auth";
import { handler } from "../middleware/response.handler";

const router = Router();

registry.registerPath({
  tags: ["Auth"],
  method: "post",
  path: "/auth/login",
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            email: { type: "string" },
            password: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful login",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              user: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  email: { type: "string" },
                  // Add other user properties here
                },
              },
              token: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.post("/login", loginLimiter, handler(login));

registry.registerPath({
  tags: ["Auth"],
  method: "post",
  path: "/auth/logout",
  responses: {
    200: {
      description: "Successful logout",
    },
  },
});

router.post("/logout", handler(logout));

registry.registerPath({
  tags: ["Auth"],
  method: "post",
  path: "/auth/register",
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            email: { type: "string" },
            password: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful registration",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              user: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  email: { type: "string" },
                  // Add other user properties here
                },
              },
              token: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.post("/register", registerLimiter, handler(register));

// Add a new route for token refresh
router.post("/refresh", handler(refreshToken));

export default router;

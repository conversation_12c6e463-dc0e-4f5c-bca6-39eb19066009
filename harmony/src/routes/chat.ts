import { registry } from "@/.";
import { chat<PERSON>ontroller } from "@/controllers/chat";
import { handler } from "@/middleware/response.handler";
import { validate } from "@/middleware/validator";
import {
  createConversationSchema,
  createMessageSchema,
  deleteConversationSchema,
  deleteMessageSchema,
  getConversationSchema,
  getMessageSchema,
  updateConversationSchema,
  updateMessageSchema,
} from "@/validation/chat";
import { Router } from "express";

const router = Router();

const conversationController = chatController.conversationController;
const messageController = chatController.messageController;

// Conversation Routes

registry.registerPath({
  tags: ["Conversation"],
  method: "post",
  path: "/chat/conversations",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string", minLength: 1 },
            description: { type: "string", minLength: 1 },
            projectId: { type: "number", nullable: true }, // Assuming projectId can be null
            participantIds: {
              type: "array",
              items: { type: "integer", minimum: 1 },
              nullable: true,
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Conversation created",
    },
  },
});
router.post(
  "/conversations",
  validate(createConversationSchema),
  handler(conversationController.createConversation)
);

registry.registerPath({
  tags: ["Conversation"],
  method: "get",
  path: "/chat/conversations/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            page: { type: "integer", default: 1 },
            pageSize: { type: "integer", default: 20 },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Get conversation by ID",
    },
  },
});
router.get(
  "/conversations/:id",
  validate(getConversationSchema),
  handler(conversationController.getConversation)
);

registry.registerPath({
  tags: ["Conversation"],
  method: "put",
  path: "/chat/conversations/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string", minLength: 1, nullable: true },
            description: { type: "string", minLength: 1, nullable: true },
            projectId: { type: "number", nullable: true },
            participantIds: {
              type: "array",
              items: { type: "integer", minimum: 1 },
              nullable: true,
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Update conversation",
    },
  },
});
router.put(
  "/conversations/:id",
  validate(updateConversationSchema),
  handler(conversationController.updateConversation)
);

registry.registerPath({
  tags: ["Conversation"],
  method: "delete",
  path: "/chat/conversations/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  responses: {
    200: {
      description: "Delete conversation",
    },
  },
});
router.delete(
  "/conversations/:id",
  validate(deleteConversationSchema),
  handler(conversationController.deleteConversation)
);

// Message Routes

registry.registerPath({
  tags: ["Message"],
  method: "post",
  path: "/chat/messages",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            conversationId: { type: "string", minLength: 1 },
            content: { type: "string", minLength: 1 },
            projectId: { type: "number", nullable: true },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Message created",
    },
  },
});
router.post(
  "/messages",
  validate(createMessageSchema),
  handler(messageController.createMessage)
);

registry.registerPath({
  tags: ["Message"],
  method: "get",
  path: "/chat/messages/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  responses: {
    200: {
      description: "Get message by ID",
    },
  },
});
router.get(
  "/messages/:id",
  validate(getMessageSchema),
  handler(messageController.getMessage)
);

registry.registerPath({
  tags: ["Message"],
  method: "put",
  path: "/chat/messages/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            content: { type: "string", minLength: 1, nullable: true },
            projectId: { type: "number", nullable: true },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Update message",
    },
  },
});
router.put(
  "/messages/:id",
  validate(updateMessageSchema),
  handler(messageController.updateMessage)
);

registry.registerPath({
  tags: ["Message"],
  method: "delete",
  path: "/chat/messages/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  responses: {
    200: {
      description: "Delete message",
    },
  },
});
router.delete(
  "/messages/:id",
  validate(deleteMessageSchema),
  handler(messageController.deleteMessage)
);

// Unread Messages

registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/chat/unread",
  security: [{ BearerAuth: [] }],
  responses: {
    200: {
      description: "Get unread messages count",
    },
  },
});

router.get("/unread", handler(conversationController.getUnreadMessagesCount));

export default router;

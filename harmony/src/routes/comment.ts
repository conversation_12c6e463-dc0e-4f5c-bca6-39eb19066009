import { Router } from "express";
import { registry } from "..";
import { commentController } from "../controllers/comment";
import { handler } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  createCommentSchema,
  deleteCommentSchema,
  getCommentSchema,
  updateCommentSchema,
} from "../validation/comment";

const router = Router();

// Create Comment
registry.registerPath({
  tags: ["Comment"],
  method: "post",
  path: "/comments",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            content: { type: "string" },
            projectId: { type: "number" }, // Assuming projectId is a number
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful comment creation",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  id: { type: "number" }, // Assuming ID is a number
                  content: { type: "string" },
                  projectId: { type: "number" }, // Assuming projectId is a number
                  authorId: { type: "number" }, // Assuming authorId is a number
                  createdAt: { type: "string", format: "date-time" }, // ISO date string
                },
              },
            },
          },
        },
      },
    },
  },
});

router.post(
  "/",
  validate(createCommentSchema),
  handler(commentController.createComment)
);

// Get Comment
registry.registerPath({
  tags: ["Comment"],
  method: "get",
  path: "/comments/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number", // Assuming ID is a number
      },
    },
  ],
  responses: {
    200: {
      description: "Successful retrieval of a comment",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  content: { type: "string" },
                  projectId: { type: "number" },
                  authorId: { type: "number" },
                  createdAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id",
  validate(getCommentSchema),
  handler(commentController.getComment)
);

// Update Comment
registry.registerPath({
  tags: ["Comment"],
  method: "put",
  path: "/comments/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number", // Assuming ID is a number
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            content: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful comment update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  content: { type: "string" },
                  projectId: { type: "number" },
                  authorId: { type: "number" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" }, // ISO date string
                },
              },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/:id",
  validate(updateCommentSchema),
  handler(commentController.updateComment)
);

// Delete Comment
registry.registerPath({
  tags: ["Comment"],
  method: "delete",
  path: "/comments/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number", // Assuming ID is a number
      },
    },
  ],
  responses: {
    200: {
      description: "Successful comment deletion",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.delete(
  "/:id",
  validate(deleteCommentSchema),
  handler(commentController.deleteComment)
);

export default router;

// harmony/src/routes/file.ts

import { Router } from "express";
import { registry } from "..";
import { file<PERSON>ontroller } from "../controllers/file";
import { handler, withFileUpload } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  deleteFileSchema,
  getFileContentsSchema,
  getFileSchema,
  updateFileSchema,
} from "../validation/file";

const router = Router();

registry.registerPath({
  tags: ["File"],
  method: "post",
  path: "/files",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            versionId: { type: "number" },
            folderId: { type: "number" },
            s3Path: { type: "string" },
            fileType: { type: "string" },
            fileSize: { type: "number" },
            metadata: { type: "object" },
          },
          required: ["name", "versionId", "s3Path", "fileType", "fileSize"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful file creation",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              file: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  folderId: { type: "number" },
                  s3Path: { type: "string" },
                  fileType: { type: "string" },
                  fileSize: { type: "number" },
                  metadata: { type: "object" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.post("/", withFileUpload(fileController.createFile));

registry.registerPath({
  tags: ["File"],
  method: "get",
  path: "/files/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful file retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              file: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  folderId: { type: "number" },
                  s3Path: { type: "string" },
                  fileType: { type: "string" },
                  fileSize: { type: "number" },
                  metadata: { type: "object" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get("/:id", validate(getFileSchema), handler(fileController.getFile));

registry.registerPath({
  tags: ["File"],
  method: "put",
  path: "/files/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            fileType: { type: "string" },
            fileSize: { type: "number" },
            folderId: { type: "number" },
            metadata: { type: "object" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful file update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              file: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  folderId: { type: "number" },
                  s3Path: { type: "string" },
                  fileType: { type: "string" },
                  fileSize: { type: "number" },
                  metadata: { type: "object" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/:id",
  validate(updateFileSchema),
  handler(fileController.updateFile)
);

registry.registerPath({
  tags: ["File"],
  method: "delete",
  path: "/files/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful file deletion",
    },
  },
});

router.delete(
  "/:id",
  validate(deleteFileSchema),
  handler(fileController.deleteFile)
);

registry.registerPath({
  tags: ["File"],
  method: "get",
  path: "/files/{id}/contents",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "File contents retrieved successfully",
      content: {
        "application/octet-stream": {
          schema: {
            type: "string",
            format: "binary",
          },
        },
      },
    },
    404: {
      description: "File not found",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/contents",
  validate(getFileContentsSchema),
  handler(async (req) => {
    const response = await fileController.getFileContents(req);
    return response || { status: 200 };
  })
);

export default router;

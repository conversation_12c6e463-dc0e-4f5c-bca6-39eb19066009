import { Router } from "express";
import { registry } from "..";
import { folderController } from "../controllers/folder";
import { handler, withFolderUpload } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  createFolderSchema,
  deleteFolderSchema,
  getFolderAncestorsSchema,
  getFolderContentsSchema,
  getFolderSchema,
  updateFolderSchema,
} from "../validation/folder";

const router = Router();

// Create folder
registry.registerPath({
  tags: ["Folder"],
  method: "post",
  path: "/folders",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            versionId: { type: "number" },
            parentId: { type: "number" },
          },
          required: ["name", "versionId"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful folder creation",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  parentId: { type: "number" },
                  depth: { type: "number" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.post(
  "/",
  validate(createFolderSchema),
  handler(folderController.createFolder)
);

registry.registerPath({
  tags: ["Folder"],
  method: "post",
  path: "/folders/upload",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "multipart/form-data": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            versionId: { type: "number" },
            parentId: { type: "number" },
            paths: {
              type: "array",
              items: { type: "string" },
            },
            files: {
              type: "array",
              items: {
                type: "string",
                format: "binary",
              },
            },
          },
          required: ["name", "versionId"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful folder upload",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  folder: {
                    type: "object",
                    properties: {
                      id: { type: "number" },
                      name: { type: "string" },
                      versionId: { type: "number" },
                      parentId: { type: "number" },
                      depth: { type: "number" },
                      createdAt: { type: "string", format: "date-time" },
                      updatedAt: { type: "string", format: "date-time" },
                    },
                  },
                  files: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "number" },
                        name: { type: "string" },
                        versionId: { type: "number" },
                        folderId: { type: "number" },
                        s3Path: { type: "string" },
                        fileType: { type: "string" },
                        fileSize: { type: "number" },
                        metadata: { type: "object" },
                        createdAt: { type: "string", format: "date-time" },
                        updatedAt: { type: "string", format: "date-time" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.post(
  "/upload",
  withFolderUpload(folderController.uploadFolder) // Cleaner approach
);

// Get folder by ID
registry.registerPath({
  tags: ["Folder"],
  method: "get",
  path: "/folders/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful folder retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  parentId: { type: "number" },
                  depth: { type: "number" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                  subFolders: { type: "array" },
                  files: { type: "array" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id",
  validate(getFolderSchema),
  handler(folderController.getFolder)
);

// Get folder ancestors
registry.registerPath({
  tags: ["Folder"],
  method: "get",
  path: "/folders/{id}/ancestors",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful folder ancestors retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "number" },
                    name: { type: "string" },
                    versionId: { type: "number" },
                    parentId: { type: "number" },
                    depth: { type: "number" },
                    createdAt: { type: "string", format: "date-time" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/ancestors",
  validate(getFolderAncestorsSchema),
  handler(folderController.getFolderAncestors)
);

// Update folder
registry.registerPath({
  tags: ["Folder"],
  method: "put",
  path: "/folders/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            parentId: { type: "number" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful folder update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  versionId: { type: "number" },
                  parentId: { type: "number" },
                  depth: { type: "number" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/:id",
  validate(updateFolderSchema),
  handler(folderController.updateFolder)
);

// Delete folder
registry.registerPath({
  tags: ["Folder"],
  method: "delete",
  path: "/folders/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful folder deletion",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.delete(
  "/:id",
  validate(deleteFolderSchema),
  handler(folderController.deleteFolder)
);

// Add the download route
registry.registerPath({
  tags: ["Folder"],
  method: "get",
  path: "/folders/{id}/download",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "Folder contents downloaded as ZIP",
      content: {
        "application/zip": {
          schema: {
            type: "string",
            format: "binary",
          },
        },
      },
    },
    404: {
      description: "Folder not found",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/download",
  validate(getFolderContentsSchema),
  handler(folderController.getFolderContents)
);

export default router;

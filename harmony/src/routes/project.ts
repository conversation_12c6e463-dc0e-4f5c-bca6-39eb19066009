// src/routes/project.ts

import { Router } from "express";
import { registry } from "..";
import { projectController } from "../controllers/project";
import { handler } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  addContributorSchema,
  createProjectSchema,
  deleteProjectSchema,
  downloadProjectSchema,
  getAllCommentsByProjectSchema,
  getAllTasksByProjectSchema,
  getAllVersionsbyProjectSchema,
  getProjectConversationsSchema,
  getProjectSchema,
  removeContributorSchema,
  updateProjectSchema,
} from "../validation/project";
import { getProjectInvitesSchema } from "../validation/projectInvite";

const router = Router();

registry.registerPath({
  tags: ["Project"],
  method: "post",
  path: "/projects",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            genre: { type: "string" },
            description: { type: "string" },
            tempo: { type: "number" },
            key: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful project creation",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              project: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                  genre: { type: "string" },
                  description: { type: "string" },
                  tempo: { type: "number" },
                  key: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.post(
  "/",
  validate(createProjectSchema),
  handler(projectController.createProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful project retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              project: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                  genre: { type: "string" },
                  description: { type: "string" },
                  tempo: { type: "number" },
                  key: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id",
  validate(getProjectSchema),
  handler(projectController.getProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}/versions",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "string" },
    },
  ],
  responses: {
    200: {
      description: "Successfully listed all versions for a project",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                number: { type: "number" },
                message: { type: "string" },
                projectId: { type: "string" },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/versions",
  validate(getAllVersionsbyProjectSchema),
  handler(projectController.getAllVersionsByProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}/tasks",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successfully got all tasks for the project",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              tasks: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    title: { type: "string" },
                    description: { type: "string" },
                    category: { type: "string" },
                    status: { type: "string" },
                    priority: { type: "string" },
                    dueDate: { type: "string", format: "date-time" },
                    assignee: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        name: { type: "string" },
                        email: { type: "string" },
                      },
                    },
                    author: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        name: { type: "string" },
                        email: { type: "string" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/tasks",
  validate(getAllTasksByProjectSchema),
  handler(projectController.getAllTasksByProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}/comments",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successfully retrieved all comments for the project",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              comments: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    content: { type: "string" },
                    author: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        name: { type: "string" },
                        email: { type: "string" },
                      },
                    },
                    createdAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/comments",
  validate(getAllCommentsByProjectSchema),
  handler(projectController.getAllCommentsByProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "put",
  path: "/projects/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            genre: { type: "string" },
            description: { type: "string" },
            tempo: { type: "number" },
            key: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful project update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              project: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                  genre: { type: "string" },
                  description: { type: "string" },
                  tempo: { type: "number" },
                  key: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/:id",
  validate(updateProjectSchema),
  handler(projectController.updateProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "delete",
  path: "/projects/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful project deletion",
    },
  },
});

router.delete(
  "/:id",
  validate(deleteProjectSchema),
  handler(projectController.deleteProject)
);

registry.registerPath({
  tags: ["Project"],
  method: "post",
  path: "/projects/{id}/contributors",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            userName: { type: "string" },
          },
          required: ["userName"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Contributor added successfully",
    },
    400: {
      description: "Invalid request",
    },
    404: {
      description: "Project or user not found",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

router.post(
  "/:id/contributors",
  validate(addContributorSchema),
  handler(projectController.addContributor)
);

registry.registerPath({
  tags: ["Project"],
  method: "delete",
  path: "/projects/{id}/contributors",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            userName: { type: "string" },
          },
          required: ["userName"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Contributor removed successfully",
    },
    400: {
      description: "Invalid request",
    },
    404: {
      description: "Project or user not found",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

router.delete(
  "/:id/contributors",
  validate(removeContributorSchema),
  handler(projectController.removeContributor)
);

registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}/download",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "number",
      },
    },
    {
      name: "versionId",
      in: "query",
      required: false,
      schema: {
        type: "number",
      },
      description:
        "ID of the version to download. If not provided, the latest version will be downloaded.",
    },
  ],
  responses: {
    200: {
      description: "Project contents downloaded as ZIP",
      content: {
        "application/zip": {
          schema: {
            type: "string",
            format: "binary",
          },
        },
      },
    },
    404: {
      description: "Project or version not found",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/download",
  validate(downloadProjectSchema),
  handler(projectController.downloadProject)
);

// Get Project Invites
registry.registerPath({
  tags: ["Project Invites"],
  method: "get",
  path: "/projects/{id}/invites",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "projectId",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successfully retrieved project invites",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                projectId: { type: "number" },
                userId: { type: "number" },
                status: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
                user: {
                  type: "object",
                  properties: {
                    id: { type: "number" },
                    name: { type: "string" },
                    email: { type: "string" },
                    profileImg: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Project not found",
    },
  },
});

router.get(
  "/:id/invites",
  validate(getProjectInvitesSchema),
  handler(projectController.getProjectInvites)
);

// get project conversations
registry.registerPath({
  tags: ["Project"],
  method: "get",
  path: "/projects/{id}/conversations",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successfully retrieved project conversations",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                title: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
                participants: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "number" },
                      name: { type: "string" },
                      email: { type: "string" },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    404: {
      description: "Project not found",
    },
  },
});

router.get(
  "/:id/conversations",
  validate(getProjectConversationsSchema),
  handler(projectController.getProjectConversations)
);

export default router;

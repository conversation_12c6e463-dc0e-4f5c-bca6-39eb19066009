// src/routes/projectInvite.ts

import { Router } from "express";
import { registry } from "..";
import { projectInviteController } from "../controllers/projectInvite";
import { handler } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  acceptProjectInviteSchema,
  createProjectInviteSchema,
  declineProjectInviteSchema,
  revokeProjectInviteSchema,
} from "../validation/projectInvite";

const router = Router();

// Create Project Invite
registry.registerPath({
  tags: ["Project Invites"],
  method: "post",
  path: "/project-invites",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            projectId: { type: "number" },
            userId: { type: "number" },
          },
          required: ["projectId", "userId"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Project invite created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "number" },
              projectId: { type: "number" },
              userId: { type: "number" },
              status: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
    400: {
      description: "Invalid request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Project or user not found",
    },
  },
});

router.post(
  "/",
  validate(createProjectInviteSchema),
  handler(projectInviteController.createProjectInvite)
);

// Accept Project Invite
registry.registerPath({
  tags: ["Project Invites"],
  method: "post",
  path: "/project-invites/{id}/accept",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Project invite accepted successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    400: {
      description: "Invalid request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Invite not found",
    },
  },
});

router.post(
  "/:id/accept",
  validate(acceptProjectInviteSchema),
  handler(projectInviteController.acceptProjectInvite)
);

// Decline Project Invite
registry.registerPath({
  tags: ["Project Invites"],
  method: "post",
  path: "/project-invites/{id}/decline",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Project invite declined successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    400: {
      description: "Invalid request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Invite not found",
    },
  },
});

router.post(
  "/:id/decline",
  validate(declineProjectInviteSchema),
  handler(projectInviteController.declineProjectInvite)
);

// Revoke Project Invite
registry.registerPath({
  tags: ["Project Invites"],
  method: "delete",
  path: "/project-invites/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Project invite revoked successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    400: {
      description: "Invalid request",
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Invite not found",
    },
  },
});

router.delete(
  "/:id",
  validate(revokeProjectInviteSchema),
  handler(projectInviteController.revokeProjectInvite)
);

export default router;

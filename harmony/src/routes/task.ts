// src/routes/task.ts

import { Router } from "express";
import { registry } from "..";
import { task<PERSON>ontroller } from "../controllers/task";
import { handler } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  createTaskSchema,
  deleteTaskSchema,
  getTaskSchema,
  updateTaskSchema,
} from "../validation/task";

const router = Router();

router.post(
  "/",
  validate(createTaskSchema),
  handler(taskController.createTask)
);

registry.registerPath({
  tags: ["Task"],
  method: "post",
  path: "/tasks",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            projectId: { type: "number" },
            title: { type: "string" },
            description: { type: "string" },
            assigneeId: { type: "number" },
            category: { type: "string" },
            status: { type: "string" },
            priority: { type: "string" },
            dueDate: { type: "string", format: "date-time" },
          },
          required: ["title"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Task created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.get("/:id", validate(getTaskSchema), handler(taskController.getTask));

registry.registerPath({
  tags: ["Task"],
  method: "get",
  path: "/tasks/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Get single task details",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "string" },
              title: { type: "string" },
              description: { type: "string" },
              status: { type: "string" },
              priority: { type: "string" },
              due_date: { type: "string", format: "date-time" },
              author: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                },
              },
              assignee: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/:id",
  validate(updateTaskSchema),
  handler(taskController.updateTask)
);

registry.registerPath({
  tags: ["Task"],
  method: "put",
  path: "/tasks/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            title: { type: "string" },
            description: { type: "string" },
            assigneeId: { type: "string" },
            category: { type: "string" },
            status: { type: "string" },
            priority: { type: "string" },
            dueDate: { type: "string", format: "date-time" },
            versionId: { type: "string" },
            folderId: { type: "string" },
            fileId: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Task updated successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "string" },
              title: { type: "string" },
              description: { type: "string" },
              assigneeId: { type: "string" },
              category: { type: "string" },
              status: { type: "string" },
              priority: { type: "string" },
              dueDate: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
  },
});

router.delete(
  "/:id",
  validate(deleteTaskSchema),
  handler(taskController.deleteTask)
);

registry.registerPath({
  tags: ["Task"],
  method: "delete",
  path: "/tasks/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Task deleted successfully",
    },
  },
});

export default router;

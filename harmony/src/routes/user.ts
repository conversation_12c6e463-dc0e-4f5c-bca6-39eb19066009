// src/routes/user.ts

import { Router } from "express";
import { registry } from "..";
import { userController } from "../controllers/user";
import { handler, withFileUpload } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import { getUserInvitesSchema } from "../validation/projectInvite";
import {
  deleteLeafletSchema,
  getLeafletContentSchema,
  getLeafletSchema,
  getUserLeafletsSchema,
  getUserProjectsSchema,
  searchUsersSchema,
  updateLeafletSchema,
} from "../validation/user";

const router = Router();

// Get all users
registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/users",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "page",
      in: "query",
      required: true,
      schema: {
        type: "number",
      },
    },
  ],
  responses: {
    200: {
      description: "List of all users",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                name: { type: "string" },
                email: { type: "string" },
                role: { type: "string" },
                bio: { type: "string" },
                profileImg: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
      },
    },
  },
});

router.get("/", handler(userController.getAllUsers));

// Search users
registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/users/search",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "q",
      in: "query",
      required: true,
      schema: {
        type: "string",
        minLength: 2,
      },
    },
    {
      name: "includeTopGenres",
      in: "query",
      required: false,
      schema: {
        type: "boolean",
      },
    },
  ],
  responses: {
    200: {
      description: "List of users matching the search query",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                name: { type: "string" },
                email: { type: "string" },
                profileImg: { type: "string" },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/search",
  validate(searchUsersSchema),
  handler(userController.searchUsers as any) // Temp fix until I figure out handler query typing
);

// Get User Invites
registry.registerPath({
  tags: ["Project Invites"],
  method: "get",
  path: "/users/invites",
  security: [{ BearerAuth: [] }],
  responses: {
    200: {
      description: "Successfully retrieved user invites",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                projectId: { type: "number" },
                userId: { type: "number" },
                status: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
                project: {
                  type: "object",
                  properties: {
                    id: { type: "number" },
                    name: { type: "string" },
                    description: { type: "string" },
                    genre: { type: "string" },
                    author: {
                      type: "object",
                      properties: {
                        id: { type: "number" },
                        name: { type: "string" },
                        profileImg: { type: "string" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    401: {
      description: "Unauthorized",
    },
  },
});

router.get(
  "/invites",
  validate(getUserInvitesSchema),
  handler(userController.getUserInvites)
);

// Create audio sample
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "post",
  path: "/users/audio",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "multipart/form-data": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            featured: { type: "boolean" },
            metadata: { type: "string" },
            file: {
              type: "string",
              format: "binary",
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful audio sample creation",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "number" },
              userId: { type: "number" },
              name: { type: "string" },
              s3Path: { type: "string" },
              featured: { type: "boolean" },
              metadata: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
  },
});

router.post("/audio", withFileUpload(userController.createLeaflet));

// Get audio sample
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "get",
  path: "/users/audio/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful audio sample retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "number" },
              userId: { type: "number" },
              name: { type: "string" },
              s3Path: { type: "string" },
              featured: { type: "boolean" },
              metadata: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/audio/:id",
  validate(getLeafletSchema),
  handler(userController.getLeaflet)
);

// Update audio sample
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "put",
  path: "/users/audio/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            name: { type: "string" },
            featured: { type: "boolean" },
            metadata: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful audio sample update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              id: { type: "number" },
              userId: { type: "number" },
              name: { type: "string" },
              s3Path: { type: "string" },
              featured: { type: "boolean" },
              metadata: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
  },
});

router.put(
  "/audio/:id",
  validate(updateLeafletSchema),
  handler(userController.updateLeaflet)
);

// Delete audio sample
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "delete",
  path: "/users/audio/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful audio sample deletion",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.delete(
  "/audio/:id",
  validate(deleteLeafletSchema),
  handler(userController.deleteLeaflet)
);

// Get audio sample content
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "get",
  path: "/users/audio/{id}/content",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful audio sample content retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              signedUrl: { type: "string" },
              fileName: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/audio/:id/content",
  validate(getLeafletContentSchema),
  handler(userController.getLeafletContent)
);

registry.registerPath({
  tags: ["User"],
  method: "put",
  path: "/users/{name}/profile-image",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "multipart/form-data": {
        schema: {
          type: "object",
          properties: {
            file: {
              type: "string",
              format: "binary",
            },
          },
          required: ["file"],
        },
      },
    },
  },
  responses: {
    200: {
      description: "Profile image successfully updated",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              profileImg: { type: "string" },
            },
          },
        },
      },
    },
    400: {
      description: "Invalid file format or size",
    },
    401: {
      description: "Unauthorized",
    },
    403: {
      description: "Forbidden - Can only update own profile image",
    },
  },
});

router.put(
  "/:name/profile-image",
  withFileUpload(userController.uploadProfileImage)
);

// Get projects for user
registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/users/{id}/projects",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful project retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              projects: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    name: { type: "string" },
                    genre: { type: "string" },
                    description: { type: "string" },
                    tempo: { type: "number" },
                    key: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:name/projects",
  validate(getUserProjectsSchema),
  handler(userController.getUserProjects)
);

// Get audio samples
registry.registerPath({
  tags: ["User Audio Samples"],
  method: "get",
  path: "/users/{name}/audio",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful audio sample retrieval",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                userId: { type: "number" },
                name: { type: "string" },
                s3Path: { type: "string" },
                featured: { type: "boolean" },
                metadata: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:name/audio",
  validate(getUserLeafletsSchema),
  handler(userController.getUserLeaflets)
);

// Get user
registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/users/{name}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successful user retrieval",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              email: { type: "string" },
              name: { type: "string" },
              role: { type: "string" },
              bio: { type: "string" },
              profileImg: { type: "string" },
              socials: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    platform: { type: "string" },
                    url: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});

router.get("/:name", handler(userController.getUser));

// Update user
registry.registerPath({
  tags: ["User"],
  method: "put",
  path: "/users/{name}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            email: { type: "string" },
            name: { type: "string" },
            role: { type: "string" },
            bio: { type: "string" },
            profileImg: { type: "string" },
            socials: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  platform: { type: "string" },
                  url: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful user update",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              email: { type: "string" },
              name: { type: "string" },
              role: { type: "string" },
              bio: { type: "string" },
              profileImg: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.put("/:name", handler(userController.updateUser));

// Delete user
registry.registerPath({
  tags: ["User"],
  method: "delete",
  path: "/users/{name}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            email: { type: "string" },
            password: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Successful user deletion",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
  },
});

router.delete("/:name", handler(userController.deleteUser));

// Get user conversations
registry.registerPath({
  tags: ["User"],
  method: "get",
  path: "/users/{name}/conversations",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "name",
      in: "path",
      required: true,
      schema: {
        type: "string",
      },
    },
  ],
  responses: {
    200: {
      description: "Successfully retrieved user conversations",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                title: { type: "string" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
                participants: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "number" },
                      name: { type: "string" },
                      email: { type: "string" },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    404: {
      description: "User not found",
    },
  },
});

router.get(
  "/:name/conversations",
  handler(userController.getUserConversations)
);

export default router;

// harmony/src/routes/version.ts

import { Router } from "express";
import { registry } from "..";
import { versionController } from "../controllers/version";
import { handler } from "../middleware/response.handler";
import { validate } from "../middleware/validator";
import {
  deleteVersionSchema,
  getAllFilesByVersionSchema,
  getAllFoldersByVersionSchema,
  getVersionSchema,
  updateVersionSchema,
} from "../validation/version";

const router = Router();

registry.registerPath({
  tags: ["Version"],
  method: "post",
  path: "/versions",
  security: [{ BearerAuth: [] }],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            projectId: { type: "number" },
            message: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    201: {
      description: "Version created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              number: { type: "number" },
              message: { type: "string" },
              projectId: { type: "number" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
        },
      },
    },
  },
});

router.post(
  "/",
  // validate(createVersionSchema),
  handler(versionController.createVersion)
);

registry.registerPath({
  tags: ["Version"],
  method: "get",
  path: "/versions/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "number" },
    },
  ],
  responses: {
    200: {
      description: "Get version details",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              number: { type: "number" },
              message: { type: "string" },
              projectId: { type: "number" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
              // File: {
              //   type: "array",
              //   items: {
              //     type: "object",
              //     properties: {
              //       name: { type: "string" },
              //       file_type: { type: "string" },
              //     },
              //   },
              // },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id",
  validate(getVersionSchema),
  handler(versionController.getVersion)
);

registry.registerPath({
  tags: ["Version"],
  method: "get",
  path: "/versions/{id}/files",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "number" },
    },
  ],
  responses: {
    200: {
      description: "Successfully listed all files for a version",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                name: { type: "string" },
                versionId: { type: "number" },
                folderId: { type: "number" },
                s3Path: { type: "string" },
                fileType: { type: "string" },
                fileSize: { type: "number" },
                metadata: { type: "object" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/files",
  validate(getAllFilesByVersionSchema),
  handler(versionController.getAllFilesByVersion)
);

registry.registerPath({
  tags: ["Version"],
  method: "get",
  path: "/versions/{id}/folders",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "number" },
    },
  ],
  responses: {
    200: {
      description: "Successfully listed all folders for a version",
      content: {
        "application/json": {
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "number" },
                name: { type: "string" },
                versionId: { type: "number" },
                parentId: { type: "number" },
                depth: { type: "number" },
                createdAt: { type: "string", format: "date-time" },
                updatedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
      },
    },
  },
});

router.get(
  "/:id/folders",
  validate(getAllFoldersByVersionSchema),
  handler(versionController.getAllFoldersByVersion)
);

registry.registerPath({
  tags: ["Version"],
  method: "put",
  path: "/versions/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "number" },
    },
  ],
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            message: { type: "string" },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "Version updated successfully",
    },
  },
});

router.put(
  "/:id",
  validate(updateVersionSchema),
  handler(versionController.updateVersion)
);

registry.registerPath({
  tags: ["Version"],
  method: "delete",
  path: "/versions/{id}",
  security: [{ BearerAuth: [] }],
  parameters: [
    {
      name: "id",
      in: "path",
      required: true,
      schema: { type: "number" },
    },
  ],
  responses: {
    200: {
      description: "Version deleted successfully",
    },
  },
});

router.delete(
  "/:id",
  validate(deleteVersionSchema),
  handler(versionController.deleteVersion)
);

export default router;

// src/server.ts
import express, { Express } from 'express';
import cors from 'cors';
import {
  routesAuth,
  routesProject,
  routesTask,
  routesUser,
  routesVersion,
} from './routes';
import { errorHandler } from "./middleware/response.handler";

export const createServer = async (): Promise<Express> => {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // API Routes - add error checking
  if (routesAuth) app.use('/api/auth', routesAuth);
  if (routesProject) app.use('/api/projects', routesProject);
  if (routesTask) app.use('/api/tasks', routesTask);
  if (routesUser) app.use('/api/users', routesUser);
  if (routesVersion) app.use('/api/versions', routesVersion);

  // Error handling middleware should be last
  app.use(errorHandler);

  return app;
};

// Only start the server if we're running the file directly
if (require.main === module) {
  const PORT = process.env.PORT || 3000;
  createServer().then(app => {
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  });
}

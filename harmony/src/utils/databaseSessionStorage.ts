import { prisma } from "../index";

// Implement the Storage interface required by Supabase
export class DatabaseSessionStorage {
  // Default TTL for sessions (7 days in milliseconds)
  private readonly DEFAULT_TTL = 7 * 24 * 60 * 60 * 1000;

  async getItem(key: string): Promise<string | null> {
    try {
      const session = await prisma.authSession.findUnique({
        where: { key },
      });

      // Check if session exists and is not expired
      if (!session) {
        console.log(`[SessionStorage] No session found for key: ${key}`);
        return null;
      }

      const updatedAt = new Date(session.updatedAt).getTime();
      const now = Date.now();
      const timeSinceUpdate = now - updatedAt;
      const remainingTime = this.DEFAULT_TTL - timeSinceUpdate;

      // Log session status for debugging
      console.log(
        `[SessionStorage] Session for ${key}: age=${Math.round(
          timeSinceUpdate / (60 * 60 * 1000)
        )}h, remaining=${Math.round(remainingTime / (60 * 60 * 1000))}h`
      );

      // If session is expired, remove it and return null
      if (timeSinceUpdate > this.DEFAULT_TTL) {
        console.log(`[SessionStorage] Session expired for key: ${key}`);
        await this.removeItem(key);
        return null;
      }

      return session?.value || null;
    } catch (error) {
      console.error("Error retrieving session:", error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await prisma.authSession.upsert({
        where: { key },
        update: { value, updatedAt: new Date() },
        create: { key, value },
      });
    } catch (error) {
      console.error("Error storing session:", error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await prisma.authSession
        .delete({
          where: { key },
        })
        .catch((err) => {
          // Ignore if session doesn't exist
          if (err.code !== "P2025") throw err;
        });
    } catch (error) {
      console.error("Error removing session:", error);
    }
  }
}

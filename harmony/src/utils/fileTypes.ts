// src/utils/fileTypes.ts

// ==================== DIGITAL AUDIO FILES ====================
// Uncompressed audio formats
const uncompressedAudioFormats: string[] = [
  ".wav", // Waveform Audio File Format
  ".aif", // Audio Interchange File Format
  ".aiff", // Audio Interchange File Format (full name)
  ".bwf", // Broadcast Wave Format
  ".sd2", // Sound Designer II
  ".au", // Unix Audio
  ".caf", // Core Audio Format (Apple)
];

// Lossy compression audio formats
const lossyCompressedAudioFormats: string[] = [
  ".mp3", // MPEG-1 Audio Layer 3
  ".m4a", // AAC container
  ".aac", // Advanced Audio Coding
  ".ogg", // Ogg Vorbis
  ".wma", // Windows Media Audio
  ".opus", // Opus Audio Format
];

// Lossless compression audio formats
const losslessCompressedAudioFormats: string[] = [
  ".flac", // Free Lossless Audio Codec
  ".alac", // Apple Lossless Audio Codec
  ".mqa", // Master Quality Authenticated
  ".dsd", // Direct Stream Digital (high-resolution)
];

// All digital audio formats combined
const digitalAudioFormats: string[] = [
  ...uncompressedAudioFormats,
  ...lossyCompressedAudioFormats,
  ...losslessCompressedAudioFormats,
];

// ==================== DAW PROJECT FILES ====================
// Ableton formats
const abletonFormats: string[] = [
  ".als", // Ableton Live Session
  ".alp", // Ableton Live Pack
];

// Apple DAW formats
const appleDAWFormats: string[] = [
  ".logicx", // Logic Pro X Project
  ".logic", // Logic Pro (older version)
  ".sng", // GarageBand Project
  ".band", // GarageBand Project (newer)
];

// Avid formats
const avidFormats: string[] = [
  ".ptx", // Pro Tools Session
  ".pts", // Pro Tools Session (older)
];

// Adobe formats
const adobeFormats: string[] = [
  ".sesx", // Adobe Audition Session
];

// Steinberg formats
const steinbergFormats: string[] = [
  ".cpr", // Cubase Project
  ".npr", // Nuendo Project
];

// Other major DAW formats
const otherMajorDAWFormats: string[] = [
  ".rpp", // REAPER Project
  ".rea", // REAPER Project (alternative)
  ".flp", // FL Studio Project
  ".aup", // Audacity Project
  ".reason", // Reason Project
  ".ardour", // Ardour Project
  ".bitwig", // Bitwig Studio Project
  ".sts", // Studio One Song
  ".dp", // Digital Performer Project
];

// Cakewalk/SONAR formats
const cakewalkFormats: string[] = [
  ".sonar", // SONAR Project
  ".son", // SONAR Project (older)
  ".sfl", // SONAR Project (older)
  ".sfw", // SONAR Project (older)
];

// Other DAW formats
const otherDAWFormats: string[] = [
  ".mmpz", // LMMS Project
  ".rns", // Renoise Song
];

// All DAW project formats combined
const dawProjectFormats: string[] = [
  ...abletonFormats,
  ...appleDAWFormats,
  ...avidFormats,
  ...adobeFormats,
  ...steinbergFormats,
  ...otherMajorDAWFormats,
  ...cakewalkFormats,
  ...otherDAWFormats,
];

// ==================== MIDI AND NOTATION ====================
// MIDI formats
const midiFormats: string[] = [
  ".mid", // Standard MIDI File
  ".midi", // Standard MIDI File (alternative extension)
  ".kar", // Karaoke MIDI
  ".gm", // General MIDI File
];

// Notation formats
const notationFormats: string[] = [
  ".musicxml", // Music XML (notation interchange)
  ".sib", // Sibelius Score
  ".mus", // Finale Score (older)
  ".musx", // Finale Score (newer)
  ".mscz", // MuseScore
];

// Guitar tablature formats
const tablatureFormats: string[] = [
  ".gp", // Guitar Pro
  ".gpx", // Guitar Pro 6+
];

// All MIDI and notation formats combined
const midiAndNotationFormats: string[] = [
  ...midiFormats,
  ...notationFormats,
  ...tablatureFormats,
];

// ==================== SAMPLER AND VIRTUAL INSTRUMENT FILES ====================
// Sample library formats
const sampleLibraryFormats: string[] = [
  ".sf2", // SoundFont 2
  ".sfz", // SFZ Format
  ".gig", // GigaSampler/GigaStudio Instrument
];

// DAW-specific instrument formats
const dawInstrumentFormats: string[] = [
  ".xrni", // Renoise Instrument
  ".exs", // EXS24 Sampler Instrument (Logic)
  ".patch", // Reason Patch
];

// Hardware sampler formats
const hardwareSamplerFormats: string[] = [
  ".akp", // Akai Program
  ".akm", // Akai MIDI Map
];

// Plugin preset formats
const pluginPresetFormats: string[] = [
  ".fxp", // Effect Preset
  ".fxb", // Effect Bank
];

// Native Instruments formats
const nativeInstrumentsFormats: string[] = [
  ".nki", // Kontakt Instrument
  ".nkm", // Kontakt Multi
  ".nkx", // Kontakt Monolith
];

// Ableton device formats
const abletonDeviceFormats: string[] = [
  ".adg", // Ableton Device Group
  ".adv", // Ableton Device Preset
  ".amxd", // Ableton Max for Live Device
];

// Plugin formats
const pluginFormats: string[] = [
  ".dll", // Windows Plugin/VST
  ".vst", // VST Plugin
  ".vst3", // VST3 Plugin
  ".component", // Audio Unit Plugin (macOS)
  ".aupreset", // Audio Unit Preset
  ".tlp", // TAL Preset
  ".vstpreset", // VST Preset
];

// All sampler and virtual instrument formats combined
const samplerAndInstrumentFormats: string[] = [
  ...sampleLibraryFormats,
  ...dawInstrumentFormats,
  ...hardwareSamplerFormats,
  ...pluginPresetFormats,
  ...nativeInstrumentsFormats,
  ...abletonDeviceFormats,
  ...pluginFormats,
];

// ==================== LOOP AND SAMPLE FILES ====================
// Loop formats
const loopFormats: string[] = [
  ".rex", // ReCycle Loop
  ".rx2", // ReCycle Loop (newer)
  ".acid", // ACID Loop
  ".groove", // Groove Template
];

// Sample service formats
const sampleServiceFormats: string[] = [
  ".splice", // Splice Sample
  ".samplepack", // Sample Pack Format
];

// All loop and sample formats combined
const loopAndSampleFormats: string[] = [
  ...loopFormats,
  ...sampleServiceFormats,
];

// ==================== STEMS AND SESSION INTERCHANGE ====================
// Industry standard interchange formats
const interchangeFormats: string[] = [
  ".omf", // Open Media Framework Interchange
  ".omfi", // Open Media Framework Interchange (alternative)
  ".aaf", // Advanced Authoring Format
  ".mtf", // Media Transfer Format
  ".xml", // Session interchange (various DAWs)
];

// Specialized interchange formats
const specializedInterchangeFormats: string[] = [
  ".stem", // Native Instruments STEM Format
];

// Archive formats commonly used for exchanging music projects
const archiveFormats: string[] = [
  ".zip", // ZIP Archive
  ".rar", // RAR Archive
  ".7z", // 7-Zip Archive
];

// All stems and session interchange formats combined
const stemsAndInterchangeFormats: string[] = [
  ...interchangeFormats,
  ...specializedInterchangeFormats,
  ...archiveFormats,
];

// ==================== FINAL COMBINED ARRAY ====================
// All music production file extensions combined
export const FINAL_ALLOWED_EXTENSIONS: string[] = [
  ...digitalAudioFormats,
  ...dawProjectFormats,
  ...midiAndNotationFormats,
  ...samplerAndInstrumentFormats,
  ...loopAndSampleFormats,
  ...stemsAndInterchangeFormats,
];

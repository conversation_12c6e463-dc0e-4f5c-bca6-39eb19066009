// src/utils/hash.ts

import crypto from "crypto";

/**
 * Generates a random hash of specified length
 * @param length Length of the hash to generate (default: 8)
 * @returns Promise<string> The generated hash
 */
export async function generateHash(length: number = 8): Promise<string> {
  return new Promise((resolve, reject) => {
    crypto.randomBytes(Math.ceil(length / 2), (err, buffer) => {
      if (err) {
        reject(err);
        return;
      }
      const hash = buffer.toString("hex").slice(0, length);
      resolve(hash);
    });
  });
}

/**
 * Validates if a string matches the hash format
 * @param hash Hash string to validate
 * @param length Expected length of the hash (default: 8)
 * @returns boolean Whether the hash is valid
 */
export function validateHash(hash: string, length: number = 8): boolean {
  const hashRegex = new RegExp(`^[0-9a-f]{${length}}$`);
  return hashRegex.test(hash);
}

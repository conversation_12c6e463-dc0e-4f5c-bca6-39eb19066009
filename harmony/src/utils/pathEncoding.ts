/**
 * Utilities for encoding/decoding file paths with special characters for Supabase storage
 */

/**
 * Encodes a file path to make it safe for Supabase storage
 * @param path Original file path that may contain special characters
 * @returns Encoded path safe for Supabase storage
 */
export function encodePath(path: string): string {
  // Replace problematic characters with safe alternatives
  // Instead of using encodeURIComponent which produces % signs
  return path
    .split("/")
    .map((part) =>
      part
        .replace(/\s/g, "_SPACE_")
        .replace(/\+/g, "_PLUS_")
        .replace(/\%/g, "_PERCENT_")
        .replace(/\#/g, "_HASH_")
        .replace(/\?/g, "_QUESTION_")
        .replace(/\&/g, "_AMP_")
        .replace(/\=/g, "_EQUAL_")
        .replace(/\:/g, "_COLON_")
        .replace(/\@/g, "_AT_")
        .replace(/\</g, "_LT_")
        .replace(/\>/g, "_GT_")
        .replace(/\"/g, "_QUOTE_")
        .replace(/\'/g, "_APOS_")
        .replace(/\`/g, "_BACKTICK_")
        .replace(/\{/g, "_LCURLY_")
        .replace(/\}/g, "_RCURLY_")
        .replace(/\[/g, "_LBRACKET_")
        .replace(/\]/g, "_RBRACKET_")
        .replace(/\|/g, "_PIPE_")
        .replace(/\\/g, "_BACKSLASH_")
        .replace(/\^/g, "_CARET_")
        .replace(/\~/g, "_TILDE_")
    )
    .join("/");
}

/**
 * Decodes a file path from Supabase storage back to its original form
 * @param encodedPath Encoded path from Supabase storage
 * @returns Original path with special characters restored
 */
export function decodePath(encodedPath: string): string {
  // Restore original characters
  return encodedPath
    .split("/")
    .map((part) =>
      part
        .replace(/_SPACE_/g, " ")
        .replace(/_PLUS_/g, "+")
        .replace(/_PERCENT_/g, "%")
        .replace(/_HASH_/g, "#")
        .replace(/_QUESTION_/g, "?")
        .replace(/_AMP_/g, "&")
        .replace(/_EQUAL_/g, "=")
        .replace(/_COLON_/g, ":")
        .replace(/_AT_/g, "@")
        .replace(/_LT_/g, "<")
        .replace(/_GT_/g, ">")
        .replace(/_QUOTE_/g, '"')
        .replace(/_APOS_/g, "'")
        .replace(/_BACKTICK_/g, "`")
        .replace(/_LCURLY_/g, "{")
        .replace(/_RCURLY_/g, "}")
        .replace(/_LBRACKET_/g, "[")
        .replace(/_RBRACKET_/g, "]")
        .replace(/_PIPE_/g, "|")
        .replace(/_BACKSLASH_/g, "\\")
        .replace(/_CARET_/g, "^")
        .replace(/_TILDE_/g, "~")
    )
    .join("/");
}

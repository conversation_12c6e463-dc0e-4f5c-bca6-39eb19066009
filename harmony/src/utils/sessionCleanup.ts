import { prisma } from "../index";

// Clean up expired sessions (run this periodically)
export async function cleanupExpiredSessions() {
  // Use 7 days to match the DatabaseSessionStorage DEFAULT_TTL
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  try {
    // Delete sessions that haven't been updated in the last 7 days
    const result = await prisma.authSession.deleteMany({
      where: {
        updatedAt: {
          lt: sevenDaysAgo,
        },
      },
    });

    console.log(`Cleaned up ${result.count} expired sessions`);

    // Also check for and remove any corrupted sessions (invalid JSON)
    const allSessions = await prisma.authSession.findMany({
      select: { id: true, key: true, value: true },
    });

    const corruptedSessions = allSessions.filter((session) => {
      try {
        JSON.parse(session.value);
        return false; // Not corrupted
      } catch (e) {
        return true; // Corrupted
      }
    });

    if (corruptedSessions.length > 0) {
      await prisma.authSession.deleteMany({
        where: {
          id: {
            in: corruptedSessions.map((s) => s.id),
          },
        },
      });
      console.log(`Cleaned up ${corruptedSessions.length} corrupted sessions`);
    }
  } catch (error) {
    console.error("Error cleaning up sessions:", error);
  }
}

import ffmpegPath from "@ffmpeg-installer/ffmpeg";
import ffmpeg from "fluent-ffmpeg";
import fetch from "node-fetch";

ffmpeg.setFfmpegPath(ffmpegPath.path);

export async function getWaveformFromUrl(
  url: string,
  samples = 200
): Promise<number[]> {
  const response = await fetch(url);
  if (!response.body) throw new Error("No response body from fetch");
  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

  return new Promise((resolve, reject) => {
    const audioData: number[] = [];

    const command = ffmpeg(response.body as any)
      .format("s16le") // 16-bit signed little endian
      .audioChannels(1) // Mono
      .audioFrequency(8000) // Lower sample rate for faster processing
      .on("error", (err) => {
        reject(new Error(`FFmpeg processing failed: ${err.message}`));
      });

    const stream = command.pipe();

    stream.on("data", (chunk: Buffer) => {
      // Convert 16-bit samples to amplitude values
      for (let i = 0; i < chunk.length; i += 2) {
        if (i + 1 < chunk.length) {
          const sample = chunk.readInt16LE(i);
          audioData.push(Math.abs(sample) / 32768); // Normalize to 0-1 range
        }
      }
    });

    stream.on("end", () => {
      if (audioData.length === 0) {
        reject(new Error("No audio data received"));
        return;
      }

      // Downsample to target number of samples
      const waveform = downsampleToWaveform(audioData, samples);
      resolve(waveform);
    });

    stream.on("error", (err) => {
      reject(err);
    });
  });
}

function downsampleToWaveform(
  audioData: number[],
  targetSamples: number
): number[] {
  if (audioData.length <= targetSamples) {
    return audioData;
  }

  const result: number[] = [];
  const segmentSize = Math.floor(audioData.length / targetSamples);

  for (let i = 0; i < targetSamples; i++) {
    const segmentStart = i * segmentSize;
    const segmentEnd = Math.min(segmentStart + segmentSize, audioData.length);

    // Find the peak (maximum) value in this segment for waveform visualization
    let peak = 0;
    for (let j = segmentStart; j < segmentEnd; j++) {
      peak = Math.max(peak, audioData[j]);
    }

    result.push(peak);
  }

  // Normalize the final waveform
  const maxPeak = Math.max(...result);
  if (maxPeak > 0) {
    return result.map((value) => value / maxPeak);
  }

  return result;
}

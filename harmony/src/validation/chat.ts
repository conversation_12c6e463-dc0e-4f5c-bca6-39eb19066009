// Conversation CRUD schemas
import { registry } from "@/.";
import { z } from "zod";

export const createConversationSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Conversation Name is required"),
    description: z.string().optional(),
    projectId: z.preprocess(
      (val) => (val === null || val === undefined ? null : Number(val)),
      z.number().nullable().optional()
    ),
    participantIds: z
      .array(z.number().min(1, "Participant ID is required"))
      .optional(),
    fromProfile: z.boolean().optional(),
  }),
});

registry.register("CreateConversation", createConversationSchema);

export const getConversationSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Conversation ID is required"),
  }),
});

registry.register("GetConversation", getConversationSchema);

export const updateConversationSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Conversation ID is required"),
  }),
  body: z.object({
    name: z.string().optional(),
    description: z.string().optional(),
    participantIds: z
      .array(z.number().min(1, "Participant ID is required"))
      .optional(),
    participantLastSeen: z.boolean().optional(),
  }),
});

registry.register("UpdateConversation", updateConversationSchema);

export const deleteConversationSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Conversation ID is required"),
  }),
});

registry.register("DeleteConversation", deleteConversationSchema);

// Message CRUD schemas

export const createMessageSchema = z.object({
  body: z.object({
    conversationId: z.string().min(1, "Conversation ID is required"),
    content: z.string().min(1, "Message content is required"),
  }),
});

registry.register("CreateMessage", createMessageSchema);

export const getMessageSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Message ID is required"),
  }),
});

registry.register("GetMessage", getMessageSchema);

export const updateMessageSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Message ID is required"),
  }),
  body: z.object({
    content: z.string().optional(),
  }),
});

registry.register("UpdateMessage", updateMessageSchema);

export const deleteMessageSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Message ID is required"),
  }),
});

registry.register("DeleteMessage", deleteMessageSchema);

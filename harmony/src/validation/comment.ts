// src/validation/comment.ts

import { z } from "zod";
import { registry } from "..";

export const createCommentSchema = z.object({
  body: z.object({
    content: z.string().min(1, "Content is required"),
    projectId: z.preprocess((val) => Number(val), z.number().min(1)),
  }),
});

registry.register("CreateComment", createCommentSchema);

export const getCommentSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Comment ID is required"),
  }),
});

registry.register("GetComment", getCommentSchema);

export const updateCommentSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Comment ID is required"),
  }),
  body: z.object({
    content: z.string().optional(),
  }),
});

registry.register("UpdateComment", updateCommentSchema);

export const deleteCommentSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Comment ID is required"),
  }),
});

registry.register("DeleteComment", deleteCommentSchema);

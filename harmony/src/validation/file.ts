// harmony/src/validation/file.ts

import { z } from "zod";
import { registry } from "..";
import { FINAL_ALLOWED_EXTENSIONS } from "../utils/fileTypes";

function isValidFileName(fileName: string): boolean {
  // Get the extension with the dot
  const extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();

  // Check if it's in our allowed extensions
  return FINAL_ALLOWED_EXTENSIONS.includes(extension);
}

export const createFileSchema = z.object({
  body: z.object({
    name: z.string().min(1, "File Name is required").refine(isValidFileName, {
      message: "File name must have a valid file format extension",
    }),
    versionId: z.preprocess((val) => Number(val), z.number().min(1)),
    folderId: z.preprocess(
      (val) => (val === null || val === undefined ? null : Number(val)),
      z.number().nullable().optional()
    ),
    s3Path: z.string().min(1),
    fileType: z.string().min(1),
    fileSize: z.preprocess((val) => Number(val), z.number().min(1)),
    metadata: z.any().optional(),
    independent: z.boolean().optional(),
  }),
});

registry.register("CreateFile", createFileSchema);

export const getFileSchema = z.object({
  params: z.object({
    id: z.string().min(1, "File ID is required"),
  }),
});

registry.register("GetFile", getFileSchema);

export const updateFileSchema = z.object({
  params: z.object({
    id: z.string().min(1, "File ID is required"),
  }),
  body: z.object({
    name: z
      .string()
      .optional()
      .refine((name) => (name ? isValidFileName(name) : true), {
        message: "File name must have a valid file format extension",
      }),
    fileType: z.string().optional(),
    fileSize: z.number().optional(),
    folderId: z.preprocess(
      (val) => (val === null || val === undefined ? null : Number(val)),
      z.number().nullable().optional()
    ),
    metadata: z.any().optional(),
  }),
});

registry.register("UpdateFile", updateFileSchema);

export const deleteFileSchema = z.object({
  params: z.object({
    id: z.string().min(1, "File ID is required"),
  }),
});

registry.register("DeleteFile", deleteFileSchema);

export const getFileContentsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "File ID is required"),
  }),
});

registry.register("GetFileContents", getFileContentsSchema);

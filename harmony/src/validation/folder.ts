// harmony/src/validation/folder.ts

import { z } from "zod";
import { registry } from "..";

export const createFolderSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Folder Name is required"),
    versionId: z.preprocess((val) => Number(val), z.number().min(1)),
    parentId: z.preprocess(
      (val) => (val === null || val === undefined ? null : Number(val)),
      z.number().nullable().optional()
    ),
  }),
});

registry.register("CreateFolder", createFolderSchema);

export const uploadFolderSchema = z.object({
  body: z.object({
    versionId: z.number().min(1),
    name: z.string().min(1),
    paths: z.array(z.string()).optional(),
    parentId: z.number().optional(),
  }),
});

registry.register("UploadFolder", uploadFolderSchema);

export const getFolderSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
});

registry.register("GetFolder", getFolderSchema);

export const getAllFilesByFolderSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
});

registry.register("getAllFilesByFolder", getAllFilesByFolderSchema);

export const getFolderAncestorsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
});

registry.register("GetFolderAncestors", getFolderAncestorsSchema);

export const updateFolderSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
  body: z.object({
    name: z.string().optional(),
    parentId: z.preprocess(
      (val) => (val === null || val === undefined ? null : Number(val)),
      z.number().nullable().optional()
    ),
  }),
});

registry.register("UpdateFolder", updateFolderSchema);

export const deleteFolderSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
});

registry.register("DeleteFolder", deleteFolderSchema);

export const getFolderContentsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Folder ID is required"),
  }),
});

registry.register("GetFolderContents", getFolderContentsSchema);

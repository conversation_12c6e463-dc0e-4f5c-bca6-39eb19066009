// src/validation/project.ts

import { z } from "zod";
import { registry } from "..";

export const createProjectSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Name is required"),
    genre: z.string().min(1, "Genre is required"),
    description: z.string().optional(),
    tempo: z.number().optional(),
    key: z.string().optional(),
  }),
});

registry.register("CreateProject", createProjectSchema);

export const getProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("GetProject", getProjectSchema);

export const getAllVersionsbyProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("GetAllVersionsbyProject", getAllVersionsbyProjectSchema);

export const getAllTasksByProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("GetAllTasksByProjectSchema", getAllTasksByProjectSchema);

export const getAllCommentsByProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("GetAllCommentsByProject", getAllCommentsByProjectSchema);

export const updateProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
  body: z.object({
    name: z.string().optional(),
    genre: z.string().optional(),
    description: z.string().optional(),
    tempo: z.number().optional(),
    key: z.string().optional(),
  }),
});

registry.register("UpdateProject", updateProjectSchema);

export const deleteProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("DeleteProject", deleteProjectSchema);

export const addContributorSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
  body: z.object({
    userName: z.string().min(1, "User name is required"),
  }),
});

registry.register("AddContributor", addContributorSchema);

export const removeContributorSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
  body: z.object({
    userName: z.string().min(1, "User name is required"),
  }),
});

registry.register("RemoveContributor", removeContributorSchema);

export const downloadProjectSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("DownloadProject", downloadProjectSchema);

// Chats

export const getProjectConversationsSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

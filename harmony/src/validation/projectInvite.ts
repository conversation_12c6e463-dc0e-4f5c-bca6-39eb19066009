// src/validation/projectInvite.ts

import { z } from "zod";
import { registry } from "..";

export const createProjectInviteSchema = z.object({
  body: z.object({
    projectId: z.number().int().positive("Project ID is required"),
    userId: z.number().int().positive("User ID is required"),
  }),
});

registry.register("CreateProjectInvite", createProjectInviteSchema);

export const acceptProjectInviteSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Invite ID is required"),
  }),
});

registry.register("AcceptProjectInvite", acceptProjectInviteSchema);

export const declineProjectInviteSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Invite ID is required"),
  }),
});

registry.register("DeclineProjectInvite", declineProjectInviteSchema);

export const revokeProjectInviteSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Invite ID is required"),
  }),
});

registry.register("RevokeProjectInvite", revokeProjectInviteSchema);

export const getProjectInvitesSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Project ID is required"),
  }),
});

registry.register("GetProjectInvites", getProjectInvitesSchema);

export const getUserInvitesSchema = z.object({
  params: z.object({
    name: z.string().optional(),
  }),
});

registry.register("GetUserInvites", getUserInvitesSchema);

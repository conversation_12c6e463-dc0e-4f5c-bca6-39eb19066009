// src/validation/task.ts

import { z } from "zod";
import { registry } from "..";
import { TaskPriority, TaskStatus } from "../types/task";

export const createTaskSchema = z.object({
  body: z.object({
    projectId: z.number().min(1, "Project ID is required"),
    title: z.string().min(1, "Title is required"),
    assigneeId: z.number().optional(),
    description: z.string().optional(),
    category: z.string().optional(),
    status: z.nativeEnum(TaskStatus).optional(),
    priority: z.nativeEnum(TaskPriority).optional(),
    dueDate: z.string().datetime().optional(),
  }),
});

registry.register("CreateTask", createTaskSchema);

export const getTaskSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Task ID is required"),
  }),
});

registry.register("GetTask", getTaskSchema);

export const updateTaskSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Task ID is required"),
  }),
  body: z.object({
    title: z.string().optional(),
    assigneeId: z.number().nullable().optional(),
    description: z.string().optional(),
    category: z.string().optional(),
    status: z.nativeEnum(TaskStatus).optional(),
    priority: z.nativeEnum(TaskPriority).optional(),
    dueDate: z.string().datetime().optional(),
    versionId: z.number().nullable().optional(),
    folderId: z.number().nullable().optional(),
    fileId: z.number().nullable().optional(),
  }),
});

registry.register("UpdateTask", updateTaskSchema);

export const deleteTaskSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Task ID is required"),
  }),
});

registry.register("DeleteTask", deleteTaskSchema);

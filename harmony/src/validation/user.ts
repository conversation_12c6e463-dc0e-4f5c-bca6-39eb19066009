// src/validation/user.ts

import { z } from "zod";
import { registry } from "..";

export const getAllUsersSchema = z.object({
  query: z.object({
    page: z.number().int().positive().min(1, "Page number must be at least 1"),
  }),
});

registry.register("GetAllUsers", getAllUsersSchema);

export const searchUsersSchema = z.object({
  query: z.object({
    q: z.string().min(1, "Query must be at least 1 characters long"),
    includeTopGenres: z.preprocess(
      // Convert string values to boolean
      (val) => {
        if (val === "true") return true;
        if (val === "false") return false;
        if (val === undefined) return false;
        return Boolean(val);
      },
      z.boolean().optional()
    ),
  }),
});

registry.register("SearchUsers", searchUsersSchema);

export const getUserProjectsSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
});

registry.register("GetUserProjects", getUserProjectsSchema);

export const getUserSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
});

registry.register("GetUser", getUserSchema);

export const updateUserSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
  body: z.object({
    email: z.string().email("Invalid email address").optional(),
    name: z.string().optional(),
    role: z.string().optional(), // TODO: Roles enum
    bio: z.string().optional(),
    profileImg: z.string().optional(),
    socials: z
      .array(
        z.object({
          platform: z.string(),
          url: z.string().url(),
        })
      )
      .optional(),
  }),
});

registry.register("UpdateUser", updateUserSchema);

export const deleteUserSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
  body: z.object({
    email: z.string().email("Invalid email address").optional(),
    password: z.string().min(1, "Password is required").optional(),
  }),
});

registry.register("DeleteUser", deleteUserSchema);

export const createLeafletSchema = z.object({
  body: z.object({
    name: z.string().min(1, "Name is required"),
    featured: z.boolean().optional(),
    metadata: z.any().optional(),
  }),
});

registry.register("CreateUserAudioSample", createLeafletSchema);

export const getUserLeafletsSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
});

registry.register("GetUserAudioSamples", getUserLeafletsSchema);

export const getLeafletSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Audio sample ID is required"),
  }),
});

registry.register("GetUserAudioSample", getLeafletSchema);

export const updateLeafletSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Audio sample ID is required"),
  }),
  body: z.object({
    name: z.string().optional(),
    featured: z.boolean().optional(),
    metadata: z.any().optional(),
  }),
});

registry.register("UpdateUserAudioSample", updateLeafletSchema);

export const deleteLeafletSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Audio sample ID is required"),
  }),
});

registry.register("DeleteUserAudioSample", deleteLeafletSchema);

export const getLeafletContentSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Audio sample ID is required"),
  }),
});

registry.register("GetUserAudioSampleContent", getLeafletContentSchema);

// Chats

export const getUserConversationsSchema = z.object({
  params: z.object({
    name: z.string().min(1, "User name is required"),
  }),
  query: z.object({
    type: z.enum(["all", "created", "linked"]).optional(),
    page: z.number().int().positive().min(1, "Page number must be at least 1"),
    pageSize: z.number().int().positive().min(1).max(100).optional(),
  }),
});
registry.register("GetUserConversations", getUserConversationsSchema);

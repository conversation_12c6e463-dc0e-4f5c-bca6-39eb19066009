// src/validation/version.ts

import { z } from "zod";
import { registry } from "..";

export const createVersionSchema = z.object({
  body: z.object({
    projectId: z.number().min(1),
    message: z.string().min(1),
  }),
});

registry.register("CreateVersion", createVersionSchema);

export const getVersionSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Version ID is required"),
  }),
});

registry.register("GetVersion", getVersionSchema);

export const getAllFilesByVersionSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Version ID is required"),
  })
});

registry.register("GetAllFilesByVersion", getAllFilesByVersionSchema);

export const getAllFoldersByVersionSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Version ID is required"),
  }),
});

registry.register("GetAllFoldersByVersion", getAllFoldersByVersionSchema);

export const updateVersionSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Version ID is required"),
  }),
  body: z.object({
    message: z.string().optional(),
  }),
});

registry.register("UpdateVersion", updateVersionSchema);

export const deleteVersionSchema = z.object({
  params: z.object({
    id: z.string().min(1, "Version ID is required"),
  }),
});

registry.register("DeleteVersion", deleteVersionSchema);

import http from "http";
import { Server } from "socket.io";

let io: Server;

export function initializeWebsockets(server: http.Server) {
  io = new Server(server, {
    cors: {
      origin: [
        process.env.FRONTEND_URL || "http://localhost:3000",
        "https://localhost:3000", // Add HTTPS version explicitly
      ],
      methods: ["GET", "POST"],
      credentials: true,
    },
  });

  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);

    socket.on("join", (userId) => {
      console.log(`User ${userId} joined`);
      socket.join(userId.toString());
    });

    // Handle joining project room
    socket.on("join_project", (projectId: number) => {
      const roomName = `project_${projectId}`;
      socket.join(roomName);
      console.log(`Client ${socket.id} joined room: ${roomName}`);
    });

    // <PERSON>le leaving project room
    socket.on("leave_project", (projectId: number) => {
      const roomName = `project_${projectId}`;
      socket.leave(roomName);
      console.log(`Client ${socket.id} left room: ${roomName}`);
    });

    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
    });
  });

  io.on("message", (socket) => {
    console.log("Message received:", socket.id);
  });

  return io;
}

export function getIO(): Server {
  if (!io) {
    throw new Error("Socket.io not initialized");
  }
  return io;
}

create sequence "public"."Comment_id_seq";

create sequence "public"."File_id_seq";

create sequence "public"."ProjectUser_id_seq";

create sequence "public"."Project_id_seq";

create sequence "public"."Task_id_seq";

create sequence "public"."User_id_seq";

create sequence "public"."Version_id_seq";

create table "public"."Comment" (
    "id" bigint not null default nextval('"Comment_id_seq"'::regclass),
    "authorId" bigint not null,
    "projectId" bigint not null,
    "content" text not null,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."File" (
    "id" bigint not null default nextval('"File_id_seq"'::regclass),
    "hash" text not null,
    "name" text not null,
    "versionId" bigint not null,
    "s3Path" text not null,
    "fileType" text not null,
    "fileSize" bigint not null,
    "metadata" jsonb,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."Project" (
    "id" bigint not null default nextval('"Project_id_seq"'::regclass),
    "name" text not null,
    "authorId" bigint not null,
    "description" text,
    "genre" text not null,
    "tempo" integer,
    "key" text,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."ProjectUser" (
    "id" bigint not null default nextval('"ProjectUser_id_seq"'::regclass),
    "projectId" bigint not null,
    "userId" bigint not null,
    "role" text not null,
    "addedAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."Task" (
    "id" bigint not null default nextval('"Task_id_seq"'::regclass),
    "projectId" bigint not null,
    "authorId" bigint not null,
    "assigneeId" bigint,
    "title" text not null,
    "description" text,
    "category" text,
    "status" text,
    "priority" text,
    "dueDate" timestamp(3) without time zone,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."User" (
    "id" bigint not null default nextval('"User_id_seq"'::regclass),
    "sbid" text not null,
    "name" text not null,
    "email" text not null,
    "role" text not null,
    "bio" text,
    "profileImg" text,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP
);


create table "public"."Version" (
    "id" bigint not null default nextval('"Version_id_seq"'::regclass),
    "hash" text not null,
    "projectId" bigint not null,
    "createdAt" timestamp(6) with time zone not null default CURRENT_TIMESTAMP,
    "updatedAt" timestamp(6) without time zone not null default CURRENT_TIMESTAMP,
    "message" text,
    "number" bigint not null
);


alter sequence "public"."Comment_id_seq" owned by "public"."Comment"."id";

alter sequence "public"."File_id_seq" owned by "public"."File"."id";

alter sequence "public"."ProjectUser_id_seq" owned by "public"."ProjectUser"."id";

alter sequence "public"."Project_id_seq" owned by "public"."Project"."id";

alter sequence "public"."Task_id_seq" owned by "public"."Task"."id";

alter sequence "public"."User_id_seq" owned by "public"."User"."id";

alter sequence "public"."Version_id_seq" owned by "public"."Version"."id";

CREATE UNIQUE INDEX "Comment_pkey" ON public."Comment" USING btree (id);

CREATE UNIQUE INDEX "File_hash_key" ON public."File" USING btree (hash);

CREATE UNIQUE INDEX "File_pkey" ON public."File" USING btree (id);

CREATE UNIQUE INDEX "ProjectUser_pkey" ON public."ProjectUser" USING btree (id);

CREATE UNIQUE INDEX "ProjectUser_projectId_userId_key" ON public."ProjectUser" USING btree ("projectId", "userId");

CREATE UNIQUE INDEX "Project_pkey" ON public."Project" USING btree (id);

CREATE UNIQUE INDEX "Task_pkey" ON public."Task" USING btree (id);

CREATE UNIQUE INDEX "User_email_key" ON public."User" USING btree (email);

CREATE UNIQUE INDEX "User_name_key" ON public."User" USING btree (name);

CREATE UNIQUE INDEX "User_pkey" ON public."User" USING btree (id);

CREATE UNIQUE INDEX "User_sbid_key" ON public."User" USING btree (sbid);

CREATE UNIQUE INDEX "Version_hash_key" ON public."Version" USING btree (hash);

CREATE UNIQUE INDEX "Version_pkey" ON public."Version" USING btree (id);

alter table "public"."Comment" add constraint "Comment_pkey" PRIMARY KEY using index "Comment_pkey";

alter table "public"."File" add constraint "File_pkey" PRIMARY KEY using index "File_pkey";

alter table "public"."Project" add constraint "Project_pkey" PRIMARY KEY using index "Project_pkey";

alter table "public"."ProjectUser" add constraint "ProjectUser_pkey" PRIMARY KEY using index "ProjectUser_pkey";

alter table "public"."Task" add constraint "Task_pkey" PRIMARY KEY using index "Task_pkey";

alter table "public"."User" add constraint "User_pkey" PRIMARY KEY using index "User_pkey";

alter table "public"."Version" add constraint "Version_pkey" PRIMARY KEY using index "Version_pkey";

alter table "public"."Comment" add constraint "Comment_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"(id) ON UPDATE CASCADE ON DELETE RESTRICT not valid;

alter table "public"."Comment" validate constraint "Comment_authorId_fkey";

alter table "public"."Comment" add constraint "Comment_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."Comment" validate constraint "Comment_projectId_fkey";

alter table "public"."File" add constraint "File_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "Version"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."File" validate constraint "File_versionId_fkey";

alter table "public"."Project" add constraint "Project_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"(id) ON UPDATE CASCADE ON DELETE RESTRICT not valid;

alter table "public"."Project" validate constraint "Project_authorId_fkey";

alter table "public"."ProjectUser" add constraint "ProjectUser_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."ProjectUser" validate constraint "ProjectUser_projectId_fkey";

alter table "public"."ProjectUser" add constraint "ProjectUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."ProjectUser" validate constraint "ProjectUser_userId_fkey";

alter table "public"."Task" add constraint "Task_assigneeId_fkey" FOREIGN KEY ("assigneeId") REFERENCES "User"(id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."Task" validate constraint "Task_assigneeId_fkey";

alter table "public"."Task" add constraint "Task_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"(id) ON UPDATE CASCADE ON DELETE RESTRICT not valid;

alter table "public"."Task" validate constraint "Task_authorId_fkey";

alter table "public"."Task" add constraint "Task_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."Task" validate constraint "Task_projectId_fkey";

alter table "public"."Version" add constraint "Version_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."Version" validate constraint "Version_projectId_fkey";

grant delete on table "public"."Comment" to "anon";

grant insert on table "public"."Comment" to "anon";

grant references on table "public"."Comment" to "anon";

grant select on table "public"."Comment" to "anon";

grant trigger on table "public"."Comment" to "anon";

grant truncate on table "public"."Comment" to "anon";

grant update on table "public"."Comment" to "anon";

grant delete on table "public"."Comment" to "authenticated";

grant insert on table "public"."Comment" to "authenticated";

grant references on table "public"."Comment" to "authenticated";

grant select on table "public"."Comment" to "authenticated";

grant trigger on table "public"."Comment" to "authenticated";

grant truncate on table "public"."Comment" to "authenticated";

grant update on table "public"."Comment" to "authenticated";

grant delete on table "public"."Comment" to "service_role";

grant insert on table "public"."Comment" to "service_role";

grant references on table "public"."Comment" to "service_role";

grant select on table "public"."Comment" to "service_role";

grant trigger on table "public"."Comment" to "service_role";

grant truncate on table "public"."Comment" to "service_role";

grant update on table "public"."Comment" to "service_role";

grant delete on table "public"."File" to "anon";

grant insert on table "public"."File" to "anon";

grant references on table "public"."File" to "anon";

grant select on table "public"."File" to "anon";

grant trigger on table "public"."File" to "anon";

grant truncate on table "public"."File" to "anon";

grant update on table "public"."File" to "anon";

grant delete on table "public"."File" to "authenticated";

grant insert on table "public"."File" to "authenticated";

grant references on table "public"."File" to "authenticated";

grant select on table "public"."File" to "authenticated";

grant trigger on table "public"."File" to "authenticated";

grant truncate on table "public"."File" to "authenticated";

grant update on table "public"."File" to "authenticated";

grant delete on table "public"."File" to "service_role";

grant insert on table "public"."File" to "service_role";

grant references on table "public"."File" to "service_role";

grant select on table "public"."File" to "service_role";

grant trigger on table "public"."File" to "service_role";

grant truncate on table "public"."File" to "service_role";

grant update on table "public"."File" to "service_role";

grant delete on table "public"."Project" to "anon";

grant insert on table "public"."Project" to "anon";

grant references on table "public"."Project" to "anon";

grant select on table "public"."Project" to "anon";

grant trigger on table "public"."Project" to "anon";

grant truncate on table "public"."Project" to "anon";

grant update on table "public"."Project" to "anon";

grant delete on table "public"."Project" to "authenticated";

grant insert on table "public"."Project" to "authenticated";

grant references on table "public"."Project" to "authenticated";

grant select on table "public"."Project" to "authenticated";

grant trigger on table "public"."Project" to "authenticated";

grant truncate on table "public"."Project" to "authenticated";

grant update on table "public"."Project" to "authenticated";

grant delete on table "public"."Project" to "service_role";

grant insert on table "public"."Project" to "service_role";

grant references on table "public"."Project" to "service_role";

grant select on table "public"."Project" to "service_role";

grant trigger on table "public"."Project" to "service_role";

grant truncate on table "public"."Project" to "service_role";

grant update on table "public"."Project" to "service_role";

grant delete on table "public"."ProjectUser" to "anon";

grant insert on table "public"."ProjectUser" to "anon";

grant references on table "public"."ProjectUser" to "anon";

grant select on table "public"."ProjectUser" to "anon";

grant trigger on table "public"."ProjectUser" to "anon";

grant truncate on table "public"."ProjectUser" to "anon";

grant update on table "public"."ProjectUser" to "anon";

grant delete on table "public"."ProjectUser" to "authenticated";

grant insert on table "public"."ProjectUser" to "authenticated";

grant references on table "public"."ProjectUser" to "authenticated";

grant select on table "public"."ProjectUser" to "authenticated";

grant trigger on table "public"."ProjectUser" to "authenticated";

grant truncate on table "public"."ProjectUser" to "authenticated";

grant update on table "public"."ProjectUser" to "authenticated";

grant delete on table "public"."ProjectUser" to "service_role";

grant insert on table "public"."ProjectUser" to "service_role";

grant references on table "public"."ProjectUser" to "service_role";

grant select on table "public"."ProjectUser" to "service_role";

grant trigger on table "public"."ProjectUser" to "service_role";

grant truncate on table "public"."ProjectUser" to "service_role";

grant update on table "public"."ProjectUser" to "service_role";

grant delete on table "public"."Task" to "anon";

grant insert on table "public"."Task" to "anon";

grant references on table "public"."Task" to "anon";

grant select on table "public"."Task" to "anon";

grant trigger on table "public"."Task" to "anon";

grant truncate on table "public"."Task" to "anon";

grant update on table "public"."Task" to "anon";

grant delete on table "public"."Task" to "authenticated";

grant insert on table "public"."Task" to "authenticated";

grant references on table "public"."Task" to "authenticated";

grant select on table "public"."Task" to "authenticated";

grant trigger on table "public"."Task" to "authenticated";

grant truncate on table "public"."Task" to "authenticated";

grant update on table "public"."Task" to "authenticated";

grant delete on table "public"."Task" to "service_role";

grant insert on table "public"."Task" to "service_role";

grant references on table "public"."Task" to "service_role";

grant select on table "public"."Task" to "service_role";

grant trigger on table "public"."Task" to "service_role";

grant truncate on table "public"."Task" to "service_role";

grant update on table "public"."Task" to "service_role";

grant delete on table "public"."User" to "anon";

grant insert on table "public"."User" to "anon";

grant references on table "public"."User" to "anon";

grant select on table "public"."User" to "anon";

grant trigger on table "public"."User" to "anon";

grant truncate on table "public"."User" to "anon";

grant update on table "public"."User" to "anon";

grant delete on table "public"."User" to "authenticated";

grant insert on table "public"."User" to "authenticated";

grant references on table "public"."User" to "authenticated";

grant select on table "public"."User" to "authenticated";

grant trigger on table "public"."User" to "authenticated";

grant truncate on table "public"."User" to "authenticated";

grant update on table "public"."User" to "authenticated";

grant delete on table "public"."User" to "service_role";

grant insert on table "public"."User" to "service_role";

grant references on table "public"."User" to "service_role";

grant select on table "public"."User" to "service_role";

grant trigger on table "public"."User" to "service_role";

grant truncate on table "public"."User" to "service_role";

grant update on table "public"."User" to "service_role";

grant delete on table "public"."Version" to "anon";

grant insert on table "public"."Version" to "anon";

grant references on table "public"."Version" to "anon";

grant select on table "public"."Version" to "anon";

grant trigger on table "public"."Version" to "anon";

grant truncate on table "public"."Version" to "anon";

grant update on table "public"."Version" to "anon";

grant delete on table "public"."Version" to "authenticated";

grant insert on table "public"."Version" to "authenticated";

grant references on table "public"."Version" to "authenticated";

grant select on table "public"."Version" to "authenticated";

grant trigger on table "public"."Version" to "authenticated";

grant truncate on table "public"."Version" to "authenticated";

grant update on table "public"."Version" to "authenticated";

grant delete on table "public"."Version" to "service_role";

grant insert on table "public"."Version" to "service_role";

grant references on table "public"."Version" to "service_role";

grant select on table "public"."Version" to "service_role";

grant trigger on table "public"."Version" to "service_role";

grant truncate on table "public"."Version" to "service_role";

grant update on table "public"."Version" to "service_role";



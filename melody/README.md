# melody

This contains the frontend for instrumentality.

`src` - Main frontend code
- `app` - App router, code for pages
- `components` - Abstracted components for use in a certain area or globally
- `constants` - Site-wide constants (e.g. colors, typology variants)
- `contexts` - Site-wide contexts, providers, and hooks (e.g. AuthContext)
- `models` - Types representing each of our DB models, enums, as well as response bodies for routes
- `routes` - React Query library of our routes and corresponding query/mutation hooks.
- `utils` - Util functions

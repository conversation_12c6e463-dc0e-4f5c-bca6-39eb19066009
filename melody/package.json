{"name": "melody", "version": "0.1.0", "license": "UNLICENSED", "private": true, "scripts": {"dev": "node scripts/dev.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.1.0", "@mui/base": "^5.0.0-beta.58", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.1", "@tanstack/react-query": "^5.64.2", "axios": "^1.7.7", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^10.14.0", "framer-motion": "^12.12.2", "jszip": "^3.10.1", "next": "14.2.9", "react": "^18", "react-datepicker": "^8.2.1", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-easy-crop": "^5.4.2", "react-firebase-hooks": "^5.1.1", "react-router-dom": "^6.26.2", "socket.io-client": "^4.8.1", "streamsaver": "^2.0.6", "tailwind-merge": "^2.5.5", "zustand": "^5.0.3"}, "devDependencies": {"@types/jszip": "^3.4.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/streamsaver": "^2.0.5", "eslint": "^8", "eslint-config-next": "14.2.9", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}
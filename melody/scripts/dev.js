/* eslint-disable @typescript-eslint/no-var-requires */
const { spawn } = require("child_process");
require("dotenv").config();

// Determine if HTTPS should be enabled
const useHttps = process.env.HTTPS === "true";

// Base command
let command = "next";
let args = ["dev"];

// Add HTTPS arguments if enabled
if (useHttps) {
  args.push("--experimental-https");
  args.push("--experimental-https-key");
  args.push("../localhost-key.pem");
  args.push("--experimental-https-cert");
  args.push("../localhost.pem");
}

// Start Next.js with the appropriate arguments
const nextDev = spawn(command, args, { stdio: "inherit", shell: true });

nextDev.on("error", (err) => {
  console.error("Failed to start Next.js dev server:", err);
  process.exit(1);
});

nextDev.on("close", (code) => {
  process.exit(code);
});

"use client";

import LoginBG from "@/components/auth/login/LoginBG";
import { BaseText } from "@/components/base/BaseText";
import { Fonts } from "@/constants/typology";
import { useLoginMutation } from "@/routes/auth";
import { But<PERSON> } from "@mui/base";
import { Warning } from "@mui/icons-material";
import {
  Alert,
  Box,
  Divider,
  Input,
  Snackbar,
  Typography,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const router = useRouter();

  // Destructure login mutation
  const { mutate: loginUser, isPending, isSuccess, error } = useLoginMutation();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginUser(
      { email, password },
      {
        onSuccess: () => {
          setOpenSnackbar(true);
          // Check if there's a stored redirect path
          const redirectPath =
            localStorage.getItem("redirectAfterLogin") || "/home";
          localStorage.removeItem("redirectAfterLogin"); // Clear it after use
          router.push(redirectPath);
        },
        onError: () => {
          setOpenSnackbar(true);
        },
      }
    );
  };

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isLoginDisabled = !email || !password;

  return (
    <Box className="relative w-screen h-screen overflow-hidden">
      <Box className="absolute inset-0 w-screen">
        <LoginBG />
      </Box>
      <Box className="relative z-10 flex justify-center items-center h-full">
        <form
          onSubmit={handleSubmit}
          className="flex flex-col gap-8 text-white bg-black bg-opacity-50 p-10 items-center rounded-xl w-[500px]"
        >
          <BaseText variant={Fonts.Title200}>beatleaf</BaseText>
          <Divider
            className="w-full bg-white opacity-35"
            orientation="horizontal"
          />
          <Box className="flex flex-col w-full items-center gap-2">
            <Input
              sx={{ color: "white", width: "100%" }}
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={!!email && !emailPattern.test(email)}
              placeholder="Email"
              required
            />
            {email && !emailPattern.test(email) && (
              <Typography variant="caption" color="white">
                <Warning fontSize="inherit" /> Invalid email format
              </Typography>
            )}
          </Box>
          <Input
            sx={{ color: "white", width: "100%" }}
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Password"
            required
          />
          <Box className="flex flex-col w-full items-center gap-2">
            <Button
              type="submit"
              className={`w-1/2 border-gray-500 border-[1px] p-2 rounded-[100px] transition-all duration-100 ${
                isLoginDisabled || isPending
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:rounded-xl hover:w-full hover:bg-[#4338ca] hover:border-[#4338ca]"
              }`}
              disabled={isLoginDisabled || isPending}
            >
              {isPending ? "Logging in..." : "Login"}
            </Button>
            <Button
              type="button"
              className="w-1/2 opacity-50 p-2 rounded-[100px] hover:underline"
              onClick={() => router.push("/register")}
            >
              Register
            </Button>
          </Box>
        </form>
      </Box>
      <Snackbar
        open={openSnackbar || !!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={isSuccess ? "success" : "error"}
          sx={{ width: "100%" }}
        >
          {isSuccess ? "Login successful!" : error?.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Login;

"use client";

import LoginBG from "@/components/auth/login/LoginBG";
import { BaseText } from "@/components/base/BaseText";
import { Fonts } from "@/constants/typology";
import { useSignUpMutation } from "@/routes/auth";
import { Button } from "@mui/base";
import { VisibilityOffOutlined, VisibilityOutlined } from "@mui/icons-material";
import {
  Alert,
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  Input,
  Link,
  Snackbar,
} from "@mui/material";
import axios from "axios";
import crypto from "crypto";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

// Feature flag to disable registration
const REGISTRATION_ENABLED = true;
const REGISTRATION_DISABLED_MESSAGE =
  "Registration is temporarily disabled due to MVP testing.\nPlease check back later.";

// URLs for terms, privacy, and NDA
const TERMS_URL = "https://app.beatleaf.io/terms";
const PRIVACY_URL = "https://app.beatleaf.io/privacy";
const NDA_URL = "https://app.beatleaf.io/nda";

// Function to hash content
const hashContent = (content: string): string => {
  return crypto.createHash("sha256").update(content).digest("hex");
};

const SignUp = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [isBeingVerified, setIsBeingVerified] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [ndaAccepted, setNdaAccepted] = useState(false);
  const [termsContent, setTermsContent] = useState("");
  const [privacyContent, setPrivacyContent] = useState("");
  const [ndaContent, setNdaContent] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const { mutate: signUp, isPending, isSuccess } = useSignUpMutation();

  // Fetch content from URLs
  useEffect(() => {
    const fetchContent = async () => {
      setIsLoading(true);
      try {
        const [termsRes, privacyRes, ndaRes] = await Promise.all([
          axios.get(TERMS_URL),
          axios.get(PRIVACY_URL),
          axios.get(NDA_URL),
        ]);

        setTermsContent(termsRes.data);
        setPrivacyContent(privacyRes.data);
        setNdaContent(ndaRes.data);
      } catch (error) {
        console.error("Error fetching legal documents:", error);
        // Set fallback content in case of fetch failure
        setTermsContent("Terms and Conditions v1.0");
        setPrivacyContent("Privacy Policy v1.0");
        setNdaContent("Non-Disclosure Agreement v1.0");
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Current time for acceptance timestamps
    const currentTime = new Date().toISOString();

    // Combine terms and privacy content for hashing
    const termsPrivacyCombined = `${termsContent}|${privacyContent}`;

    signUp(
      {
        email,
        password,
        termsPrivacyTime: currentTime,
        termsPrivacyContent: hashContent(termsPrivacyCombined),
        ndaTime: currentTime,
        ndaContent: hashContent(ndaContent),
      },
      {
        onSuccess: () => {
          setOpenSnackbar(true);
          setSnackbarMessage(
            "Registration successful! Check email for verification."
          );
          setIsBeingVerified(true);
        },
        onError: () => {
          setOpenSnackbar(true);
          setSnackbarMessage("Registration failed");
        },
      }
    );
  };

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  const isRegisterDisabled =
    !email ||
    !password ||
    !confirmPassword ||
    password !== confirmPassword ||
    !termsAccepted ||
    !ndaAccepted ||
    isLoading;

  return (
    <Box className="relative w-screen h-screen overflow-hidden">
      <Box className="absolute inset-0 w-screen">
        <LoginBG />
      </Box>
      <Box className="relative z-10 flex justify-center items-center h-full">
        {REGISTRATION_ENABLED ? (
          isBeingVerified ? (
            <div className="flex flex-col gap-8 text-white bg-black bg-opacity-50 p-10 items-center rounded-xl w-[500px]">
              <BaseText variant={Fonts.Title200}>beatleaf</BaseText>
              <Divider
                className="w-full bg-white opacity-35"
                orientation="horizontal"
              />
              <BaseText variant={Fonts.BodySemibold} className="text-center">
                A verification email has been sent to {email}!
              </BaseText>
              <BaseText
                variant={Fonts.BodyRegular}
                className="text-center text-slate-400"
              >
                {`Please check your email and click the verification link. If you don't see the email, check your spam folder.`}
              </BaseText>
            </div>
          ) : (
            <form
              onSubmit={handleSubmit}
              className="flex flex-col gap-8 text-white bg-black bg-opacity-50 p-10 items-center rounded-xl w-[500px]"
            >
              <BaseText variant={Fonts.Title200}>beatleaf</BaseText>
              <Divider
                className="w-full bg-white opacity-35"
                orientation="horizontal"
              />
              <Input
                sx={{ color: "white", width: "100%" }}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                required
              />
              <Input
                sx={{ color: "white", width: "100%" }}
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password (min. 6 characters)"
                required
                error={!!password && password.length < 6}
                endAdornment={
                  password && (
                    <Button
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-white opacity-50 hover:opacity-100"
                    >
                      {showPassword ? (
                        <VisibilityOutlined fontSize="small" />
                      ) : (
                        <VisibilityOffOutlined fontSize="small" />
                      )}
                    </Button>
                  )
                }
              />
              <Input
                sx={{ color: "white", width: "100%" }}
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                error={!!confirmPassword && password !== confirmPassword}
                placeholder="Confirm Password"
                required
              />

              <Box className="flex flex-col gap-1">
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={termsAccepted}
                      onChange={(e) => setTermsAccepted(e.target.checked)}
                      sx={{
                        color: "#cbd5e1",
                        "&.Mui-checked": { color: "#cbd5e1" },
                      }}
                    />
                  }
                  label={
                    <span className="text-slate-300 text-sm">
                      I accept the{" "}
                      <Link
                        href="https://app.beatleaf.io/terms"
                        target="_blank"
                        className="text-blue-400 hover:underline"
                      >
                        Terms and Conditions
                      </Link>{" "}
                      and{" "}
                      <Link
                        href="https://app.beatleaf.io/privacy"
                        target="_blank"
                        className="text-blue-400 hover:underline"
                      >
                        Privacy Policy
                      </Link>
                    </span>
                  }
                  className="w-full"
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={ndaAccepted}
                      onChange={(e) => setNdaAccepted(e.target.checked)}
                      sx={{
                        color: "#cbd5e1",
                        "&.Mui-checked": { color: "#cbd5e1" },
                      }}
                    />
                  }
                  label={
                    <span className="text-slate-300 text-sm">
                      I accept the{" "}
                      <Link
                        href="https://app.beatleaf.io/nda"
                        target="_blank"
                        className="text-blue-400 hover:underline"
                      >
                        Non-Disclosure Agreement
                      </Link>
                    </span>
                  }
                  className="w-full"
                />
              </Box>

              <Button
                type="submit"
                className={`w-1/2 border-gray-500 border-[1px] p-2 rounded-[100px] transition-all duration-100 ${
                  isRegisterDisabled || isPending || password.length < 6
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:rounded-xl hover:w-full hover:bg-[#4338ca] hover:border-[#4338ca]"
                }`}
                disabled={
                  isRegisterDisabled || isPending || password.length < 6
                }
              >
                {isPending
                  ? "Signing up..."
                  : isLoading
                  ? "Loading..."
                  : "Register"}
              </Button>
              <Button
                type="button"
                className="w-full opacity-50 p-2 rounded-[100px]"
                onClick={() => router.push("/login")}
              >
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-white hover:underline"
                >
                  Already have an account? Login
                </BaseText>
              </Button>
            </form>
          )
        ) : (
          <div className="flex flex-col gap-8 text-white bg-black bg-opacity-50 p-10 items-center rounded-xl w-[500px]">
            <BaseText variant={Fonts.Title200}>beatleaf</BaseText>
            <Divider
              className="w-full bg-white opacity-35"
              orientation="horizontal"
            />
            <BaseText variant={Fonts.BodyRegular} className="text-center">
              {REGISTRATION_DISABLED_MESSAGE}
            </BaseText>
            <Button
              type="button"
              className="w-full border-gray-500 border-[1px] p-2 rounded-[100px] hover:rounded-xl hover:bg-[#4338ca] hover:border-[#4338ca] transition-all duration-100"
              onClick={() => router.push("/login")}
            >
              <BaseText variant={Fonts.BodyRegular} className="text-white">
                Go to Login
              </BaseText>
            </Button>
          </div>
        )}
      </Box>
      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={isSuccess ? "success" : "error"}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SignUp;

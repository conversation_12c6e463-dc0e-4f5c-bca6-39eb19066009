"use client";

import { BaseText } from "@/components/base/BaseText";
import { Fonts } from "@/constants/typology";
import { KeyboardArrowDown, KeyboardArrowRight } from "@mui/icons-material";
import { AppBar, Box, Toolbar } from "@mui/material";
import { useState } from "react";

export default function HelpPage() {
  return (
    <Box className="flex flex-col items-center m-3 gap-3">
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-left">
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Help
          </BaseText>
        </Toolbar>
      </AppBar>
      <HelpSection title="Getting Started">
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Welcome to beatleaf! To get started, create a new project or explore
          existing ones. Use the navigation bar to access different features and
          settings.
        </BaseText>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          <KeyWord>
            Note: This platform is currently designed for computers, laptops,
            and tablets (on the larger end). Using on a mobile phone is not yet
            supported.
          </KeyWord>
        </BaseText>
      </HelpSection>
      <HelpSection title="Creating a Project" collapsible>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          To create a new project, click on the <KeyWord>Home</KeyWord> button
          on the dashboard. In either list or card view, click on the{" "}
          <KeyWord>+</KeyWord> icon. Follow the prompts to set up your project
          details and preferences.
        </BaseText>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          When creating a project, include the following details:
          <ul className="list-disc list-inside text-slate-400 ml-5">
            <li>Project name</li>
            <li>Project genre</li>
            <li>
              Project tempo <em>(optional)</em>
            </li>
            <li>
              Project key <em>(optional - choose both note and mode)</em>
            </li>
            <li>
              Project description <em>(optional)</em>
            </li>
          </ul>
          {
            "After creation, you will be redirected to the newly created project's dashboard."
          }
        </BaseText>
      </HelpSection>
      <HelpSection title="Navigating the Project Dashboard" collapsible>
        <KeyWord>Outline</KeyWord>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          {"In a project's dashboard, you will see various sections:"}
          <ul className="list-disc list-inside text-slate-400 ml-5">
            <li>Project overview, metadata, and contributors</li>
            <li>Versions and their contents</li>
            <li>Comments</li>
            <li>Tasks</li>
          </ul>
          Below is more in-depth information regarding each section.
        </BaseText>
        <br />
        <KeyWord>Project Overview</KeyWord>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Contains basic information like project name, description, and
          metrics. Can also find the author and contributors of this project.
        </BaseText>
        <br />
        <KeyWord>Contents</KeyWord>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          This section displays the actual content of your project. At the top
          is the dropdown of versions and the current version you are on. Cycle
          through the dropdown to view different versions. The most recent is on
          top.
        </BaseText>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Below the version header is the list of files belonging to the
          selected version. You can view the info of the files and play them (if
          they are audio files).
        </BaseText>
        <br />
        <KeyWord>Comments</KeyWord>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Here, you can view all comments made on the project, and post your
          own. On comments you authored, you can edit and delete.
        </BaseText>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          <KeyWord>(WIP)</KeyWord> If you link projects, versions, and/or files,
          a rich button/link will be created in the comment.
        </BaseText>
        <br />
        <KeyWord>Tasks</KeyWord>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Here, you can view all tasks made for the project, and add your own.
          Once done, check them off and see the progress!
        </BaseText>
      </HelpSection>
      <HelpSection title="Contributing to a Project" collapsible>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Contributing to a project is a easy as uploading a file, folder, or
          creating a new version! To do this:
          <ul className="list-disc list-inside text-slate-400 ml-5">
            <li>{`Click "Upload File/Upload Folder" to add content to a project`}</li>
            <li>{`You can also click the upload icons to the right of a folder name, for nested uploads`}</li>
            <li>{`Click "Create Version" to create a new version. It will copy the contents of the previous version`}</li>
          </ul>
        </BaseText>
      </HelpSection>
      <HelpSection title="User Profile and Discovery" collapsible>
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Your user profile contains your information and projects. You can
          access it by clicking on your avatar/profile picture in the navigation
          bar or elsewhere. Here are some of the things you can do:
          <ul className="list-disc list-inside text-slate-400 ml-5">
            <li>View your projects</li>
            <li>Edit your username and profile picture</li>
            <li>Edit your bio</li>
            <li>
              Add <em>leaflets</em> - these are audio/project samples viewable
              to the public
            </li>
          </ul>
        </BaseText>
        <br />
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          To see other users and their profiles, click the Discovery tab in the
          navbar. You will see a gallery of users in the platform. Each card
          contains:
          <ul className="list-disc list-inside text-slate-400 ml-5">
            <li>Their name and profile picture</li>
            <li>Their top 3 worked on genres</li>
            <li>A preview of their bio</li>
          </ul>
          Click on a user card to view their profile.
        </BaseText>
      </HelpSection>
    </Box>
  );
}

interface HelpSectionProps {
  title: string;
  children: React.ReactNode;
  collapsible?: boolean;
}

const HelpSection: React.FC<HelpSectionProps> = ({
  title,
  children,
  collapsible = false,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <Box
      className="flex flex-col rounded-xl items-start bg-bgdark text-slate-200 w-full h-full p-6 gap-3"
      sx={{
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
      }}
    >
      <Box
        className={`flex w-full justify-between items-center ${
          collapsible ? "cursor-pointer" : ""
        }`}
        onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
      >
        <BaseText variant={Fonts.BodySemibold}>{title}</BaseText>
        {collapsible &&
          (isCollapsed ? (
            <KeyboardArrowRight className="text-slate-400" />
          ) : (
            <KeyboardArrowDown className="text-slate-400" />
          ))}
      </Box>
      {(!isCollapsed || !collapsible) && (
        <Box className="bg-bglight w-full p-3 rounded-lg">{children}</Box>
      )}
    </Box>
  );
};

const KeyWord = ({ children }: { children: React.ReactNode }) => {
  return (
    <span className="inline-block">
      <BaseText variant={Fonts.BodySemibold} className="text-slate-300 inline">
        {children}
      </BaseText>
    </span>
  );
};

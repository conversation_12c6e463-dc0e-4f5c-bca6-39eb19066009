"use client";

import { create } from "zustand";
import ProjectView, {
  ProjectViewType,
} from "../../../components/dashboard/ProjectView";
import { useAuth } from "../../../contexts/AuthContext";

// Define the store with explicit type annotations
interface ProjectViewState {
  projectView: ProjectViewType;
  setProjectView: (view: ProjectViewType) => void;
}

const useProjectViewStore = create<ProjectViewState>((set) => ({
  projectView: ProjectViewType.LIST,
  setProjectView: (view) => set({ projectView: view }),
}));

export default function Home() {
  const { projectView, setProjectView } = useProjectViewStore();
  const { user } = useAuth();

  if (!user?.name) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <ProjectView
      projectView={projectView}
      setProjectView={setProjectView}
      userName={user.name}
    />
  );
}

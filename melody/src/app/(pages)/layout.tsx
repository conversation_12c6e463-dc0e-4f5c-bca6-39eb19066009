"use client";

// app/(pages)/layout.tsx
import ProtectedRoute from "@/components/base/ProtectedRoute";
import { Box } from "@mui/material";
import { ReactNode } from "react";
import Footer from "../../components/base/Footer";
import Navbar from "../../components/base/Navbar";
import { MobileProvider } from "../../contexts/MobileProvider";

export default function ProtectedPagesLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <MobileProvider>
      <ProtectedRoute>
        <Box
          sx={{
            display: "grid",
            gridTemplateRows: "1fr auto",
            minHeight: "100vh",
          }}
        >
          <Navbar />
          <Box component="main" className="mt-20">
            {children}
          </Box>
          <Footer />
        </Box>
      </ProtectedRoute>
    </MobileProvider>
  );
}

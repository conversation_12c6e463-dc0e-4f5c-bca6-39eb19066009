// melody/src/app/(pages)/projects/[id]/settings/page.tsx

"use client";
import { BaseText } from "@/components/base/BaseText";
import Modal from "@/components/base/Modal";
import GenreDropdown from "@/components/project/GenreDropdown";
import KeyDropdown from "@/components/project/KeyDropdown";
import { Fonts } from "@/constants/typology";
import { SbUser, useAuth } from "@/contexts/AuthContext";
import { Project, UpdateProjectBody } from "@/models/project";
import { ProjectInvite } from "@/models/projectInvite";
import { ProjectUser } from "@/models/projectUser";
import {
  useDeleteProjectMutation,
  useGetProjectQuery,
  useRemoveContributorMutation,
  useUpdateProjectMutation,
} from "@/routes/project";
import {
  useCreateProjectInviteMutation,
  useGetProjectInvitesQuery,
  useRevokeProjectInviteMutation,
} from "@/routes/projectInvite";
import { useSearchUsersQuery } from "@/routes/user";
import { buttonStyles } from "@/utils/buttonStyles";
import { Add, ArrowBackIosNew, DeleteRounded, Edit } from "@mui/icons-material";
import {
  AppBar,
  Avatar,
  Box,
  Button,
  CircularProgress,
  Toolbar,
} from "@mui/material";
import { UseMutateFunction } from "@tanstack/react-query";
import dayjs from "dayjs";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import "react-datepicker/dist/react-datepicker.css";

export default function ProjectSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const id = Number(params.id);
  const { data: project, refetch: refetchProject } = useGetProjectQuery(id);
  const { mutate: updateProject } = useUpdateProjectMutation();
  const { user: currentUser } = useAuth();

  return (
    <Box className="flex flex-col items-center m-3">
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl mb-3"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-left">
          <ArrowBackIosNew
            className="text-white cursor-pointer"
            onClick={() => router.back()}
            sx={{
              padding: "1px",
              mr: 2,
              borderRadius: "50%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              "&:hover": {
                backgroundColor: "rgba(100, 116, 139, 0.4)",
              },
            }}
          />
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            {project?.name}
            <BaseText
              variant={Fonts.FootnoteRegular}
              className="text-slate-400"
            >
              Last modified at{" "}
              {dayjs(project?.updatedAt).format("h:mm A, MMM D, YYYY")}
            </BaseText>
          </BaseText>
        </Toolbar>
      </AppBar>

      {project && currentUser && (
        <SettingsDetails
          project={project}
          currentUser={currentUser}
          refetchProject={refetchProject}
          updateProject={updateProject}
        />
      )}
    </Box>
  );
}

interface SettingsDetailsProps {
  project: Project;
  currentUser: SbUser | null;
  refetchProject: () => void;
  updateProject: UseMutateFunction<
    Project,
    Error,
    { id: number } & UpdateProjectBody,
    unknown
  >;
}

const SettingsDetails = ({
  project,
  currentUser,
  refetchProject,
  updateProject,
}: SettingsDetailsProps) => {
  const [isInviteContributorModalOpen, setIsInviteContributorModalOpen] =
    useState(false);
  const [isRemoveContributorModalOpen, setIsRemoveContributorModalOpen] =
    useState(false);
  const [contributorToRemove, setContributorToRemove] =
    useState<ProjectUser | null>(null);
  const [editMode, setEditMode] = useState<null | "basic" | "metadata">(null);
  const [settingsFormData, setSettingsFormData] = useState({
    name: project?.name || "",
    description: project?.description || "--",
    genre: project?.genre || "--",
    tempo: project?.tempo || "--",
    key: project?.key || "--",
  });

  const { mutate: inviteContributor, isPending: pendingInviteContributor } =
    useCreateProjectInviteMutation();
  const { mutate: removeContributor } = useRemoveContributorMutation();
  const { data: projectInvites, refetch: refetchProjectInvites } =
    useGetProjectInvitesQuery(project.id);

  const { mutate: revokeProjectInvite } = useRevokeProjectInviteMutation();

  useEffect(() => {
    if (!editMode) {
      setSettingsFormData({
        name: project?.name,
        description: project?.description || "--",
        genre: project?.genre || "--",
        tempo: project?.tempo || "",
        key: project?.key || "--",
      });
    }
  }, [project, editMode]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setSettingsFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSaveChanges = () => {
    setEditMode(null);
    const cleanTempoValue = settingsFormData.tempo
      ? typeof settingsFormData.tempo === "string"
        ? parseInt(settingsFormData.tempo)
        : settingsFormData.tempo
      : undefined;

    updateProject(
      {
        id: project.id,
        name: settingsFormData.name,
        description: settingsFormData.description,
        genre: settingsFormData.genre,
        tempo: cleanTempoValue,
        key: settingsFormData.key,
      },
      {
        onSuccess: () => {
          refetchProject();
        },
        onError: (error) => {
          console.error("Error updating project:", error);
          alert("Failed to update project. Please try again.");
        },
      }
    );
    refetchProject();
  };

  const [isDeleteProjectModalOpen, setIsDeleteProjectModalOpen] =
    useState(false);
  const handleCloseDeleteModal = () => {
    setIsDeleteProjectModalOpen(false);
  };
  const { mutate: deleteProject } = useDeleteProjectMutation();
  return (
    <Box className="flex flex-col rounded-xl bg-bgdark text-slate-200 w-full h-full p-6 gap-4 mb-3">
      <Box className="flex">
        <BaseText variant={Fonts.HeadlineSemibold} className="mb-2">
          Project Settings
        </BaseText>
      </Box>
      <Box className="flex flex-col bg-bglight p-4 rounded-lg gap-2">
        <Box className="flex">
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Basic Information
          </BaseText>
          {editMode === "basic" ? (
            <Box className="flex gap-2 ml-auto">
              <Button
                variant="contained"
                color="primary"
                className="text-md normal-case"
                sx={buttonStyles.primaryButtonStyles}
                onClick={handleSaveChanges}
              >
                Save
              </Button>
              <Button
                color="primary"
                className="text-md normal-case"
                sx={buttonStyles.outlinedDangerButtonStyles}
                onClick={() => setEditMode(null)}
              >
                Cancel
              </Button>
            </Box>
          ) : (
            <Edit
              className="text-slate-300 hover:text-slate-400 cursor-pointer ml-auto"
              onClick={() => setEditMode("basic")}
            />
          )}
        </Box>

        <Box className={`flex gap-3 ${editMode === "basic" ? "flex-col" : ""}`}>
          <BaseText
            variant={Fonts.BodySemibold}
            className="text-slate-400 w-fit"
          >
            Project Name
          </BaseText>
          {editMode === "basic" ? (
            <input
              type="text"
              name="name"
              value={settingsFormData.name}
              onChange={handleInputChange}
              className="p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 max-w-[300px]"
            />
          ) : (
            <BaseText
              variant={Fonts.BodyRegular}
              className="flex flex-row items-center gap-2 rounded-md w-fit"
            >
              {project.name}
            </BaseText>
          )}
        </Box>
        {/* <Divider className="w-full bg-slate-700 self-center" /> */}
        <Box className="flex gap-3">
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Author
          </BaseText>
          <BaseText
            variant={Fonts.BodyRegular}
            className="flex flex-row items-center gap-2"
          >
            <span>
              <Link
                href="/users/[username]"
                as={`/users/${project?.author.name}`}
              >
                <Avatar
                  src={project?.author.profileImg}
                  sx={{
                    bgcolor: "#4338ca",
                    color: "white",
                    width: "22px",
                    height: "22px",
                    fontSize: "14px",
                  }}
                  popover="auto"
                >
                  {project?.author.name.charAt(0).toUpperCase()}
                </Avatar>
              </Link>
            </span>
            {project?.author.name}
          </BaseText>
        </Box>
        {/* <Divider className="w-full bg-slate-700 self-center" /> */}
        <Box className="mb-1">
          <BaseText
            variant={Fonts.BodySemibold}
            className="text-slate-400 mb-4"
          >
            Description
          </BaseText>
          {editMode === "basic" ? (
            <textarea
              name="description"
              value={settingsFormData.description}
              onChange={handleInputChange}
              className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600"
            />
          ) : (
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              {project.description}
            </BaseText>
          )}
        </Box>
      </Box>

      <Box className="flex flex-col bg-bglight p-4 rounded-lg gap-1">
        <Box className="flex">
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Metadata
          </BaseText>
          {editMode === "metadata" ? (
            <Box className="flex gap-2 ml-auto">
              <Button
                variant="contained"
                color="primary"
                className="text-md normal-case"
                sx={buttonStyles.primaryButtonStyles}
                onClick={handleSaveChanges}
              >
                Save
              </Button>
              <Button
                color="primary"
                className="text-md normal-case"
                sx={buttonStyles.outlinedDangerButtonStyles}
                onClick={() => setEditMode(null)}
              >
                Cancel
              </Button>
            </Box>
          ) : (
            <Edit
              className="text-slate-300 hover:text-slate-400 cursor-pointer ml-auto"
              onClick={() => setEditMode("metadata")}
            />
          )}
        </Box>
        <Box
          className={`flex gap-3 mb-1 ${
            editMode === "metadata" ? "flex-col" : ""
          }`}
        >
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Genre
          </BaseText>
          {editMode === "metadata" ? (
            <Box className="max-w-[250px]">
              <GenreDropdown
                value={settingsFormData.genre}
                onChange={(genre) => {
                  setSettingsFormData((prev) => ({
                    ...prev,
                    genre,
                  }));
                }}
              />
            </Box>
          ) : (
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              {project.genre}
            </BaseText>
          )}
        </Box>
        <Box
          className={`flex gap-3 mb-1 ${
            editMode === "metadata" ? "flex-col" : ""
          }`}
        >
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Tempo
          </BaseText>
          {editMode === "metadata" ? (
            <input
              type="text"
              name="tempo"
              value={settingsFormData.tempo}
              onChange={handleInputChange}
              className="max-w-[250px] p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600"
            />
          ) : (
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              {project.tempo} bpm
            </BaseText>
          )}
        </Box>
        <Box
          className={`flex gap-3 mb-1 ${
            editMode === "metadata" ? "flex-col" : ""
          }`}
        >
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Key
          </BaseText>
          {editMode === "metadata" ? (
            <Box className="max-w-[250px]">
              <KeyDropdown
                value={settingsFormData.key}
                onChange={(key) => {
                  setSettingsFormData((prev) => ({
                    ...prev,
                    key,
                  }));
                }}
              />
            </Box>
          ) : (
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              {project.key || "--"}
            </BaseText>
          )}
        </Box>
        {/* <Divider className="w-1/4 bg-slate-700" /> */}
        <Box className="flex gap-3 mb-1">
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Created at
          </BaseText>
          <BaseText variant={Fonts.BodyRegular}>
            {dayjs(project?.createdAt).format("MMM D, YYYY")}
          </BaseText>
        </Box>
        <Box className="flex gap-3 mb-1">
          <BaseText variant={Fonts.BodySemibold} className="text-slate-400">
            Updated at
          </BaseText>
          <BaseText variant={Fonts.BodyRegular}>
            {dayjs(project?.updatedAt).format("MMM D, YYYY")}
          </BaseText>
        </Box>
      </Box>

      <Box className="bg-bglight p-4 rounded-lg flex-col">
        <Box className="flex flex-row">
          <Box className="w-full">
            <Box className="flex justify-between items-center mb-4">
              <BaseText
                variant={Fonts.HeadlineSemibold}
                className="text-slate-300"
              >
                Contributors
              </BaseText>
              {currentUser?.id === project.author.id && (
                <Button
                  variant="contained"
                  color="primary"
                  className="text-md normal-case"
                  sx={buttonStyles.primaryButtonStyles}
                  onClick={() => setIsInviteContributorModalOpen(true)}
                >
                  <Add className="mr-2 -ml-1" /> Invite Contributor
                </Button>
              )}
            </Box>
            <Box className="text-slate-300 flex flex-col flex-wrap gap-3 mt-2">
              {/* Avatars with names */}
              {project?.contributors.map((contributor, index) => (
                <BaseText
                  key={index}
                  variant={Fonts.BodyRegular}
                  className="flex flex-row items-center gap-2"
                >
                  <span>
                    <Link
                      href={"/users/[username]"}
                      as={`/users/${contributor.user?.name}`}
                    >
                      <Avatar
                        src={contributor.user?.profileImg}
                        sx={{
                          bgcolor: "#4338ca", // This will be randomized later
                          color: "white",
                          width: "22px",
                          height: "22px",
                          fontSize: "14px",
                          transition: "filter 0.2s ease-in-out", // Smooth transition
                          "&:hover": {
                            filter: "brightness(0.8)", // Darkens the avatar on hover
                          },
                        }}
                        popover="auto"
                      >
                        {contributor.user?.name.charAt(0).toUpperCase()}
                      </Avatar>
                    </Link>
                  </span>
                  {contributor.user?.name}
                  {((currentUser?.id === project.author.id &&
                    contributor.user?.id !== project.author.id) ||
                    (currentUser?.id !== project.author.id &&
                      currentUser?.id === contributor.user?.id)) && (
                    <DeleteRounded
                      className="text-slate-400 hover:text-slate-500 cursor-pointer"
                      fontSize="small"
                      onClick={() => {
                        setContributorToRemove(contributor);
                        setIsRemoveContributorModalOpen(true);
                      }}
                    />
                  )}
                </BaseText>
              ))}
            </Box>

            {/* Pending Invites Section */}
            {projectInvites && projectInvites.length > 0 && (
              <Box className="mt-4">
                <BaseText
                  variant={Fonts.BodySemibold}
                  className="text-slate-400 mb-2"
                >
                  Pending Invites
                </BaseText>
                <Box className="text-slate-300 flex flex-row flex-wrap gap-3">
                  {projectInvites.map((invite, index) => (
                    <BaseText
                      key={index}
                      variant={Fonts.BodyRegular}
                      className="flex flex-row items-center gap-2"
                    >
                      <span>
                        <Avatar
                          src={invite.user?.profileImg}
                          sx={{
                            bgcolor: "#6b7280",
                            color: "white",
                            width: "22px",
                            height: "22px",
                            fontSize: "14px",
                          }}
                        >
                          {invite.user?.name.charAt(0).toUpperCase()}
                        </Avatar>
                      </span>
                      {invite.user?.name}
                      <span className="text-slate-400 text-xs">(pending)</span>
                      <DeleteRounded
                        className="text-slate-500 hover:text-slate-400 cursor-pointer italic"
                        fontSize="small"
                        onClick={() => {
                          revokeProjectInvite(invite.id, {
                            onSuccess: () => {
                              refetchProjectInvites();
                            },
                          });
                        }}
                      />
                    </BaseText>
                  ))}
                </Box>
              </Box>
            )}

            {/* Invite Contributor Modal */}
            <InviteContributorModal
              isOpen={isInviteContributorModalOpen}
              onClose={() => setIsInviteContributorModalOpen(false)}
              inviteContributor={inviteContributor}
              pendingInviteContributor={pendingInviteContributor}
              projectId={project.id}
            />

            {/* Remove Contributor Modal */}
            <RemoveContributorModal
              isOpen={isRemoveContributorModalOpen}
              onClose={() => setIsRemoveContributorModalOpen(false)}
              removeContributor={removeContributor}
              projectId={project.id}
              initialContributor={contributorToRemove}
            />
          </Box>
        </Box>
      </Box>

      <Button
        className="flex w-fit p-1 rounded-lg"
        onClick={() => {
          setIsDeleteProjectModalOpen(true);
        }}
        sx={buttonStyles.outlinedDangerButtonStyles}
      >
        <BaseText variant={Fonts.BodySemibold}>Delete Project</BaseText>
        <DeleteRounded className="ml-1 cursor-pointer" />
      </Button>

      <DeleteProjectModal
        isOpen={isDeleteProjectModalOpen}
        onClose={handleCloseDeleteModal}
        project={project}
        deleteProject={deleteProject}
      />
    </Box>
  );
};

const InviteContributorModal = ({
  isOpen,
  onClose,
  inviteContributor,
  pendingInviteContributor,
  projectId,
}: {
  isOpen: boolean;
  onClose: () => void;
  inviteContributor: UseMutateFunction<
    ProjectInvite,
    Error,
    { userId: number; projectId: number },
    unknown
  >;
  pendingInviteContributor: boolean;
  projectId: number;
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const { data: users, isLoading } = useSearchUsersQuery(searchQuery);

  const handleUserSelect = (user: { id: number; name: string }) => {
    setSelectedUser(user);
    setSearchQuery(user.name);
  };

  const handleSubmit = () => {
    if (!selectedUser) {
      alert("Please select a user from the dropdown.");
      return;
    }

    inviteContributor(
      { projectId: projectId, userId: selectedUser.id },
      {
        onSuccess: () => {
          setSearchQuery(""); // Clear the input field
          setSelectedUser(null);
          onClose(); // Close the modal
        },
        onError: (error) => {
          console.error("Error inviting contributor:", error);
          alert("Failed to invite contributor. Please try again.");
        },
      }
    );
  };

  return (
    <Modal title="Invite Contributor" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4">
        Search for a user to invite:
      </BaseText>
      <Box className="flex flex-col gap-4 min-w-[400px]">
        <Box className="relative">
          <input
            type="text"
            placeholder="Search username..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setSelectedUser(null);
            }}
            className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {searchQuery && !selectedUser && (
            <Box className="absolute z-10 w-full mt-1 bg-[#1f2937] rounded-md shadow-lg max-h-[200px] overflow-y-auto">
              {isLoading ? (
                <Box className="p-2 text-slate-400">Loading...</Box>
              ) : users && users.length > 0 ? (
                users.map((user) => (
                  <Box
                    key={user.id}
                    className="p-2 hover:bg-slate-700 cursor-pointer"
                    onClick={() => handleUserSelect(user)}
                  >
                    <Box className="flex items-center gap-2">
                      <Avatar
                        src={user.profileImg}
                        sx={{
                          bgcolor: "#4338ca",
                          color: "white",
                          width: "24px",
                          height: "24px",
                          fontSize: "14px",
                          marginRight: "4px",
                        }}
                      >
                        {user.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <BaseText variant={Fonts.BodySemibold} className="mr-2">
                        {user.name}
                      </BaseText>
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-400"
                      >
                        {user.email}
                      </BaseText>
                    </Box>
                  </Box>
                ))
              ) : (
                <Box className="p-2 text-slate-400">No users found</Box>
              )}
            </Box>
          )}
        </Box>

        {pendingInviteContributor ? (
          <CircularProgress
            size={24}
            color="inherit"
            className="self-center flex mx-auto"
          />
        ) : (
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={{
              backgroundColor: "#4338ca",
              textTransform: "none",
              fontSize: "16px",
            }}
            disabled={!selectedUser || pendingInviteContributor}
            onClick={handleSubmit}
          >
            Invite Contributor
          </Button>
        )}
      </Box>
    </Modal>
  );
};

const RemoveContributorModal = ({
  isOpen,
  onClose,
  removeContributor,
  projectId,
  initialContributor,
}: {
  isOpen: boolean;
  onClose: () => void;
  removeContributor: UseMutateFunction<
    { success: boolean },
    Error,
    { id: number; userName: string },
    unknown
  >;
  projectId: number;
  initialContributor: ProjectUser | null;
}) => {
  const router = useRouter();
  const [contributorToRemove, setContributorToRemove] =
    useState<ProjectUser | null>(initialContributor);

  const { user: currentUser } = useAuth();

  useEffect(() => {
    if (isOpen) {
      setContributorToRemove(initialContributor); // Set the contributor when the modal opens
    } else {
      setContributorToRemove(null); // Reset the state when the modal closes
    }
  }, [isOpen, initialContributor]);

  return (
    <Modal title="Remove Contributor" isOpen={isOpen} onClose={onClose}>
      {currentUser?.id === contributorToRemove?.user.id ? (
        <BaseText variant={Fonts.BodySemibold} className="mb-4">
          Are you sure you want to leave this project?
        </BaseText>
      ) : (
        <BaseText variant={Fonts.BodySemibold} className="mb-4">
          Are you sure you want to remove{" "}
          <span className="text-red-500">
            {contributorToRemove?.user?.name}
          </span>{" "}
          from this project?
        </BaseText>
      )}
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            if (!contributorToRemove) return;

            removeContributor(
              { id: projectId, userName: contributorToRemove.user.name },
              {
                onSuccess: () => {
                  // If you removed yourself, redirect to the projects page
                  if (contributorToRemove.user.id === currentUser?.id) {
                    router.push("/projects");
                  }
                  onClose();
                },
                onError: (error) => {
                  console.error("Error removing contributor:", error);
                  alert("Failed to remove contributor. Please try again.");
                },
              }
            );
          }}
        >
          {currentUser?.id === contributorToRemove?.user.id
            ? "Leave Project"
            : "Remove"}
        </Button>
        <Button
          variant="contained"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

interface DeleteProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project;
  deleteProject: UseMutateFunction<
    { success: boolean },
    Error,
    number,
    unknown
  >;
}
const DeleteProjectModal = ({
  isOpen,
  onClose,
  project,
  deleteProject,
}: DeleteProjectModalProps) => {
  const router = useRouter();
  const [deleteProjectInputValue, setDeleteProjectInputValue] = useState("");
  const handleDeleteInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDeleteProjectInputValue(e.target.value);
  };
  const isDisabled = deleteProjectInputValue !== project.name;
  const handleCancel = () => {
    setDeleteProjectInputValue("");
    onClose();
  };

  return (
    <Modal title="Delete Project?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        {`To delete your project, type "${project.name}".`}
      </BaseText>
      <input
        type="text"
        name="deleteConfirm"
        placeholder={project.name}
        value={deleteProjectInputValue}
        autoComplete="off"
        onChange={handleDeleteInputChange}
        className="p-2 w-fit rounded-md self-center bg-[#1f2937] text-slate-300 border border-slate-600"
      />
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          disabled={isDisabled}
          onClick={() => {
            deleteProject(project.id, {
              onSuccess: () => {
                onClose();
                router.push("/");
              },
              onError: (error) => {
                console.error("Error deleting project:", error);
                alert("Failed to delete project. Please try again.");
              },
            });
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={handleCancel}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

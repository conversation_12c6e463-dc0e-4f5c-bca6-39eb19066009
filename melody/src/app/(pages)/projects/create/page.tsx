// melody/src/app/(pages)/projects/create/page.tsx

"use client";

import { <PERSON><PERSON> } from "@mui/base";
import { ArrowBackIosNew } from "@mui/icons-material";
import {
  Alert,
  AppBar,
  Box,
  CircularProgress,
  FormControl,
  Snackbar,
  TextField,
  Toolbar,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { BaseText } from "../../../../components/base/BaseText";
import GenreDropdown from "../../../../components/project/GenreDropdown";
import KeyDropdown from "../../../../components/project/KeyDropdown";
import { Fonts } from "../../../../constants/typology";
import { useCreateProjectMutation } from "../../../../routes/project";

/*
Mandatory:
- Name
- Genre
Optional:
- Description
- Key
- Tempo
*/

export default function Page() {
  const router = useRouter();
  const {
    mutate: createProjectMutation,
    isPending: pendingCreateProject,
    isSuccess,
  } = useCreateProjectMutation();

  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  const [formData, setFormData] = useState({
    name: "",
    genre: "",
    description: "",
    tempo: "",
    key: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleGenreChange = (genre: string) => {
    setFormData({ ...formData, genre });
  };

  const handleKeyChange = (key: string) => {
    setFormData({ ...formData, key });
  };

  const handleSubmit = async () => {
    if (!formData.name || !formData.genre) {
      setOpenSnackbar(true);
      setSnackbarMessage("Name and genre are required");
      return;
    }

    const formattedData = {
      name: formData.name,
      genre: formData.genre,
      description: formData.description || undefined,
      tempo: formData.tempo ? Number(formData.tempo) : undefined,
      key: formData.key || undefined,
    };

    try {
      createProjectMutation(formattedData, {
        onSuccess: (response) => {
          const justCreatedProjectId = response.id;
          if (justCreatedProjectId) {
            router.push(`/projects/${justCreatedProjectId}`);
          }
        },
        onError: () => {
          setOpenSnackbar(true);
          setSnackbarMessage("Failed to create project");
        },
      });
    } catch (error) {
      console.error("Failed to create project:", error);
      setOpenSnackbar(true);
      setSnackbarMessage("Failed to create project");
    }
  };

  return (
    <Box className="flex flex-col items-center m-3">
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl mb-3"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-left">
          <ArrowBackIosNew
            className="mr-5 text-white cursor-pointer"
            onClick={() => router.back()}
          />
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Create a new project
          </BaseText>
        </Toolbar>
      </AppBar>
      <Box className="flex flex-col rounded-xl items-start bg-bgdark text-slate-200 w-full h-full p-6 gap-2">
        <FormControl className="w-full" margin="none">
          <Box className="flex flex-row gap-4">
            <Box>
              <BaseText variant={Fonts.SubheadlineSemibold} required>
                Project Name
              </BaseText>
              <TextField
                required
                name="name"
                value={formData.name}
                onChange={handleChange}
                hiddenLabel
                className="w-[300px] text-white bg-slate-800 rounded-md"
                variant="filled"
                size="small"
                margin="dense"
                sx={{ input: { color: "white" } }}
              />
            </Box>
            <Box>
              <BaseText variant={Fonts.SubheadlineSemibold} required>
                Project Genre
              </BaseText>
              <Box className="w-[300px] mt-2">
                <GenreDropdown
                  value={formData.genre}
                  onChange={handleGenreChange}
                />
              </Box>
            </Box>
          </Box>
          <Box className="flex flex-row gap-4 pt-3">
            <Box>
              <BaseText variant={Fonts.SubheadlineSemibold}>
                Project Tempo
              </BaseText>
              <TextField
                name="tempo"
                value={formData.tempo}
                onChange={handleChange}
                hiddenLabel
                className="w-[300px] text-white bg-slate-800 rounded-md"
                variant="filled"
                size="small"
                margin="dense"
                sx={{ input: { color: "white" } }}
              />
            </Box>
            <Box>
              <BaseText variant={Fonts.SubheadlineSemibold}>
                Project Key
              </BaseText>
              <Box className="w-[300px] mt-2">
                <KeyDropdown value={formData.key} onChange={handleKeyChange} />
              </Box>
            </Box>
          </Box>
          <BaseText variant={Fonts.SubheadlineSemibold} className="pt-3">
            Project Description
          </BaseText>
          <TextField
            name="description"
            value={formData.description}
            onChange={handleChange}
            hiddenLabel
            className="w-[615px] text-white bg-slate-800 rounded-md"
            variant="filled"
            size="small"
            margin="dense"
            multiline
            minRows={3}
            sx={{
              textarea: { color: "white" },
            }}
          />
          <Button
            className="bg-[#4338ca] rounded-md p-2 mt-6 max-w-[200px] hover:scale-105 transition-all origin-left"
            onClick={handleSubmit}
            disabled={pendingCreateProject}
          >
            <BaseText variant={Fonts.BodySemibold} className="text-white">
              {pendingCreateProject ? (
                <CircularProgress size={16} />
              ) : (
                "Create Project"
              )}
            </BaseText>
          </Button>
        </FormControl>
      </Box>
      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={isSuccess ? "success" : "error"}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}

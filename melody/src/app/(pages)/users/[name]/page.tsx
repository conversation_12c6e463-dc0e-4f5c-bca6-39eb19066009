"use client";

import { BaseText } from "@/components/base/BaseText";
import Modal from "@/components/base/Modal";
import { UploadProgressBar } from "@/components/common/UploadProgressBar";
import {
  Add,
  Cancel,
  Check,
  CircleOutlined,
  Close,
  Delete,
  Edit,
  Event,
  Facebook,
  GitHub,
  Instagram,
  LinkedIn,
  MusicNote,
  PlayArrow,
  Star,
  Stop,
  ThreeP,
  X,
  YouTube,
} from "@mui/icons-material";
import { Avatar, Box, Button } from "@mui/material";
import dayjs from "dayjs";
import { AnimatePresence, motion } from "framer-motion";
import { useParams, useRouter } from "next/navigation";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import Cropper, { Area, Point } from "react-easy-crop";
import { AudioPlayer } from "../../../../components/player/AudioPlayer";
import SocialDropdown, {
  SOCIAL_PLATFORMS,
} from "../../../../components/user/SocialDropdown";
import { BGStyles } from "../../../../constants/colors";
import { Fonts } from "../../../../constants/typology";
import { useAuth } from "../../../../contexts/AuthContext";
import { useUploadProgress } from "../../../../hooks/useUploadProgress";
import { useLogoutMutation } from "../../../../routes/auth";
import { useCreateConversationMutation } from "../../../../routes/chat";
import {
  useCreateLeafletMutation,
  useDeleteLeafletMutation,
  useDeleteUserMutation,
  useGetLeafletContentMutation,
  useGetUserLeafletsQuery,
  useGetUserQuery,
  useUpdateLeafletMutation,
  useUpdateProfileImageMutation,
  useUpdateUserMutation,
} from "../../../../routes/user";
import { buttonStyles } from "../../../../utils/buttonStyles";

const TikTok = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 448 512"
      width="1em"
      height="1em"
      style={{ fontSize: "inherit" }}
      fill="currentColor"
    >
      <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z" />
    </svg>
  );
};

const Soundcloud = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      style={{ fontSize: "inherit" }}
      fill="currentColor"
    >
      <path d="M 14.5 6 C 12.601563 6 11 6.90625 10 8.40625 L 10 17 L 20.5 17 C 22.398438 17 24 15.398438 24 13.5 C 24 11.601563 22.398438 10 20.5 10 C 20.300781 10 20.011719 9.992188 19.8125 10.09375 C 19.210938 7.695313 17 6 14.5 6 Z M 8 8 L 8 17 L 9 17 L 9 8.09375 C 8.699219 7.992188 8.300781 8 8 8 Z M 7 8.09375 C 6.601563 8.195313 6.300781 8.398438 6 8.5 L 6 17 L 7 17 Z M 5 9.40625 C 4.5 9.90625 4.195313 10.488281 4.09375 11.1875 L 4 11.1875 L 4 17 L 5 17 Z M 3 11 C 2.601563 11 2.300781 11.085938 2 11.1875 L 2 16.8125 C 2.300781 16.914063 2.601563 17 3 17 Z M 1 11.8125 C 0.398438 12.3125 0 13.101563 0 14 C 0 14.898438 0.398438 15.6875 1 16.1875 Z"></path>
    </svg>
  );
};

const displayPlatform = (social: { platform: string; url: string }) => {
  const fixedUrl =
    social.url.startsWith("http://") || social.url.startsWith("https://")
      ? social.url
      : `https://${social.url}`;

  switch (social.platform.toLowerCase().trim()) {
    case "twitter":
    case "x":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <X sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "instagram":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <Instagram sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "facebook":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <Facebook sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "tiktok":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <TikTok />
        </a>
      );
    case "youtube":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <YouTube sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "linkedin":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <LinkedIn sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "github":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <GitHub sx={{ fontSize: "inherit" }} />
        </a>
      );
    case "soundcloud":
      return (
        <a href={fixedUrl} target="_blank" rel="noopener noreferrer">
          <Soundcloud />
        </a>
      );
    case "spotify":
    case "bandcamp":
    default:
      // For platforms without specific icons
      return (
        <a
          href={fixedUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-400 hover:underline"
        >
          {SOCIAL_PLATFORMS.find((p) => p.value === social.platform)?.label ||
            social.platform}
        </a>
      );
  }
};

// Enhanced version with particles
const StarSparkle = ({
  active,
  onClick,
}: {
  active: boolean;
  onClick: () => void;
}) => {
  const [showParticles, setShowParticles] = useState(false);

  const handleClick = () => {
    onClick();
    if (!active) {
      setShowParticles(true);
      setTimeout(() => setShowParticles(false), 700);
    }
  };

  return (
    <motion.div className="relative" onClick={handleClick}>
      <Star
        fontSize="small"
        className={active ? "text-yellow-500" : "hover:text-yellow-500"}
      />

      {showParticles && (
        <>
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ x: 0, y: 0, opacity: 1, scale: 0 }}
              animate={{
                x: Math.cos((i * Math.PI) / 4) * 20,
                y: Math.sin((i * Math.PI) / 4) * 20,
                opacity: 0,
                scale: 1,
              }}
              transition={{ duration: 0.7, ease: "easeOut" }}
              className="absolute top-1/2 left-1/2 w-1 h-1 bg-yellow-500 rounded-full"
              style={{ zIndex: 10 }}
            />
          ))}
        </>
      )}

      {active && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1.5, 0],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 0.5,
            times: [0, 0.5, 1],
            repeat: 0,
          }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <div className="absolute w-full h-full rounded-full bg-yellow-500 opacity-30" />
        </motion.div>
      )}
    </motion.div>
  );
};

// Add this validation function
const validateSocialUrl = (platform: string, url: string): boolean => {
  if (!url) return false;

  // Ensure URL has http:// or https:// prefix
  const fixedUrl =
    url.startsWith("http://") || url.startsWith("https://")
      ? url
      : `https://${url}`;

  try {
    const urlObj = new URL(fixedUrl);

    // Platform-specific validation
    switch (platform.toLowerCase()) {
      case "twitter":
        return (
          urlObj.hostname.includes("twitter.com") ||
          urlObj.hostname.includes("x.com")
        );
      case "instagram":
        return urlObj.hostname.includes("instagram.com");
      case "facebook":
        return (
          urlObj.hostname.includes("facebook.com") ||
          urlObj.hostname.includes("fb.com")
        );
      case "tiktok":
        return urlObj.hostname.includes("tiktok.com");
      case "youtube":
        return (
          urlObj.hostname.includes("youtube.com") ||
          urlObj.hostname.includes("youtu.be")
        );
      case "linkedin":
        return urlObj.hostname.includes("linkedin.com");
      case "github":
        return urlObj.hostname.includes("github.com");
      case "soundcloud":
        return urlObj.hostname.includes("soundcloud.com");
      case "spotify":
        return (
          urlObj.hostname.includes("spotify.com") ||
          urlObj.hostname.includes("open.spotify.com")
        );
      case "bandcamp":
        return urlObj.hostname.includes("bandcamp.com");
      default:
        return true; // Allow any URL for unknown platforms
    }
  } catch (e) {
    return false; // Invalid URL format
  }
};

export default function Page() {
  const router = useRouter();
  const params = useParams();

  // Get auth user
  const { user: currentUser, setAuthData } = useAuth();

  const { name } = params;

  const { data: user, error, refetch } = useGetUserQuery(name as string);
  const { mutate: updateUser } = useUpdateUserMutation();

  const isCurrentUser = currentUser?.name === name;
  const [editing, setEditing] = useState(false);
  const [editedName, setEditedName] = useState(user?.name || "");
  const [editedBio, setEditedBio] = useState(user?.bio || "");
  const [editedSocials, setEditedSocials] = useState(user?.socials || []);
  const [newSocialPlatform, setNewSocialPlatform] = useState("");
  const [newSocialUrl, setNewSocialUrl] = useState("");

  // Add these states to your component
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<number | null>(
    null
  );
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [showPlayer, setShowPlayer] = useState(false);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [isAddSampleModalOpen, setIsAddSampleModalOpen] = useState(false);
  const [newSampleName, setNewSampleName] = useState("");
  const [newSampleFile, setNewSampleFile] = useState<File | null>(null);
  const [editingSampleId, setEditingSampleId] = useState<number | null>(null);
  const [editedSampleName, setEditedSampleName] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Add these state variables
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [sampleToDelete, setSampleToDelete] = useState<number | null>(null);

  // Add mutations
  const { mutate: createLeaflet } = useCreateLeafletMutation();
  const { mutate: updateLeaflet } = useUpdateLeafletMutation();
  const { mutate: deleteLeaflet } = useDeleteLeafletMutation();

  const { mutate: getLeafletContent } = useGetLeafletContentMutation();

  // Add state to track validation for each social
  const [socialUrlsValid, setSocialUrlsValid] = useState<boolean[]>([]);
  const [newSocialUrlValid, setNewSocialUrlValid] = useState(false);

  // Add this near your other state variables
  const { uploads } = useUploadProgress();
  const [isUploading, setIsUploading] = useState(false);

  // Add these state variables
  const [isDeleteAccountModalOpen, setIsDeleteAccountModalOpen] =
    useState(false);
  const [deleteAccountEmail, setDeleteAccountEmail] = useState("");
  const [deleteAccountPassword, setDeleteAccountPassword] = useState("");
  const [deleteAccountError, setDeleteAccountError] = useState("");

  // PFP states
  const [isEditAvatarModalOpen, setIsEditAvatarModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);

  // Cropper states
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  // Add these mutations
  const { mutate: deleteUser } = useDeleteUserMutation();
  const { mutate: logoutUser } = useLogoutMutation();

  // Add this handler
  const handleDeleteAccount = () => {
    setDeleteAccountError("");
    deleteUser(
      {
        name: user?.name || "",
        email: deleteAccountEmail,
        password: deleteAccountPassword,
      },
      {
        onSuccess: () => {
          // Logout the user
          logoutUser();
          // Clear auth context
          setAuthData(null);
          // Redirect to home
          router.push("/");
        },
        onError: (error) => {
          console.error("Error deleting account:", error);
          setDeleteAccountError(
            "Failed to delete account. Please check your credentials and try again."
          );
        },
      }
    );
  };

  useEffect(() => {
    if (user) {
      setEditedName(user.name || "");
      setEditedBio(user.bio || "");
      setEditedSocials(user.socials || []);

      // Validate all existing social URLs
      setSocialUrlsValid(
        (user.socials || []).map((social) =>
          validateSocialUrl(social.platform, social.url)
        )
      );
    }
  }, [user]);

  const { data: userLeaflets, refetch: refetchAudioSamples } =
    useGetUserLeafletsQuery(user?.name || "");

  const { mutate: updateProfileImage } = useUpdateProfileImageMutation();

  const { mutate: createDM } = useCreateConversationMutation();

  if (error) {
    router.push("/404");
    return;
  }

  // Add handlers
  const handlePlaySample = (sampleId: number) => {
    if (currentPlayingAudio === sampleId) {
      setShowPlayer(false);
      setCurrentPlayingAudio(null);
      if (audioUrl) {
        setAudioUrl(null);
      }
      return;
    }

    setCurrentPlayingAudio(sampleId);
    // Get the audio content using the mutation
    getLeafletContent(sampleId.toString(), {
      onSuccess: (data) => {
        if (data && data.signedUrl) {
          setAudioUrl(data.signedUrl);
          setWaveformData(data.waveform);
          setShowPlayer(true);
        }
      },
      onError: (error) => {
        console.error("Error fetching audio content:", error);
        setCurrentPlayingAudio(null);
      },
    });
  };

  const handleClosePlayer = () => {
    setShowPlayer(false);
    setCurrentPlayingAudio(null);
    if (audioUrl) {
      setAudioUrl(null);
    }
  };

  const handleAddSample = () => {
    if (newSampleName && newSampleFile) {
      setIsUploading(true);
      createLeaflet(
        { name: newSampleName, file: newSampleFile },
        {
          onSuccess: () => {
            setIsAddSampleModalOpen(false);
            setNewSampleName("");
            setNewSampleFile(null);
            setIsUploading(false);
            refetchAudioSamples();
          },
          onError: (error) => {
            console.error("Error creating leaflet:", error);
            setIsUploading(false);
          },
        }
      );
    }
  };

  const handleUpdateSample = (id: number) => {
    if (editedSampleName) {
      updateLeaflet(
        { id: id.toString(), name: editedSampleName },
        {
          onSuccess: () => {
            setEditingSampleId(null);
            setEditedSampleName("");
            refetchAudioSamples();
          },
        }
      );
    }
  };

  const handleDeleteSample = (id: number) => {
    setSampleToDelete(id);
    setIsDeleteModalOpen(true);
  };

  // Add a function to confirm deletion
  const confirmDeleteSample = () => {
    if (sampleToDelete !== null) {
      deleteLeaflet(sampleToDelete.toString(), {
        onSuccess: () => {
          refetchAudioSamples();
          setIsDeleteModalOpen(false);
          setSampleToDelete(null);
        },
        onError: (error) => {
          console.error("Error deleting sample:", error);
        },
      });
    }
  };

  const handleToggleFeatured = (id: number) => {
    updateLeaflet(
      {
        id: id.toString(),
        featured: !userLeaflets?.find((s) => s.id === id)?.featured,
      },
      {
        onSuccess: () => {
          refetchAudioSamples();
        },
        onError: (error) => {
          console.error("Error toggling featured:", error);
        },
      }
    );
  };

  // TODO:  Add support for editing avatar - depends on S3 support and integration.

  const addSocial = () => {
    if (!newSocialPlatform || !newSocialUrl || !newSocialUrlValid) return;

    setEditedSocials([
      ...editedSocials,
      { platform: newSocialPlatform, url: newSocialUrl },
    ]);
    setSocialUrlsValid([...socialUrlsValid, true]);
    setNewSocialPlatform("");
    setNewSocialUrl("");
    setNewSocialUrlValid(false);
  };

  const removeSocial = (index: number) => {
    const updatedSocials = [...editedSocials];
    updatedSocials.splice(index, 1);
    setEditedSocials(updatedSocials);
  };

  // Add this function near your other handlers
  const handleImageSelect = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // CreateImage function
  const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const image = new window.Image();
      image.addEventListener("load", () => resolve(image));
      image.addEventListener("error", (error) => reject(error));
      image.src = url;
    });

  const getCroppedImg = async (
    imageSrc: string,
    pixelCrop: Area
  ): Promise<File> => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      throw new Error("No 2d context");
    }

    // Set canvas size to the desired width/height
    canvas.width = 400;
    canvas.height = 400;

    // Draw the cropped image
    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      400,
      400
    );

    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error("Canvas is empty"));
          return;
        }
        // Convert blob to File with a proper name and type
        const file = new File([blob], `${user?.name}-${Date.now()}.png`, {
          type: "image/png",
        });
        resolve(file);
      }, "image/png");
    });
  };

  return (
    <Box
      className={`flex flex-col max-w-[1400px] mx-auto items-center m-3 ${
        showPlayer ? "pb-20" : ""
      }`}
    >
      <Box
        className="flex flex-col md:flex-row items-center rounded-xl border-[1px] border-bgdark text-slate-200 w-full h-full p-6 gap-4 mb-3"
        sx={{
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
          background: BGStyles.CHROME_DIM,
        }}
      >
        <Box
          className="flex flex-row mr-4 rounded-[50%] bg-bglight relative"
          sx={{ cursor: isCurrentUser ? "pointer" : "default" }}
        >
          <Avatar
            src={user?.profileImg}
            sx={{
              bgcolor: "#4338ca",
              color: "white",
              cursor: isCurrentUser ? "pointer" : "default",
              width: 175,
              height: 175,
              fontSize: 100,
              transition: "filter 0.3s",
              "&:hover": {
                filter: isCurrentUser ? "brightness(0.8)" : "none",
              },
            }}
            onClick={() => isCurrentUser && router.push("/users/" + name)}
          >
            {user?.name.charAt(0).toUpperCase()}
          </Avatar>
          {isCurrentUser && (
            <Box
              className="absolute inset-0 flex items-center justify-center rounded-[50%] gap-2"
              sx={{
                color: "white",
                bgcolor: "#111827",
                fontSize: 24,
                fontWeight: "bold",
                opacity: 0,
                transition: "opacity 0.3s",
                "&:hover": {
                  opacity: 0.75,
                },
              }}
              onClick={() => setIsEditAvatarModalOpen(true)}
            >
              <Edit />
              Edit
            </Box>
          )}
        </Box>
        <Box className="flex flex-col gap-4 w-full">
          <Box className="flex flex-row gap-4">
            <Box>
              <BaseText variant={Fonts.Title100} className="text-slate-300">
                {!editing && user?.name}
                {isCurrentUser && editing && (
                  <input
                    type="text"
                    value={editedName}
                    onChange={(e) => {
                      setEditedName(e.target.value);
                    }}
                    className="bg-bglight text-slate-300 p-2 rounded-lg"
                  />
                )}
              </BaseText>
              <Box className="flex flex-row gap-3 mt-2 text-slate-400">
                <BaseText variant={Fonts.FootnoteRegular}>
                  <CircleOutlined fontSize="inherit" className="-mt-[1px]" />{" "}
                  {user?.role}
                </BaseText>
                <BaseText variant={Fonts.FootnoteRegular}>
                  <Event fontSize="inherit" className="-mt-[1px]" /> Joined{" "}
                  {dayjs(user?.createdAt).format("MMM D, YYYY")}
                </BaseText>
              </Box>
            </Box>
            {isCurrentUser && (
              <Box className="ml-auto flex flex-row gap-2 items-center">
                {!editing ? (
                  <>
                    <Button
                      sx={buttonStyles.primaryButtonStyles}
                      onClick={() => setEditing(true)}
                    >
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-200 px-2"
                      >
                        Edit Profile
                      </BaseText>
                    </Button>
                  </>
                ) : (
                  <>
                    <Box
                      className={`cursor-pointer ${
                        editedSocials.some((_, i) => !socialUrlsValid[i])
                          ? "bg-gray-600 cursor-not-allowed"
                          : "bg-[#4338ca] hover:bg-bglight"
                      } rounded-lg p-2 px-6 transition-all`}
                      onClick={() => {
                        if (editedSocials.some((_, i) => !socialUrlsValid[i]))
                          return;

                        updateUser({
                          name: user!.name,
                          userData: {
                            name: editedName,
                            bio: editedBio,
                            socials: editedSocials,
                          },
                        });
                        setEditing(false);
                        setAuthData({
                          ...currentUser,
                          id: Number(currentUser?.id),
                          user: {
                            ...currentUser,
                            name: editedName,
                          },
                        });
                        router.push("/users/" + editedName);
                        refetch();
                      }}
                    >
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-200"
                      >
                        Save
                      </BaseText>
                    </Box>
                    <Box
                      className="cursor-pointer bg-red-900 rounded-lg p-2 px-6 hover:bg-bglight transition-all"
                      onClick={() => {
                        setEditedName(user?.name || "");
                        setEditedBio(user?.bio || "");
                        setEditing(false);
                      }}
                    >
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-200"
                      >
                        Cancel
                      </BaseText>
                    </Box>
                  </>
                )}
              </Box>
            )}
            {!isCurrentUser && (
              <Box className="ml-auto flex flex-row gap-2 items-center">
                <Button
                  sx={buttonStyles.primaryButtonStyles}
                  onClick={() =>
                    createDM(
                      {
                        name: `${currentUser!.name}, ${user!.name}`,
                        description: "DM",
                        fromProfile: true,
                        participantIds: [currentUser!.id, user!.id],
                      },
                      {
                        onSuccess: (conversation) =>
                          router.push(`/chat?cid=${conversation.id}`),
                      }
                    )
                  }
                >
                  <Box className="flex flex-row items-center gap-1">
                    <ThreeP fontSize="inherit" className=" ml-1" />
                    <BaseText
                      variant={Fonts.BodyRegular}
                      className="text-slate-200 px-2"
                    >
                      Message
                    </BaseText>
                  </Box>
                </Button>
              </Box>
            )}
          </Box>
          {/* Description Section */}
          <Box className="bg-none rounded-lg flex flex-col gap-2">
            <Box className="flex flex-col w-full gap-4">
              {user?.bio && !editing && (
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-300 whitespace-pre-line"
                >
                  {editedBio}
                </BaseText>
              )}
              {isCurrentUser && editing && (
                <textarea
                  placeholder="Write a bio, your life story, or just a few links...this is your space!"
                  value={editedBio}
                  onChange={(e) => setEditedBio(e.target.value)}
                  className="bg-bgdark text-slate-300 p-2 rounded-lg"
                  rows={4}
                />
              )}
            </Box>
            <Box className="flex flex-row w-full gap-10">
              {false && (
                <Box>
                  <BaseText
                    variant={Fonts.BodySemibold}
                    className="text-slate-400 mb-4"
                  >
                    Email
                  </BaseText>
                  <BaseText
                    variant={Fonts.BodyRegular}
                    className="text-slate-300"
                  >
                    {user?.email}
                  </BaseText>
                </Box>
              )}
            </Box>
            <Box className="flex flex-col w-full gap-1 ">
              <BaseText
                variant={Fonts.SubheadlineSemibold}
                className="text-slate-400"
              >
                Connect
              </BaseText>

              {!editing && (
                <Box className="flex flex-row flex-wrap gap-2 -mt-1">
                  {user?.socials && user.socials.length > 0 ? (
                    user.socials.map((social, index) => (
                      <Box key={index} className="flex items-center">
                        <BaseText
                          variant={Fonts.HeadlineRegular}
                          className="text-slate-300 mr-2"
                        >
                          {displayPlatform(social)}
                        </BaseText>
                      </Box>
                    ))
                  ) : (
                    <BaseText
                      variant={Fonts.BodyRegular}
                      className="text-slate-400"
                    >
                      No social links added.
                    </BaseText>
                  )}
                </Box>
              )}

              {isCurrentUser && editing && (
                <Box className="flex flex-col gap-3">
                  {editedSocials.map((social, index) => (
                    <Box key={index} className="flex items-center gap-2">
                      <Box className="w-1/3">
                        <SocialDropdown
                          value={social.platform}
                          onChange={(platform) => {
                            const updated = [...editedSocials];
                            updated[index].platform = platform;
                            setEditedSocials(updated);

                            // Revalidate URL with new platform
                            const updatedValidity = [...socialUrlsValid];
                            updatedValidity[index] = validateSocialUrl(
                              platform,
                              social.url
                            );
                            setSocialUrlsValid(updatedValidity);
                          }}
                        />
                      </Box>
                      <input
                        type="text"
                        value={social.url}
                        onChange={(e) => {
                          const url = e.target.value;
                          const updated = [...editedSocials];
                          updated[index].url = url;
                          setEditedSocials(updated);

                          // Update validation state
                          const updatedValidity = [...socialUrlsValid];
                          updatedValidity[index] = validateSocialUrl(
                            social.platform,
                            url
                          );
                          setSocialUrlsValid(updatedValidity);
                        }}
                        className={`bg-bgdark text-slate-300 rounded-lg flex-1 h-[36px] px-3 ${
                          social.url && !socialUrlsValid[index]
                            ? "border border-red-500"
                            : ""
                        }`}
                        placeholder="URL"
                      />
                      <Box
                        className="cursor-pointer p-2 rounded-lg bg-red-900 hover:bg-red-800 hover:rounded-[50%] transition-all"
                        onClick={() => removeSocial(index)}
                      >
                        <Close />
                      </Box>
                    </Box>
                  ))}

                  <Box className="flex items-center gap-2 mt-2">
                    <Box className="w-1/3">
                      <SocialDropdown
                        value={newSocialPlatform}
                        onChange={(platform) => {
                          setNewSocialPlatform(platform);
                          // Revalidate URL with new platform
                          setNewSocialUrlValid(
                            validateSocialUrl(platform, newSocialUrl)
                          );
                        }}
                      />
                    </Box>
                    <input
                      type="text"
                      value={newSocialUrl}
                      onChange={(e) => {
                        const url = e.target.value;
                        setNewSocialUrl(url);
                        setNewSocialUrlValid(
                          validateSocialUrl(newSocialPlatform, url)
                        );
                      }}
                      className={`bg-bgdark text-slate-300 rounded-lg flex-1 h-[36px] px-3 ${
                        newSocialUrl && !newSocialUrlValid
                          ? "border border-red-500"
                          : ""
                      }`}
                      placeholder="URL (e.g. https://twitter.com/username)"
                    />
                    <Box
                      className={`cursor-pointer p-2 rounded-lg transition-all ${
                        !newSocialPlatform ||
                        !newSocialUrl ||
                        !newSocialUrlValid
                          ? "bg-gray-600 cursor-not-allowed"
                          : "bg-green-500 hover:bg-green-600 hover:rounded-[50%]"
                      }`}
                      onClick={addSocial}
                    >
                      <Check />
                    </Box>
                  </Box>
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Box>

      {userLeaflets && (
        <Box
          className="flex flex-col rounded-xl bg-bgdark text-slate-200 w-full h-full p-6 gap-4 mb-3"
          sx={{
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
          }}
        >
          <Box className="flex flex-row justify-between items-center mb-2">
            <Box className="flex flex-row items-center gap-3">
              <Box
                className="p-2 rounded-xl"
                sx={{ background: BGStyles.CHROME }}
              >
                <MusicNote fontSize="medium" />
              </Box>
              <Box>
                <BaseText variant={Fonts.HeadlineSemibold}>Leaflets</BaseText>
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-slate-400"
                >
                  {userLeaflets.length} track
                  {userLeaflets.length > 1 ? "s" : ""}
                </BaseText>
              </Box>
            </Box>
            {isCurrentUser && (
              <Button
                sx={buttonStyles.primaryButtonStyles}
                onClick={() => setIsAddSampleModalOpen(true)}
              >
                <Add fontSize="small" className="text-slate-200" />
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-200"
                >
                  Add Leaflet
                </BaseText>
              </Button>
            )}
          </Box>

          <Box className="bg-bglight p-6 pt-4 rounded-lg flex flex-row">
            <Box className="w-full flex flex-col gap-2">
              {userLeaflets?.length === 0 ? (
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-400"
                >
                  {isCurrentUser
                    ? "Add your first leaflet for others to hear!"
                    : "No leaflets to show."}
                </BaseText>
              ) : (
                <AnimatePresence>
                  {userLeaflets?.map((leaflet) => (
                    <motion.div
                      key={leaflet.id}
                      layout
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Box className="flex flex-row justify-between items-center mb-2 border-b border-slate-600 p-3">
                        <Box className="flex flex-row gap-4 items-center">
                          <Box
                            className="cursor-pointer rounded-full bg-[#4338ca] hover:bg-[#3730a3] flex items-center justify-center"
                            onClick={() => handlePlaySample(leaflet.id)}
                            sx={{
                              width: 36,
                              height: 36,
                              minWidth: 36,
                              minHeight: 36,
                            }}
                          >
                            {currentPlayingAudio === leaflet.id ? (
                              <Stop fontSize="small" />
                            ) : (
                              <PlayArrow fontSize="small" />
                            )}
                          </Box>
                          <Box className="flex flex-col">
                            {editingSampleId === leaflet.id ? (
                              <input
                                type="text"
                                value={editedSampleName}
                                onChange={(e) =>
                                  setEditedSampleName(e.target.value)
                                }
                                className="bg-bgdark text-slate-300 p-2 rounded-lg"
                                autoFocus
                              />
                            ) : (
                              <BaseText
                                variant={Fonts.BodySemibold}
                                className="text-slate-300"
                              >
                                {leaflet.name}
                              </BaseText>
                            )}
                            <BaseText
                              variant={Fonts.SubheadlineRegular}
                              className="text-slate-400"
                            >
                              {dayjs(leaflet.updatedAt).format("MMM D, YYYY")}
                            </BaseText>
                          </Box>
                        </Box>

                        {isCurrentUser && (
                          <Box className="flex flex-row gap-2">
                            {editingSampleId === leaflet.id ? (
                              <>
                                <Button
                                  sx={buttonStyles.primaryButtonStyles}
                                  onClick={() => handleUpdateSample(leaflet.id)}
                                >
                                  <BaseText
                                    variant={Fonts.BodyRegular}
                                    className="text-slate-200"
                                  >
                                    Save
                                  </BaseText>
                                </Button>
                                <Button
                                  sx={buttonStyles.outlinedDangerButtonStyles}
                                  onClick={() => {
                                    setEditingSampleId(null);
                                    setEditedSampleName("");
                                  }}
                                >
                                  <BaseText
                                    variant={Fonts.BodyRegular}
                                    className="text-slate-200"
                                  >
                                    Cancel
                                  </BaseText>
                                </Button>
                              </>
                            ) : (
                              <>
                                <Box
                                  className="cursor-pointer rounded-full bg-bglight"
                                  onClick={() => {
                                    handleToggleFeatured(leaflet.id);
                                  }}
                                >
                                  <StarSparkle
                                    active={leaflet.featured}
                                    onClick={() =>
                                      handleToggleFeatured(leaflet.id)
                                    }
                                  />
                                </Box>
                                <Box
                                  className="cursor-pointer rounded-full bg-bglight"
                                  onClick={() => {
                                    setEditingSampleId(leaflet.id);
                                    setEditedSampleName(leaflet.name);
                                  }}
                                >
                                  <Edit
                                    fontSize="small"
                                    className="hover:text-[#4338ca]"
                                  />
                                </Box>
                                <Box
                                  className="cursor-pointer rounded-full bg-bglight"
                                  onClick={() => handleDeleteSample(leaflet.id)}
                                >
                                  <Delete
                                    fontSize="small"
                                    className="hover:text-red-500"
                                  />
                                </Box>
                              </>
                            )}
                          </Box>
                        )}
                      </Box>
                    </motion.div>
                  ))}
                </AnimatePresence>
              )}
            </Box>
          </Box>
        </Box>
      )}

      {/* Danger Zone */}
      {isCurrentUser && (
        <Box
          className="border-[1px] border-[#020617] rounded-xl w-full flex flex-col gap-3 p-6"
          sx={{
            background:
              "radial-gradient(circle at 0% 30%, #02061711 0%, #ef444433 100%)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
          }}
        >
          <Box className="flex flex-row items-center gap-3 mb-2">
            <Cancel className="text-red-500 -mt-1" fontSize="medium" />
            <BaseText variant={Fonts.HeadlineSemibold} className="text-red-500">
              Danger Zone
            </BaseText>
          </Box>
          <Box className="flex flex-row justify-between border-[1px] border-slate-700 p-4 rounded-lg">
            <Box>
              <BaseText className="text-red-500" variant={Fonts.BodySemibold}>
                Delete Account
              </BaseText>
              <BaseText
                className="text-slate-400"
                variant={Fonts.SubheadlineRegular}
              >
                Delete your account and all information associated with it
              </BaseText>
            </Box>
            <Button
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={() => setIsDeleteAccountModalOpen(true)}
            >
              <BaseText variant={Fonts.BodyRegular} className="px-2">
                Delete Account
              </BaseText>
            </Button>
          </Box>
        </Box>
      )}

      {/* Audio Player */}
      {showPlayer && audioUrl && (
        <Box className="fixed bottom-0 left-0 right-0 z-50">
          <AudioPlayer
            fileUrl={audioUrl}
            fileName={
              userLeaflets?.find((sample) => sample.id === currentPlayingAudio)
                ?.name || "Audio Sample"
            }
            onClose={handleClosePlayer}
            waveformData={waveformData}
          />
        </Box>
      )}

      {/* Add Leaflet Modal */}
      <Modal
        title="Add a New Leaflet"
        isOpen={isAddSampleModalOpen}
        onClose={() => {
          if (!isUploading) {
            setIsAddSampleModalOpen(false);
            setNewSampleName("");
            setNewSampleFile(null);
          }
        }}
      >
        <Box className="flex flex-col gap-4 w-[500px] max-w-full">
          <Box className="flex flex-col gap-1">
            <BaseText variant={Fonts.BodySemibold}>
              You can add audio leaflets to your profile - for others to hear!
            </BaseText>
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              Please ensure the file is in a supported audio format.
            </BaseText>
          </Box>

          <Box className="flex flex-col gap-4 mt-4">
            <Box>
              <BaseText variant={Fonts.BodySemibold} className="mb-2">
                Leaflet Name
              </BaseText>
              <input
                type="text"
                value={newSampleName}
                onChange={(e) => setNewSampleName(e.target.value)}
                className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
              />
            </Box>

            <Box>
              <BaseText variant={Fonts.BodySemibold} className="mb-2">
                Audio File
              </BaseText>
              <input
                type="file"
                ref={fileInputRef}
                accept="audio/*"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setNewSampleFile(e.target.files[0]);
                  }
                }}
                className="hidden"
              />
              <Box
                className="cursor-pointer bg-bglight p-2 rounded-lg text-center hover:bg-[#1e293b]"
                onClick={() => fileInputRef.current?.click()}
              >
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-300"
                >
                  {newSampleFile ? newSampleFile.name : "Choose audio file"}
                </BaseText>
              </Box>
            </Box>

            {/* Add upload progress bars */}
            {Object.values(uploads).map((upload) => (
              <UploadProgressBar
                key={upload.id}
                fileName={upload.fileName}
                progress={upload.progress}
                status={upload.status}
              />
            ))}

            <Box className="flex flex-row gap-2 mt-4 w-full">
              <Button
                className="flex-1"
                variant="contained"
                sx={buttonStyles.primaryButtonStyles}
                onClick={handleAddSample}
                disabled={!newSampleName || !newSampleFile || isUploading}
              >
                {isUploading ? "Uploading..." : "Add Leaflet"}
              </Button>
              <Button
                className="flex-1"
                variant="outlined"
                sx={buttonStyles.outlinedDangerButtonStyles}
                onClick={() => {
                  if (!isUploading) {
                    setIsAddSampleModalOpen(false);
                    setNewSampleName("");
                    setNewSampleFile(null);
                  }
                }}
                disabled={isUploading}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        </Box>
      </Modal>

      {/* Delete Leaflet Modal */}
      <Modal
        title="Delete Leaflet"
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSampleToDelete(null);
        }}
      >
        <Box className="flex flex-col gap-2 w-[450px] max-w-full">
          <BaseText variant={Fonts.BodySemibold} className="text-center">
            Are you sure you want to delete this audio leaflet?
          </BaseText>
          <BaseText
            variant={Fonts.BodyRegular}
            className="text-slate-300 text-center"
          >
            This action cannot be undone.
          </BaseText>

          <Box className="flex flex-row gap-2 mt-4 justify-center w-full">
            <Button
              variant="outlined"
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={confirmDeleteSample}
            >
              Delete
            </Button>
            <Button
              variant="contained"
              sx={buttonStyles.primaryButtonStyles}
              onClick={() => {
                setIsDeleteModalOpen(false);
                setSampleToDelete(null);
              }}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Delete Account Modal */}
      <Modal
        title="Delete Account"
        isOpen={isDeleteAccountModalOpen}
        onClose={() => {
          setIsDeleteAccountModalOpen(false);
          setDeleteAccountEmail("");
          setDeleteAccountPassword("");
          setDeleteAccountError("");
        }}
      >
        <Box className="flex flex-col gap-4 w-[500px] max-w-full">
          <BaseText variant={Fonts.BodySemibold} className="text-center">
            Are you sure you want to delete your account?
          </BaseText>
          <BaseText
            variant={Fonts.BodyRegular}
            className="text-slate-300 text-center"
          >
            This action cannot be undone. All your data will be permanently
            deleted.
          </BaseText>

          <Box className="flex flex-col gap-2 mt-2">
            <BaseText variant={Fonts.BodySemibold}>
              Please confirm your email:
            </BaseText>
            <input
              type="email"
              value={deleteAccountEmail}
              onChange={(e) => setDeleteAccountEmail(e.target.value)}
              className="bg-bglight text-slate-300 p-2 rounded-lg w-full"
              placeholder="Enter your email"
            />
          </Box>

          <Box className="flex flex-col gap-2">
            <BaseText variant={Fonts.BodySemibold}>
              Please confirm your password:
            </BaseText>
            <input
              type="password"
              value={deleteAccountPassword}
              onChange={(e) => setDeleteAccountPassword(e.target.value)}
              className="bg-bglight text-slate-300 p-2 rounded-lg w-full"
              placeholder="Enter your password"
            />
          </Box>

          {deleteAccountError && (
            <BaseText variant={Fonts.BodyRegular} className="text-red-500">
              {deleteAccountError}
            </BaseText>
          )}

          <Box className="flex flex-row gap-4 justify-center mt-4">
            <Button
              variant="outlined"
              sx={{
                ...buttonStyles.outlinedDangerButtonStyles,
                ...((!deleteAccountEmail || !deleteAccountPassword) && {
                  backgroundColor: "gray",
                  opacity: 0.5,
                  cursor: "not-allowed",
                }),
              }}
              disabled={!deleteAccountEmail || !deleteAccountPassword}
              onClick={handleDeleteAccount}
            >
              Delete Account
            </Button>
            <Button
              variant="contained"
              sx={buttonStyles.primaryButtonStyles}
              onClick={() => {
                setIsDeleteAccountModalOpen(false);
                setDeleteAccountEmail("");
                setDeleteAccountPassword("");
                setDeleteAccountError("");
              }}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Edit Avatar Modal */}
      <Modal
        title="Edit Profile Picture"
        isOpen={isEditAvatarModalOpen}
        onClose={() => {
          setIsEditAvatarModalOpen(false);
          setSelectedImage(null);
          setImagePreviewUrl(null);
          setCrop({ x: 0, y: 0 });
          setZoom(1);
        }}
      >
        <Box className="flex flex-col gap-4 w-[400px] max-w-full">
          {imagePreviewUrl ? (
            <Box className="flex flex-col items-center">
              <Box
                className="relative w-[300px] h-[300px]"
                sx={{
                  backgroundColor: "transparent",
                  "& .reactEasyCrop_Container": {
                    borderRadius: "50%",
                    overflow: "hidden",
                  },
                }}
              >
                <Cropper
                  image={imagePreviewUrl}
                  crop={crop}
                  zoom={zoom}
                  aspect={1}
                  cropShape="round"
                  showGrid={true}
                  onCropChange={setCrop}
                  onZoomChange={setZoom}
                  onCropComplete={(croppedArea, croppedAreaPixels) => {
                    setCroppedAreaPixels(croppedAreaPixels);
                  }}
                  cropSize={{ width: 300, height: 300 }}
                  objectFit="vertical-cover"
                  minZoom={1}
                  maxZoom={3}
                  restrictPosition={true}
                />
              </Box>

              {/* Zoom Slider */}
              <Box className="w-full mt-4 px-4">
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-300 mb-2"
                >
                  Zoom
                </BaseText>
                <input
                  type="range"
                  value={zoom}
                  min={1}
                  max={3}
                  step={0.1}
                  onChange={(e) => setZoom(Number(e.target.value))}
                  className="w-full accent-[#4338ca]"
                />
              </Box>
            </Box>
          ) : (
            <Box className="flex flex-col items-center">
              <BaseText
                variant={Fonts.BodyRegular}
                className="text-slate-400 text-center"
              >
                No image selected. Choose an image to preview.
              </BaseText>
            </Box>
          )}

          <input
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            className="hidden"
            id="avatar-upload"
          />
          <label
            htmlFor="avatar-upload"
            className="cursor-pointer bg-bglight p-2 rounded-lg text-center hover:bg-[#1e293b]"
          >
            <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
              {selectedImage ? "Choose different image" : "Choose image file"}
            </BaseText>
          </label>

          <Box className="flex flex-row gap-2 mt-4 w-full">
            <Button
              className="flex-1"
              variant="contained"
              sx={buttonStyles.primaryButtonStyles}
              onClick={async () => {
                if (imagePreviewUrl && croppedAreaPixels) {
                  try {
                    const croppedImage = await getCroppedImg(
                      imagePreviewUrl,
                      croppedAreaPixels
                    );

                    updateProfileImage(
                      {
                        name: user?.name || "",
                        file: croppedImage,
                      },
                      {
                        onSuccess: (data) => {
                          refetch();
                        },
                        onError: (error) => {
                          console.error("Error updating profile image:", error);
                        },
                      }
                    );

                    setIsEditAvatarModalOpen(false);
                    setSelectedImage(null);
                    setImagePreviewUrl(null);
                    setCrop({ x: 0, y: 0 });
                    setZoom(1);
                  } catch (e) {
                    console.error("Error cropping image:", e);
                  }
                }
              }}
              disabled={!selectedImage || !croppedAreaPixels}
            >
              Update Picture
            </Button>
            <Button
              className="flex-1"
              variant="outlined"
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={() => {
                setIsEditAvatarModalOpen(false);
                setSelectedImage(null);
                setImagePreviewUrl(null);
                setCrop({ x: 0, y: 0 });
                setZoom(1);
              }}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </Modal>
    </Box>
  );
}

"use client";

import { BaseText } from "@/components/base/BaseText";
import { UserCard } from "@/components/user/UserCard";
import { Fonts } from "@/constants/typology";
import { useAuth } from "@/contexts/AuthContext";
import { useGetPaginatedUsersQuery, useSearchUsersQuery } from "@/routes/user";
import { AppBar, Box, Pagination, Toolbar } from "@mui/material";
import { useEffect, useState } from "react";

const USERS_PER_PAGE = 12;

export default function Page() {
  const { user: currentUser } = useAuth();
  const [inputValue, setInputValue] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);

  // Use search query when there's input, otherwise get paginated users
  const { data: searchResults, isLoading: searchLoading } = useSearchUsersQuery(
    searchQuery,
    true
  );
  const { data: paginatedData, isLoading: paginatedLoading } =
    useGetPaginatedUsersQuery({
      page,
      limit: USERS_PER_PAGE,
    });

  // Debounce search query updates
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(inputValue);
      if (inputValue.length > 0) setPage(1); // Reset to first page on new search
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [inputValue]);

  // Determine which data set to use
  const isSearchMode = searchQuery.length > 0;
  const users = isSearchMode ? searchResults : paginatedData?.users;
  const isLoading = isSearchMode ? searchLoading : paginatedLoading;
  const totalPages = isSearchMode
    ? searchResults
      ? Math.ceil(searchResults.length / USERS_PER_PAGE)
      : 0
    : paginatedData?.totalPages || 0;

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <Box className="flex flex-col items-center m-3">
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl mb-3"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-between">
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Users
          </BaseText>
        </Toolbar>
      </AppBar>

      <Box className="flex flex-row justify-center mt-2 mb-4 w-full px-6">
        <input
          type="text"
          placeholder="Search username..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className="w-full p-2 flex self-center rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </Box>

      <Box className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 w-full px-4">
        {isLoading ? (
          <Box className="p-2 text-slate-400">Loading users...</Box>
        ) : users && users.length > 0 ? (
          users.map((user) => (
            <UserCard
              key={user.id}
              user={user}
              isCurrentUser={currentUser?.id === user.id}
            />
          ))
        ) : (
          <Box className="p-2 text-slate-400">
            {isSearchMode
              ? "No users found matching your search"
              : "No users found"}
          </Box>
        )}
      </Box>

      {!isSearchMode && totalPages > 1 && (
        <Box className="mt-6 mb-4">
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            variant="outlined"
            shape="rounded"
            sx={{
              "& .MuiPaginationItem-root": {
                color: "#94a3b8",
                borderColor: "#475569",
              },
              "& .Mui-selected": {
                backgroundColor: "#4338ca",
                borderColor: "#4338ca",
                "&:hover": {
                  backgroundColor: "#3730a3",
                },
              },
            }}
          />
        </Box>
      )}
    </Box>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Instrument+Sans:ital,wght@0,400..700;1,400..700&display=swap");

/* Add this near the top of your CSS file */
@media (prefers-color-scheme: dark) {
  html {
    /* Prevents Dark Reader from inverting colors */
    -webkit-filter: none !important;
    filter: none !important;
  }
}

/* Add this meta tag to prevent Dark Reader from affecting your site */
html[data-darkreader-mode] {
  color-scheme: light !important;
}

/* Force Dark Reader to ignore specific elements if needed */
[data-darkreader-inline-bgcolor],
[data-darkreader-inline-color],
[data-darkreader-inline-border],
[data-darkreader-inline-bgimage] {
  /* Reset any Dark Reader inline styles */
  all: revert !important;
}

:root {
  --background: #131728;
  --foreground: #000000;
  background: var(--background);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Instrument Sans", sans-serif;
  overflow-y: scroll;
  padding-right: 0 !important;
  /* Prevent content shift when modal opens */
}

/* Prevent content shift when modal opens */
.MuiModal-root {
  padding-right: 0 !important;
}

/* Prevent AppBar background shift */
.MuiAppBar-root {
  padding-right: 0 !important;
}

/* Prevent background shift */
.MuiModal-backdrop {
  width: 100% !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* width */
::-webkit-scrollbar {
  width: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #000022;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #333a49;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-from-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

#status-dropdown {
  position: absolute;
  background-color: #1f2937;
  color: #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Add notification bell wobble animation */
@keyframes bellWobble {
  0%,
  100% {
    transform: rotate(0);
  }
  20% {
    transform: rotate(8deg);
  }
  40% {
    transform: rotate(-8deg);
  }
  60% {
    transform: rotate(4deg);
  }
  80% {
    transform: rotate(-4deg);
  }
}

.bell-wobble:hover {
  animation: bellWobble 0.5s ease;
  transform-origin: top center;
}

/* Add notification bell wobble animation */
@keyframes bellWobble {
  0%,
  100% {
    transform: rotate(0);
  }
  20% {
    transform: rotate(8deg);
  }
  40% {
    transform: rotate(-8deg);
  }
  60% {
    transform: rotate(4deg);
  }
  80% {
    transform: rotate(-4deg);
  }
}

.bell-wobble:hover {
  animation: bellWobble 0.5s ease;
  transform-origin: top center;
}

/* Avatar image styles */
.MuiAvatar-root {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.MuiAvatar-root .MuiAvatar-img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Target all MUI buttons when disabled */
.MuiButton-root.Mui-disabled {
  /* Keep the button visible but desaturate it */
  opacity: 0.7 !important;
  filter: grayscale(100%) !important;

  /* Override default disabled styles */
  background-color: inherit !important;
  /* Preserves original background */
  color: rgba(255, 255, 255, 0.55) !important;
  /* Faded text */
  border: 1px solid rgba(255, 255, 255, 0.75) !important;
  /* Faded border */
  pointer-events: none !important;
  /* Prevents clicking */
}

/* If you need to override specific MUI button variants */
.MuiButton-contained.Mui-disabled {
  background-color: inherit !important;
}

.MuiButton-outlined.Mui-disabled {
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
}

/* Add this to your globals.css file */
@keyframes fileHighlightFadeOut {
  0% {
    background-color: rgba(59, 130, 246, 0.3); /* blue-500 with opacity */
  }
  100% {
    background-color: rgba(59, 130, 246, 0);
  }
}

@keyframes folderHighlightFadeOut {
  0% {
    background-color: rgba(234, 179, 8, 0.3); /* yellow-500 with opacity */
  }
  100% {
    background-color: rgba(234, 179, 8, 0);
  }
}

.file-highlight {
  animation: fileHighlightFadeOut 2s ease-out 1s forwards;
}

.folder-highlight {
  animation: folderHighlightFadeOut 2s ease-out 1s forwards;
}

/* Crop container styles */
.reactEasyCrop_Container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.reactEasyCrop_CropArea {
  width: 100%;
  height: 100%;
  border: 2px solid #4338ca; /* Indigo color to match your theme */
  border-radius: 50%;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.9);
}

.reactEasyCrop_Image {
  max-width: none !important;
  touch-action: none;
  user-select: none;
}

/* Add styles for the cropper controls */
.reactEasyCrop_Contain {
  max-width: 100%;
  max-height: 100%;
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

import "../lib/axios-config"; // Import at the top to initialize axios

import { AuthProvider } from "@/contexts/AuthContext";
import { SocketProvider } from "@/contexts/SocketContext";
import { ThemeProvider } from "@mui/material";
import { QueryClientProvider } from "@tanstack/react-query";
import { twMerge } from "tailwind-merge";
import CsrfTokenProvider from "../components/auth/CsrfTokenProvider";
import TokenRefresher from "../components/auth/TokenRefresher";
import { instrumentSans } from "../constants/fonts";
import CookieCheck from "../contexts/CookieCheck";
import { theme } from "../contexts/ThemeRegistry";
import { queryClient } from "../routes/queryClient";
import "./globals.css";

export const metadata = {
  title: "beatleaf",
  description: "Music production for the modern, connected age.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      className={twMerge(
        `${instrumentSans.variable}`,
        "w-full h-full flex flex-col bg-white"
      )}
      lang="en"
      suppressHydrationWarning
      data-darkreader-mode="off"
    >
      <head>
        <meta name="darkreader-lock" content="on" />
      </head>
      <body className={"antialiased"}>
        <ThemeProvider theme={theme}>
          <QueryClientProvider client={queryClient}>
            <CookieCheck>
              <AuthProvider>
                <SocketProvider>
                  <TokenRefresher />
                  <CsrfTokenProvider />
                  {children}
                </SocketProvider>
              </AuthProvider>
            </CookieCheck>
          </QueryClientProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

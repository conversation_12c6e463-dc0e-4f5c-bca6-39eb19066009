import fs from "fs";
import path from "path";

// Function to read the HTML file
function getNdaHtml() {
  const filePath = path.join(process.cwd(), "src/app/nda/nda.html");
  try {
    const fileContent = fs.readFileSync(filePath, "utf8");
    return fileContent;
  } catch (error) {
    console.error("Error reading NDA HTML file:", error);
    return "<p>Non-Disclosure Agreement content could not be loaded.</p>";
  }
}

export default function Page() {
  const ndaHtml = getNdaHtml();

  return (
    <div className="flex bg-white p-10 justify-center">
      <div
        className="prose w-full max-w-[1000px]"
        dangerouslySetInnerHTML={{ __html: ndaHtml }}
      />
    </div>
  );
}

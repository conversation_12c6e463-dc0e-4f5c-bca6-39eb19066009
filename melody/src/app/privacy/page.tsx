import fs from "fs";
import path from "path";

// Function to read the HTML file
function getPrivacyHtml() {
  const filePath = path.join(process.cwd(), "src/app/privacy/privacy.html");
  try {
    const fileContent = fs.readFileSync(filePath, "utf8");
    return fileContent;
  } catch (error) {
    console.error("Error reading privacy HTML file:", error);
    return "<p>Privacy policy content could not be loaded.</p>";
  }
}

export default function Page() {
  // Read the HTML content at build time
  const privacyHtml = getPrivacyHtml();

  return (
    <div className="flex bg-white p-10 justify-center">
      <div
        className="prose w-full max-w-[1000px]"
        dangerouslySetInnerHTML={{ __html: privacyHtml }}
      />
    </div>
  );
}

import fs from "fs";
import path from "path";

// Function to read the HTML file
function getTermsHtml() {
  const filePath = path.join(process.cwd(), "src/app/terms/terms.html");
  try {
    const fileContent = fs.readFileSync(filePath, "utf8");
    return fileContent;
  } catch (error) {
    console.error("Error reading terms HTML file:", error);
    return "<p>Terms and Conditions content could not be loaded.</p>";
  }
}

export default function Page() {
  const termsHtml = getTermsHtml();

  return (
    <div className="flex bg-white p-10 justify-center">
      <div
        className="prose w-full max-w-[1000px]"
        dangerouslySetInnerHTML={{ __html: termsHtml }}
      />
    </div>
  );
}

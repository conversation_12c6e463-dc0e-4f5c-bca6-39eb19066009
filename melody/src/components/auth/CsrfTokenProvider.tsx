"use client";

import axios from "axios";
import { useEffect } from "react";
import { API_URL } from "../../lib/axios-config";
import { useCsrfToken } from "../../routes/auth";

export default function CsrfTokenProvider() {
  const { refetch } = useCsrfToken();

  useEffect(() => {
    // Function to fetch and store CSRF token
    const fetchAndStoreToken = async () => {
      try {
        const { data } = await axios.get(`${API_URL}/csrf-token`, {
          withCredentials: true,
        });

        // Store in localStorage as fallback for users with cookies disabled
        if (data.csrfToken) {
          localStorage.setItem("csrf_token", data.csrfToken);
        }
      } catch (error) {
        console.error("Failed to fetch CSRF token:", error);
      }
    };

    // Fetch CSRF token immediately on mount, regardless of auth state
    refetch();
    fetchAndStoreToken();

    // Set up a timer to refresh the token periodically (every 30 minutes)
    const intervalId = setInterval(() => {
      refetch();
      fetchAndStoreToken();
    }, 30 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [refetch]); // Remove isAuthenticated dependency to ensure token is fetched on mount

  // This component doesn't render anything
  return null;
}

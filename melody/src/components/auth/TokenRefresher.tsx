"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useTokenRefresh } from "@/hooks/useTokenRefresh";
import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";

export default function TokenRefresher() {
  const { isAuthenticated } = useAuth();
  const pathname = usePathname();
  const isAuthPage =
    pathname?.includes("/login") || pathname?.includes("/register");

  // Set up automatic token refresh
  const { refreshToken } = useTokenRefresh();
  const initialRefreshDone = useRef(false);

  useEffect(() => {
    // Only refresh token on mount if authenticated and not on auth pages
    if (!initialRefreshDone.current && isAuthenticated && !isAuthPage) {
      refreshToken();
      initialRefreshDone.current = true;
    }
    // The useTokenRefresh hook will handle the periodic refreshes
  }, [refreshToken, isAuthenticated, isAuthPage]);

  // This component doesn't render anything
  return null;
}

/**
 * Blurred vector graphic background for the login page.
 * <AUTHOR>
 */
export default function LoginBG() {
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        background: "linear-gradient(140deg, #000000 0%, #111317 100%)",
        overflow: "hidden",
      }}
    >
      {/* Add a simpler blur effect that works better on mobile */}
      <div
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          background:
            "radial-gradient(circle at 30% 30%, #6257d255 0%, transparent 70%)",
        }}
      />
      <div
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          background:
            "radial-gradient(circle at 80% 60%, #28839a4d 0%, transparent 60%)",
        }}
      />
      <div
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          backdropFilter: "blur(80px)",
          WebkitBackdropFilter: "blur(80px)",
        }}
      />
    </div>
  );
}

import { Typography } from "@mui/material";
import React from "react";
import { twMerge } from "tailwind-merge";
import { instrumentSans } from "../../constants/fonts";
import { FontConfigs, Fonts } from "../../constants/typology";

interface BaseTextProps {
  variant: Fonts;
  inline?: boolean;
  className?: string;
  required?: boolean;
  children: React.ReactNode;
  onClick?: (
    event: React.MouseEvent<HTMLSpanElement | HTMLDivElement, MouseEvent>
  ) => void; // Allow onClick for flexibility, if needed
  [key: string]: unknown; // Allow additional props with safer type
}

export const BaseText: React.FC<BaseTextProps> = ({
  variant,
  inline = false,
  className,
  required,
  children,
  onClick, // Optional onClick prop
  ...props // Spread additional props
}) => {
  return inline ? (
    <span>
      <Typography
        component="span"
        className={twMerge(className, instrumentSans.className)}
        sx={{
          fontSize: FontConfigs[variant].size,
          lineHeight: FontConfigs[variant].leading,
          fontWeight: FontConfigs[variant].weight,
          fontFamily: `var(--font-instrument-sans)`,
        }}
        onClick={onClick} // Pass the onClick handler to Typography
        {...props} // Spread additional props to Typography
      >
        {children} {required && <span className="text-[#14b8a6]">*</span>}
      </Typography>
    </span>
  ) : (
    <Typography
      component="div"
      className={twMerge(className, instrumentSans.className)}
      sx={{
        fontSize: FontConfigs[variant].size,
        lineHeight: FontConfigs[variant].leading,
        fontWeight: FontConfigs[variant].weight,
        fontFamily: `var(--font-instrument-sans)`,
      }}
      onClick={onClick} // Pass the onClick handler to Typography
      {...props} // Spread additional props to Typography
    >
      {children} {required && <span className="text-[#14b8a6]">*</span>}
    </Typography>
  );
};

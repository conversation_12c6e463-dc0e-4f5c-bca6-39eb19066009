import { Box } from "@mui/material";
import React, { ReactNode } from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "./BaseText";

export interface CardProps {
  title: string;
  content: ReactNode;
  imageComponent?: ReactNode;
}

const Card: React.FC<CardProps> = ({ title, content, imageComponent }) => {
  return (
    <Box
      className="w-[180px] border-slate-700 border-[1px] rounded-lg overflow-hidden cursor-pointer justify-center shadow-lg bg-[#131728] m-4 transition-all duration-200 hover:scale-105"
      sx={{
        background: "linear-gradient(145deg, #151b2d, #0f1420)",
        boxShadow: "0 8px 16px rgba(0, 0, 0, 0.3)",
        border: "1px solid rgba(255, 255, 255, 0.05)",
        "&:hover": {
          transform: "translateY(-5px)",
          boxShadow: "0 12px 20px rgba(0, 0, 0, 0.4)",
        },
      }}
    >
      <Box className="flex flex-col items-center p-4">
        <BaseText
          variant={Fonts.HeadlineSemibold}
          className="pb-4 text-center text-slate-300"
        >
          {title}
        </BaseText>
        {imageComponent && (
          <Box className="flex justify-center mb-4">{imageComponent}</Box>
        )}
        {content}
      </Box>
    </Box>
  );
};

export default Card;

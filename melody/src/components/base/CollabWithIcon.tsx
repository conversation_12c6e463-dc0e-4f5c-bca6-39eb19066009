import { Box, Typography } from "@mui/material";
import React from "react";
import { PersonIcon } from "../icons";

interface CollabWithIconProps {
  collaborators: number;
  iconSize: number;
  cardView: boolean;
}

const CollabWithIcon: React.FC<CollabWithIconProps> = ({
  collaborators,
  iconSize,
  cardView,
}) => {
  return (
    <Box className={`flex ${cardView ? "flex-col" : "flex-row"} gap-3`}>
      <PersonIcon size={iconSize} />
      <Typography className="text-[14px] text-center">
        {collaborators}
      </Typography>
    </Box>
  );
};

export default CollabWithIcon;

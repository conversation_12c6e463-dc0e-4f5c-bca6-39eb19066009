import { Box, Typography } from "@mui/material";
import React from "react";
import { DurationIcon } from "../icons/DurationIcon";

interface DurationWithIconProps {
  duration: string;
  iconSize: number;
  cardView: boolean;
}

const DurationWithIcon: React.FC<DurationWithIconProps> = ({
  duration,
  iconSize,
  cardView,
}) => {
  return (
    <Box className={`flex ${cardView ? "flex-col" : "flex-row"} gap-3`}>
      <DurationIcon size={iconSize} />
      <Typography className="text-[14px] text-center">{duration}</Typography>
    </Box>
  );
};

export default DurationWithIcon;

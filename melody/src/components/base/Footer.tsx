import { Box } from "@mui/material";
import Link from "next/link";
import { Fonts } from "../../constants/typology";
import { BeatleafIcon } from "../icons/Beatleaf";
import { BaseText } from "./BaseText";

export default function Footer() {
  return (
    <Box
      component="footer"
      sx={{
        padding: 2,
        borderTop: 1,
        borderColor: "divider",
        marginTop: 3,
        height: 100,
      }}
      className="flex flex-col bg-bgdark justify-center items-center gap-2 py-auto"
    >
      <Box className="flex flex-row items-center">
        <BeatleafIcon size={26} />
        <BaseText variant={Fonts.HeadlineSemibold} className="text-[#4338ca]">
          beatleaf
        </BaseText>
      </Box>
      <Box className="flex flex-row justify-center">
        <BaseText
          variant={Fonts.SubheadlineRegular}
          className="text-slate-500 text-center px-3"
        >
          © 2025 Beatleaf. All rights reserved.
        </BaseText>
        <BaseText
          variant={Fonts.SubheadlineRegular}
          className="text-slate-500 border-l-[1px] border-l-slate-500 text-center px-3 hover:underline"
        >
          <Link href={"/terms"} rel="noopener noreferrer" target="_blank">
            Terms and Conditions
          </Link>
        </BaseText>
        <BaseText
          variant={Fonts.SubheadlineRegular}
          className="text-slate-500 border-l-[1px] border-l-slate-500 text-center px-3 hover:underline"
        >
          <Link href={"/privacy"} rel="noopener noreferrer" target="_blank">
            Privacy Policy
          </Link>
        </BaseText>
      </Box>
    </Box>
  );
}

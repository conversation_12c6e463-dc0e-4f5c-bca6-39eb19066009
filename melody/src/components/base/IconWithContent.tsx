import { Box } from "@mui/material";
import React, { ReactNode } from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "./BaseText";

interface IconComponentProps {
  size?: number;
}

// Define a type for components that accept a size prop
type IconComponent = React.ComponentType<IconComponentProps>;

interface IconWithContentProps {
  icon: React.ReactElement<IconComponentProps> | IconComponent;
  iconSize?: number;
  content: string | ReactNode;
  cardView: boolean;
  mainSection: boolean;
}

const IconWithContent: React.FC<IconWithContentProps> = ({
  icon,
  iconSize = 24,
  content = false,
}) => {
  const IconElement = React.isValidElement(icon)
    ? React.cloneElement(icon, { size: iconSize } as IconComponentProps)
    : React.createElement(icon as IconComponent, { size: iconSize });

  return (
    <Box className=" flex flex-row gap-3 w-full align-middle items-center">
      <BaseText
        variant={Fonts.Caption200Regular}
        className="align-middle pb-[2px]"
      >
        {IconElement}
      </BaseText>
      <BaseText
        variant={Fonts.SubheadlineRegular}
        className="align-middle text-slate-300"
      >
        {content}
      </BaseText>
    </Box>
  );
};

export default IconWithContent;

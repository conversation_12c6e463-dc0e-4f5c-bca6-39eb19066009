import { Close } from "@mui/icons-material";
import { Box } from "@mui/material";
import { useEffect } from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "./BaseText";

interface ModalProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  children: JSX.Element | JSX.Element[];
}

const Modal = ({ title, isOpen, onClose, children }: ModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <Box
      className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
      display="flex"
      justifyContent="center"
      alignItems="center"
      onClick={onClose}
    >
      <Box
        className="bg-bgdark border-[#131728] border-2 rounded-lg shadow-lg p-6 relative slide-in-from-bottom "
        onClick={(e) => e.stopPropagation()}
      >
        <Box className="relative flex flex-row items-center mb-4">
          <Box
            sx={{
              padding: 0,
              borderRadius: "50%",
              fontSize: "20px",
              backgroundColor: "transparent", // Background color for the close button
              marginTop: -0.35,
              cursor: "pointer", // Change cursor to pointer for better UX
            }}
            onClick={onClose}
          >
            <Close fontSize="inherit" className="text-slate-400" />
          </Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="text-slate-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            {title}
          </BaseText>
        </Box>
        <Box className="flex flex-col gap-3 text-slate-300">{children}</Box>
      </Box>
    </Box>
  );
};

export default Modal;

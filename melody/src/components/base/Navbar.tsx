// melody/src/components/base/Navbar.tsx
import { buttonStyles } from "@/utils/buttonStyles";
import { Notifications, Textsms } from "@mui/icons-material";
import { AppBar, Avatar, Box, Button, Toolbar } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Fonts } from "../../constants/typology";
import { useAuth } from "../../contexts/AuthContext";
import { useLogoutMutation } from "../../routes/auth";
import { useGetUnreadMessagesCountQuery } from "../../routes/chat";
import { useGetUserInvitesQuery } from "../../routes/projectInvite";
import { useGetUserQuery } from "../../routes/user";
import { BeatleafIcon } from "../icons/Beatleaf";
import { BaseText } from "./BaseText";
import NotificationsMenu from "./NotificationsMenu";

export default function Navbar() {
  const { user, logout: clientLogout } = useAuth();
  const { mutate: logoutUser } = useLogoutMutation();
  const [notificationAnchor, setNotificationAnchor] =
    useState<null | HTMLElement>(null);
  const { data: invites } = useGetUserInvitesQuery();

  const { data: fetchedUser } = useGetUserQuery(user?.name || "");

  const { data: unreadMessagesCount = 0 } = useGetUnreadMessagesCountQuery();

  const handleNotificationClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  const router = useRouter();

  const tempName = user?.email?.split("@")[0];

  const userName = () => {
    if (!user?.name) return tempName || "";
    return user.name;
  };

  return (
    <AppBar position="fixed" className="bg-bgdark">
      <Toolbar className="flex justify-between items-center p-3 bg-bgdark">
        <Box
          className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
          onClick={() => router.push("/")}
        >
          <BeatleafIcon size={40} />
          <BaseText variant={Fonts.Title100} className="text-[#4338ca]">
            beatleaf
          </BaseText>
        </Box>
        <Box className="flex items-center space-x-4">
          <Box className="flex items-center space-x-3">
            <Link href="/" className="hover:opacity-80 transition-opacity">
              <Button
                sx={{
                  ...buttonStyles.subtleButtonStyles,
                  padding: "3px 8px",
                  minWidth: 0,
                }}
                className="transition-opacity"
              >
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-white"
                >
                  Home
                </BaseText>
              </Button>
            </Link>
            <Link href="/users" className="hover:opacity-80 transition-opacity">
              <Button
                sx={{
                  ...buttonStyles.subtleButtonStyles,
                  padding: "3px 8px",
                  minWidth: 0,
                }}
                className="transition-opacity"
              >
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-white"
                >
                  Discover
                </BaseText>
              </Button>
            </Link>
            <Link href="/help" className="hover:opacity-80 transition-opacity">
              <Button
                sx={{
                  ...buttonStyles.subtleButtonStyles,
                  padding: "3px 8px",
                  minWidth: 0,
                }}
                className="transition-opacity"
              >
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-white"
                >
                  Help
                </BaseText>
              </Button>
            </Link>
            <Box className="relative hover:opacity-80 transition-opacity">
              <Box onClick={handleNotificationClick}>
                <Notifications
                  component="svg"
                  className="text-white cursor-pointer bell-wobble"
                />
              </Box>
              {invites && invites.length > 0 && (
                <Box className="absolute -top-2 -right-2 bg-red-500 rounded-full w-4 h-4 flex items-center justify-center">
                  <BaseText
                    variant={Fonts.Caption100Regular}
                    className="text-white text-xs"
                  >
                    {invites.length}
                  </BaseText>
                </Box>
              )}
              <NotificationsMenu
                anchorEl={notificationAnchor}
                open={!!notificationAnchor}
                onClose={handleNotificationClose}
              />
            </Box>
            <Link href="/chat" className="hover:opacity-80 transition-opacity">
              <Box className="relative">
                <Box>
                  <Textsms
                    component="svg"
                    className="text-white cursor-pointer bell-wobble"
                  />
                </Box>
                {unreadMessagesCount > 0 && (
                  <Box className="absolute -top-2 -right-2 bg-red-500 rounded-full w-4 h-4 flex items-center justify-center">
                    <BaseText
                      variant={Fonts.Caption100Regular}
                      className="text-white text-xs"
                    >
                      {unreadMessagesCount}
                    </BaseText>
                  </Box>
                )}
              </Box>
            </Link>
            <Box className="flex items-center space-x-3 group">
              <Box className="flex flex-col text-right">
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-white cursor-pointer group-hover:opacity-40 transition-opacity"
                  onClick={() => router.push("/users/" + userName())}
                >
                  {userName()}
                </BaseText>
                <Button
                  onClick={() => {
                    clientLogout();
                    logoutUser();
                  }}
                  sx={{
                    ...buttonStyles.subtleButtonStyles,
                    padding: 0,
                    minWidth: 0,
                  }}
                  className="transition-opacity"
                >
                  <BaseText
                    variant={Fonts.Caption100Regular}
                    className="text-gray-400 text-sm"
                  >
                    Log out
                  </BaseText>
                </Button>
              </Box>
              <Avatar
                src={fetchedUser?.profileImg}
                className="group-hover:brightness-75"
                sx={{
                  bgcolor: "#4338ca",
                  color: "white",
                  cursor: "pointer",
                  transition: "filter 0.2s ease-in-out",
                  width: 40,
                  height: 40,
                  fontSize: 20,
                }}
                popover="auto"
                onClick={() => router.push("/users/" + userName())}
              >
                {userName().charAt(0).toUpperCase()}
              </Avatar>
            </Box>
          </Box>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

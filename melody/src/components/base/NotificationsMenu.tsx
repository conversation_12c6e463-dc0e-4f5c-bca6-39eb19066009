import { Check, Close } from "@mui/icons-material";
import { Box, CircularProgress, Menu, MenuItem } from "@mui/material";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { Fonts } from "../../constants/typology";
import { useAuth } from "../../contexts/AuthContext";
import {
  useAcceptProjectInviteMutation,
  useDeclineProjectInviteMutation,
  useGetUserInvitesQuery,
} from "../../routes/projectInvite";
import { useGetUserProjectsQuery } from "../../routes/user";
import { BaseText } from "./BaseText";

dayjs.extend(relativeTime);

interface NotificationsMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}

export default function NotificationsMenu({
  anchorEl,
  open,
  onClose,
}: NotificationsMenuProps) {
  // Get user
  const { user } = useAuth();
  const { data: invites, isLoading } = useGetUserInvitesQuery();
  const { refetch: refetchProjects } = useGetUserProjectsQuery(
    user?.name || ""
  );
  const { mutate: acceptInvite, isPending: pendingAcceptInvite } =
    useAcceptProjectInviteMutation();
  const { mutate: declineInvite, isPending: pendingDeclineInvite } =
    useDeclineProjectInviteMutation();

  const handleAccept = (inviteId: number) => {
    acceptInvite(inviteId, {
      onSuccess: () => {
        onClose();
        refetchProjects();
      },
    });
  };

  const handleDecline = (inviteId: number) => {
    declineInvite(inviteId, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          sx: {
            backgroundColor: "#020617",
            color: "white",
            minWidth: "320px",
            maxHeight: "400px",
            mt: 1,
          },
        },
      }}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      disablePortal
      keepMounted
    >
      <Box className="p-2 border-b border-slate-700">
        <BaseText variant={Fonts.BodySemibold} className="text-slate-300">
          Notifications
        </BaseText>
      </Box>

      {isLoading ? (
        <MenuItem disabled>
          <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
            Loading...
          </BaseText>
        </MenuItem>
      ) : !invites || invites.length === 0 ? (
        <MenuItem disabled>
          <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
            No notifications
          </BaseText>
        </MenuItem>
      ) : (
        invites.map((invite) => (
          <MenuItem
            key={invite.id}
            className="border-b border-slate-700/50 hover:bg-slate-800"
          >
            <Box className="flex flex-row w-full py-1 gap-2">
              <Box className="flex justify-between items-start w-full">
                <Box className="flex flex-col">
                  <BaseText
                    variant={Fonts.SubheadlineSemibold}
                    className="text-slate-300"
                  >
                    Project Invite
                  </BaseText>
                  <BaseText
                    variant={Fonts.FootnoteRegular}
                    className="text-slate-400"
                  >
                    {`${invite.project.author.name} invited you to join "${invite.project.name}"`}
                  </BaseText>
                  <BaseText
                    variant={Fonts.Caption100Regular}
                    className="text-slate-500"
                  >
                    {dayjs(invite.createdAt).fromNow()}
                  </BaseText>
                </Box>
              </Box>
              <Box className="flex justify-end gap-2 items-center">
                <Box
                  className="p-1 rounded hover:bg-green-900/50 cursor-pointer h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAccept(invite.id);
                  }}
                >
                  {pendingAcceptInvite ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : (
                    <Check className="text-green-500" fontSize="small" />
                  )}
                </Box>
                <Box
                  className="p-1 rounded hover:bg-red-900/50 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDecline(invite.id);
                  }}
                >
                  {pendingDeclineInvite ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : (
                    <Close className="text-red-500" fontSize="small" />
                  )}
                </Box>
              </Box>
            </Box>
          </MenuItem>
        ))
      )}
    </Menu>
  );
}

"use client";

import { Box } from "@mui/material";
import Link from "next/link";
import React from "react";
import { Fonts } from "../../constants/typology";
import Card, { CardProps } from "../base/Card";
import IconWithContent from "../base/IconWithContent";
import { MetronomeIcon, MusicNotesIcon, PersonIcon } from "../icons";
import { BaseText } from "./BaseText";

interface ProjectProps extends Omit<CardProps, "title"> {
  projectId: number;
  projectTitle: string;
  projectGenre: string;
  projectCollaborators: number;
  projectDuration: string; // Format "MM:SS"
  projectCompletionStatus: number; // Percentage
  projectUnseenChanges: boolean; // For notification icon
  projectLastModified: number; // in days
  projectFileSize: number;
  projectTempo?: number;
  projectKey?: string;
  projectFileCount: number;

  // maybe add these props below? not sure yet

  // keyScale: string;
  // trackCount: number;
  // daw: string;
  // pluginsUsed: number;
}

/**
 *
 * @param {string} [ProjectProps.projectTitle] - title of a user music project
 * @param {number} [ProjectProps.projectCollaborators] - # of collaborators/teammates on the music project
 * @param {string} [ProjectProps.projectDuration] - the duration of the project
 * @param {number} [ProjectProps.projectLastModified] - how long ago the project was last modified
 * @param {number} [ProjectProps.projectFileSize] - the size of the project file(s)
 * @param {number} [ProjectProps.projectTempo] - the tempo of the project in BPM
 * @param {number} [ProjectProps.projectFileCount] - # of files in the project
 * @returns
 */
const ProjectCard: React.FC<ProjectProps> = ({
  projectId,
  projectTitle,
  projectGenre,
  projectCollaborators,
  // projectCompletionStatus,
  // projectUnseenChanges,
  projectLastModified,
  projectKey,
  projectTempo,
  // projectFileCount,
}) => {
  // Keeps track of whether or not to show more Project Card details
  // const [showMore, setShowMore] = useState(false);

  /**
   *
   * @param bytes
   * @returns formatted file size bytes into a string with correct units B, KB, MB, or GB
   */
  // const formatFileSize = (bytes: number): string => {
  //   const KB = 1024;
  //   const MB = 1024 * KB;
  //   const GB = 1024 * MB;

  //   if (bytes < KB) return `${bytes}B`;
  //   if (bytes < MB) return `${(bytes / KB).toFixed(1)}KB`;
  //   if (bytes < GB) return `${(bytes / MB).toFixed(1)}MB`;
  //   return `${(bytes / GB).toFixed(1)}GB`;
  // };

  /**
   *
   * @param days - # of days of when the project was last modified
   * @returns formatted # of days into string with correct units of time
   */
  const formatLastModified = (days: number): string => {
    const week = 7;
    const month = 30;
    const year = 365;
    if (days === 0) return "today";
    if (days === 1) return "yesterday";
    if (days < week) return `${days} days ago`;
    if (days < month)
      return `${Math.floor(days / week)} week${
        Math.floor(days / week) > 1 ? "s" : ""
      } ago`;
    if (days < year)
      return `${Math.floor(days / month)} month${
        Math.floor(days / month) > 1 ? "s" : ""
      } ago`;
    return `${Math.floor(days / year)} year${
      Math.floor(days / year) > 1 ? "s" : ""
    } ago`;
  };

  // Main Project Card content immediately visible to user
  const mainCardContent = (
    <Box className="flex flex-col gap-4 h-auto w-full justify-center items-center">
      <Box className="flex flex-col gap-1 justify-center items-center">
        <Box
          className="flex px-2 py-1 rounded-full text-xs items-center align-middle -mt-2 mb-2"
          sx={{
            backgroundColor: "#1e293b",
            color: "white",
            fontSize: "0.6rem",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <BaseText
            variant={Fonts.Caption100Regular}
            className="text-white translate-y-[0.5px]"
          >
            {projectGenre}
          </BaseText>
        </Box>
        {/* Collaborators w/icon */}
        <IconWithContent
          icon={<PersonIcon />}
          iconSize={16}
          content={projectCollaborators || 1}
          cardView={true}
          mainSection={true}
        />

        {/* Duration w/icon */}
        <IconWithContent
          icon={<MetronomeIcon />}
          iconSize={16}
          content={projectTempo ? `${projectTempo} bpm` : "--"}
          cardView={true}
          mainSection={true}
        />
        {/* File size w/icon */}
        <IconWithContent
          icon={<MusicNotesIcon />}
          iconSize={16}
          content={projectKey || "--"}
          cardView={true}
          mainSection={true}
        />
      </Box>
      <BaseText
        variant={Fonts.Caption200Regular}
        className="text-center text-slate-400"
      >{`Modified ${formatLastModified(projectLastModified)}`}</BaseText>
    </Box>
  );

  // const moreInfoContent = (
  //   <>
  //     <Box className="flex flex-row gap-3 h-auto w-full align-middle items-center">
  //       {/* Tempo w/icon */}
  //       {projectTempo && (
  //         <IconWithContent
  //           icon={<MetronomeIcon />}
  //           iconSize={14}
  //           content={
  //             <BaseText
  //               variant={Fonts.Caption100Regular}
  //               className="text-center"
  //             >
  //               {projectTempo} bpm
  //             </BaseText>
  //           }
  //           cardView={true}
  //           mainSection={false}
  //         />
  //       )}
  //       {/* File count w/icon */}
  //       <IconWithContent
  //         icon={
  //           <FileCopyOutlined fontSize="inherit" className="text-[#4338ca]" />
  //         }
  //         iconSize={14}
  //         content={
  //           <BaseText variant={Fonts.Caption100Regular} className="text-center">
  //             {projectFileCount} files
  //           </BaseText>
  //         }
  //         cardView={true}
  //         mainSection={false}
  //       />
  //     </Box>
  //   </>
  // );

  // Main return function for ProjectCard compononent
  return (
    <Link href={`/projects/${projectId}`}>
      <Card
        title={`${projectTitle}`}
        content={
          <Box className=" flex flex-col justify-center align-middle items-center">
            <Box>{mainCardContent}</Box>
            {/*  Show more icon button */}
            {/* <IconButton
            size="small"
            sx={{
              padding: 0,
              paddingLeft: 1,
              paddingY: 1,
              color: "#999999",
              "&:hover": {
                color: "#222222",
              },
              fontSize: "12px",
              fontFamily: "var(--font-instrument-sans)",
              textAlign: "center",
              justifyContent: "center",
            }}
            onClick={() => setShowMore(!showMore)}
            disableRipple
          >
            More {showMore ? <ArrowDropUp /> : <ArrowDropDownIcon />}
          </IconButton>
          {showMore && <>{moreInfoContent}</>} */}
          </Box>
        }
      ></Card>
    </Link>
  );
};

export default ProjectCard;

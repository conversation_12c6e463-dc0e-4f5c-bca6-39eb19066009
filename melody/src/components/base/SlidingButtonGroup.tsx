import { Box, Button, ButtonGroup } from "@mui/material";
import { styled } from "@mui/system";
import { useState } from "react";

const StyledButtonGroup = styled(ButtonGroup)(({}) => ({
  position: "relative",
  "& .MuiButton-root": {
    zIndex: 1,
    transition: "color 0.3s ease",
    border: "none",
  },
}));

const SliderBox = styled(Box)(({}) => ({
  position: "absolute",
  height: "100%",
  top: 0,
  transition: "left 0.3s ease, width 0.3s ease",
  backgroundColor: "#4338ca",
  zIndex: 0,
}));

interface SlidingButtonGroupProps {
  buttons: string[];
  buttonIcons?: JSX.Element[];
  onStateChange?: (index: number) => void;
  defaultIndex?: number;
}

export default function SlidingButtonGroup({
  buttons,
  buttonIcons = [],
  onStateChange,
  defaultIndex = 0,
}: SlidingButtonGroupProps) {
  const [activeIndex, setActiveIndex] = useState(defaultIndex);

  const handleClick = (index: number) => {
    setActiveIndex(index);
    onStateChange?.(index);
  };

  return (
    <StyledButtonGroup
      sx={{ border: "none" }}
      variant="text"
      className="bg-white rounded-md"
    >
      <SliderBox
        className="rounded-sm"
        sx={{
          left: `${(activeIndex / buttons.length) * 100}%`,
          width: `${(1 / buttons.length) * 100}%`,
          borderWidth: "0px",
          borderColor: "white",
        }}
      />
      {buttons.map((button, index) => (
        <Button
          key={button}
          onClick={() => handleClick(index)}
          sx={{
            color: activeIndex === index ? "white" : "black",
            "&:hover": {
              color: activeIndex === index ? "white" : "#4338ca",
            },
            flex: 1,
            display: "flex",
            gap: 1,
            paddingX: 2,
          }}
          disableRipple
        >
          {buttonIcons.length > 0 ? buttonIcons[index] : ""}
          {button}
        </Button>
      ))}
    </StyledButtonGroup>
  );
}

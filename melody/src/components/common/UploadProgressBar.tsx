import { Box, LinearProgress } from "@mui/material";
import React from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "../base/BaseText";

interface UploadProgressBarProps {
  fileName: string;
  progress: number;
  status: string;
}

export const UploadProgressBar: React.FC<UploadProgressBarProps> = ({
  fileName,
  progress,
  status,
}) => {
  return (
    <Box className="w-full p-3 bg-slate-800 rounded-md mb-2">
      <Box className="flex justify-between mb-1">
        <BaseText variant={Fonts.BodySemibold} className="text-slate-300">
          {fileName}
        </BaseText>
        <BaseText variant={Fonts.FootnoteRegular} className="text-slate-400">
          {progress.toFixed(0)}%
        </BaseText>
      </Box>
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{
          height: 8,
          borderRadius: 4,
          backgroundColor: "rgba(30, 41, 59, 0.5)",
          "& .MuiLinearProgress-bar": {
            backgroundColor: status === "error" ? "#ef4444" : "#4338ca",
          },
        }}
      />
      <BaseText variant={Fonts.FootnoteRegular} className="text-slate-400 mt-1">
        {status === "completed"
          ? "Upload complete"
          : status === "error"
          ? "Upload failed"
          : "Uploading..."}
      </BaseText>
    </Box>
  );
};

import { Box } from "@mui/material";
import dayjs from "dayjs";
import Link from "next/link";
import { Fonts } from "../../constants/typology";
import { Project } from "../../models/project";
import { BaseText } from "../base/BaseText";
import ProjectCard from "../base/ProjectCard";
import { PlaylistIcon } from "../icons";

const CardView = ({ projects }: { projects: Project[] }) => {
  return (
    <Box className="flex flex-wrap flex-row items-start">
      <Link href="/projects/create">
        <Box className="w-[180px] h-[213px] rounded-lg overflow-hidden flex justify-center items-center shadow-lg border-slate-600 border-2 border-dashed m-4 transition-all duration-200 hover:scale-105">
          <Box className="flex flex-col justify-center items-center h-full w-full">
            <BaseText
              variant={Fonts.Title200}
              className="text-center text-slate-600"
            >
              +
            </BaseText>
          </Box>
        </Box>
      </Link>
      {projects.map((project, index) => (
        <ProjectCard
          key={index}
          projectId={Number(project.id)}
          projectTitle={project.name}
          imageComponent={<PlaylistIcon />}
          content={project.description}
          projectCollaborators={project.contributors.length}
          projectGenre={project.genre}
          projectDuration={"3:08"}
          projectCompletionStatus={50}
          projectUnseenChanges={true}
          projectLastModified={dayjs().diff(dayjs(project.updatedAt), "days")}
          projectFileSize={4200000}
          projectTempo={project.tempo}
          projectFileCount={3}
          projectKey={project.key}
        />
      ))}
    </Box>
  );
};

export default CardView;

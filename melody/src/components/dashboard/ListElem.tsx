"use client";

import { Box } from "@mui/material";
import Link from "next/link";
import { Fonts } from "../../constants/typology";
import { BaseText } from "../base/BaseText";

export interface ProjectProps {
  name: string;
  stats: string;
  date: string;
  url: string;
}

export default function ListElem({ project }: { project: ProjectProps }) {
  return (
    <Link href={project.url}>
      <Box className="bg-[#131728] hover:bg-bgdark text-black py-3 px-9 w-[900px] max-w-[85%] transition-all duration-300 border-b-[1px] border-slate-600 hover:cursor-pointer mx-auto">
        <Box className="flex flex-row w-full h-8">
          <Box className="flex basis-1/4 text-left justify-start items-center text-slate-400">
            <BaseText variant={Fonts.Caption200Regular}>
              {project.stats}
            </BaseText>
          </Box>
          <Box className="flex basis-1/2 justify-center items-center text-slate-400">
            <BaseText variant={Fonts.HeadlineSemibold}>{project.name}</BaseText>
          </Box>
          <Box className="flex basis-1/4 justify-end items-center text-slate-400">
            <BaseText variant={Fonts.Caption200Regular}>
              {project.date}
            </BaseText>
          </Box>
        </Box>
      </Box>
    </Link>
  );
}

"use client";

import { Project } from "../../models/project";
import ListElem from "./ListElem";

export const TEST_AMOUNT = 10;

// const generateRandomDate = (): string => {
//   const start = new Date(2024, 0, 1);
//   const end = new Date();
//   const randomDate = new Date(
//     start.getTime() + Math.random() * (end.getTime() - start.getTime())
//   );
//   return randomDate.toISOString();
// };

const formatDate = (date: string): string => {
  if (date === "") return "";
  return new Date(date).toLocaleString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

// const TEST_PROJECTS: ProjectProps[] = Array.from(
//   { length: TEST_AMOUNT },
//   (_, i) => ({
//     name: `Project ${i + 1}`,
//     stats: `${Math.floor(Math.random() * 50) + 1} files | ${
//       Math.floor(Math.random() * 100) + 1
//     } MB`,
//     date: generateRandomDate(),
//     url: "/",
//   })
// ).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

export default function ListView({ projects }: { projects: Project[] }) {
  return (
    <div className="flex flex-col items-center pt-10">
      <ListElem
        project={{
          name: "+",
          stats: "",
          date: "",
          url: "/projects/create",
        }}
      />
      {projects.map((project, index) => (
        <ListElem
          key={index}
          project={{
            name: project.name,
            stats: `${project.genre} | ${project.tempo || "--"} bpm | ${
              project.key || "--"
            }`,
            date: formatDate(project.updatedAt),
            url: `/projects/${project.id}`,
          }}
        />
      ))}
      {/* {TEST_PROJECTS.map((project, index) => (
        <ListElem
          key={index}
          project={{ ...project, date: formatDate(project.date) }}
        />
      ))} */}
    </div>
  );
}

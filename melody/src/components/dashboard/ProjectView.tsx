// melody/src/components/dashboard/ProjectView.tsx

import { FormatListBulleted, ViewModule } from "@mui/icons-material";
import { AppBar, Box, Tab, Tabs, Toolbar } from "@mui/material";
import { useState } from "react";
import { COLORS } from "../../constants/colors";
import { Fonts } from "../../constants/typology";
import { Project } from "../../models/project";
import { useGetUserProjectsQuery } from "../../routes/user";
import { BaseText } from "../base/BaseText";
import SlidingButtonGroup from "../base/SlidingButtonGroup";
import CardView from "./CardView";
import ListView from "./ListView";

export enum ProjectViewType {
  LIST,
  CARD,
}

export enum ProjectFilterType {
  ALL,
  OWNED,
  CONTRIBUTED,
}

export default function ProjectView({
  projectView,
  setProjectView,
  userName,
}: {
  projectView: ProjectViewType;
  setProjectView: (view: ProjectViewType) => void;
  userName: string;
}) {
  const { data: projects } = useGetUserProjectsQuery(userName);
  const [filterType, setFilterType] = useState<ProjectFilterType>(
    ProjectFilterType.ALL
  );

  // Count projects for each category
  const allProjectsCount = projects?.length || 0;
  const ownedProjectsCount =
    projects?.filter((p) => p.author.name === userName).length || 0;
  const contributedProjectsCount =
    projects?.filter((p) => p.author.name !== userName).length || 0;

  // Filter projects based on the selected filter type
  const filteredProjects = projects
    ? projects.filter((project: Project) => {
        if (filterType === ProjectFilterType.ALL) return true;
        if (filterType === ProjectFilterType.OWNED)
          return project.author.name === userName;
        if (filterType === ProjectFilterType.CONTRIBUTED)
          return project.author.name !== userName;
        return true;
      })
    : [];

  return (
    <Box className="flex flex-col items-center m-3">
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl mb-3"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-between">
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            Projects
          </BaseText>
          <SlidingButtonGroup
            buttons={["List", "Card"]}
            buttonIcons={[
              <FormatListBulleted key={0} />,
              <ViewModule key={1} />,
            ]}
            onStateChange={(index) => setProjectView(index as ProjectViewType)}
            defaultIndex={projectView}
          />
        </Toolbar>
      </AppBar>

      {/* Project filter tabs */}
      <Box
        className="w-full bg-bgdark rounded-xl mb-3 px-4"
        sx={{
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
        }}
      >
        <Tabs
          value={filterType}
          onChange={(_, newValue) => setFilterType(newValue)}
          textColor="inherit"
          indicatorColor="primary"
          sx={{
            "& .MuiTab-root": { color: "#94a3b8" },
            "& .Mui-selected": { color: "#e2e8f0" },
          }}
        >
          <Tab
            label={
              <BaseText variant={Fonts.BodySemibold}>
                All Projects{" "}
                <span className="text-[#4338ca]">({allProjectsCount})</span>
              </BaseText>
            }
            sx={{ textTransform: "none" }}
            value={ProjectFilterType.ALL}
          />
          <Tab
            label={
              <BaseText variant={Fonts.BodySemibold}>
                My Projects{" "}
                <span className="text-[#4338ca]">({ownedProjectsCount})</span>
              </BaseText>
            }
            sx={{ textTransform: "none" }}
            value={ProjectFilterType.OWNED}
          />
          <Tab
            label={
              <BaseText variant={Fonts.BodySemibold}>
                Contributed Projects{" "}
                <span className="text-[#4338ca]">
                  ({contributedProjectsCount})
                </span>
              </BaseText>
            }
            sx={{ textTransform: "none" }}
            value={ProjectFilterType.CONTRIBUTED}
          />
        </Tabs>
      </Box>

      <Box
        className={`flex flex-row justify-center bg-[${COLORS.BACKGROUND}] w-full mb-6 pb-6`}
      >
        {projectView === ProjectViewType.LIST && (
          <ListView projects={filteredProjects} />
        )}
        {projectView === ProjectViewType.CARD && (
          <CardView projects={filteredProjects} />
        )}
      </Box>
    </Box>
  );
}

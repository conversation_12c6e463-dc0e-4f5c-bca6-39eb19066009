import React from "react";

interface BeatleafProps {
  size?: number;
  color?: string;
}

export const BeatleafIcon: React.FC<BeatleafProps> = ({
  size = 24,
  color = "#4338ca",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 1024 1024"
    >
      <defs>
        <style>
          {`.cls-1 {
            fill: ${color};
            fill-rule: evenodd;
          }`}
        </style>
      </defs>
      <path
        id="Color_Fill_1"
        data-name="Color Fill 1"
        className="cls-1"
        d="M529,101c138.373,16.961,234.424,104.923,250,203,8.859,55.781-4.567,140.523-66,188-8.866,6.852-20.009,18.74-32,19-1.708-3.3-6.111-15.858-3-23l21-19c13.9-17.292,26.182-39.076,32-64,22.971-98.411-75.652-166.044-143-186l-2,2V539c0,51.544,5.958,112.954-5,157a260.135,260.135,0,0,1-15.9,45.616C555.673,762.208,542.9,782.625,530,798c-50,59.579-122.933,62.294-226,100-18.169,6.647-35.953,13.9-54,22l-1-4c-4.447-45.987-9.7-108.538-10-141a223.724,223.724,0,0,1,1-24,228.332,228.332,0,0,1,12-53c15.286-43.914,35.439-74.6,58.189-96.576C342.484,570.229,385.233,549.209,438,540c26.755-4.669,58.569.815,83,2,13.235-18.673,8-63.926,8-95,0-81.47,1.094-152.537,0-234C528.5,175.638,528.5,138.839,529,101Zm-1,481-66,35C404.457,651.626,306.967,715.8,299,796h3c37.183-62.945,100.032-109.488,159-151,16.411-11.553,69.709-42.455,69-63h-2Z"
      />
    </svg>
  );
};

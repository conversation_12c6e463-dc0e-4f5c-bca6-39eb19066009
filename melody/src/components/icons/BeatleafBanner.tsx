import { Box } from "@mui/material";
import { Fonts } from "../../constants/typology";
import { BaseText } from "../base/BaseText";
import { BeatleafIcon } from "./Beatleaf";

export const BeatleafBanner = ({
  iconSize = 32,
  fontVariant = Fonts.Title100,
  className,
}: {
  iconSize?: number;
  fontVariant?: Fonts;
  className?: string;
}) => {
  return (
    <Box
      className={`w-fit h-fit inline-flex flex-row items-center gap-1 pr-2 ${className}`}
    >
      <BeatleafIcon size={iconSize} />
      <BaseText variant={fontVariant} className="text-[#4338ca]">
        beatleaf
      </BaseText>
    </Box>
  );
};

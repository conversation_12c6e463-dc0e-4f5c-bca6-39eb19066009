import React from "react";

interface DurationIconProps {
  size?: number;
}

export const DurationIcon: React.FC<DurationIconProps> = ({ size }) => {
  return (
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width={size}
      height={size}
      viewBox="0 0 512 512"
      xmlSpace="preserve"
    >
      <rect width="100%" height="100%" fill="none" />
      <path
        fill="#ffffff"
        d="M256,0C114.615,0,0,114.615,0,256s114.615,256,256,256s256-114.615,256-256S397.385,0,256,0z M256,448
        c-106.039,0-192-85.961-192-192S149.961,64,256,64s192,85.961,192,192S362.039,448,256,448z M280,128h-48v160h160v-48H280V128z"
      />
    </svg>
  );
};

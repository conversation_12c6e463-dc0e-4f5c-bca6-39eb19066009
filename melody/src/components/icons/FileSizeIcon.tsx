import React from "react";

interface FileSizeIconProps {
  size?: number;
  color?: string;
  variant?: "outline" | "filled";
}

export const FileSizeIcon: React.FC<FileSizeIconProps> = ({
  size = 24,
  color = "#4338ca",
  variant = "filled",
}) => {
  if (variant === "filled") {
    return (
      <svg
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 512 512"
      >
        <path
          d="M0 0 C1.0674049 -0.00453111 2.13480981 -0.00906223 3.23456031 -0.01373065 C6.80557241 -0.02637467 10.376432 -0.02475424 13.94746399 -0.02310181 C16.51027624 -0.02908709 19.0730867 -0.03589264 21.63589478 -0.04345703 C27.87208127 -0.05929056 34.10821205 -0.0655655 40.34441676 -0.06668179 C45.41661627 -0.06763356 50.48880334 -0.07175029 55.56099892 -0.07808304 C69.95729267 -0.09568993 84.35355666 -0.10492294 98.74986126 -0.10342209 C99.5253436 -0.10334218 100.30082594 -0.10326227 101.09980774 -0.10317993 C101.87624257 -0.10309817 102.65267741 -0.10301641 103.45264057 -0.10293217 C116.02800077 -0.10211975 128.60324106 -0.12124772 141.1785666 -0.14945666 C154.10619654 -0.17822585 167.03376809 -0.1919947 179.96143085 -0.19026911 C187.21310512 -0.18960653 194.46465372 -0.19504055 201.71629906 -0.21662521 C208.53962391 -0.2368055 215.36267988 -0.23612213 222.18601799 -0.22159195 C224.68528888 -0.21950443 227.1845764 -0.22420126 229.68381882 -0.23631287 C233.10617159 -0.25181895 236.52761701 -0.24242076 239.94995117 -0.22705078 C240.92965069 -0.23733791 241.90935021 -0.24762503 242.91873759 -0.25822389 C255.57912548 -0.13331302 266.67568091 4.78863298 276.25337219 12.93539429 C287.30041286 24.25736131 291.94856853 41.97257013 296.92790222 56.5173645 C298.56003905 61.28407238 300.21669287 66.04215844 301.87161255 70.80099487 C302.73331033 73.27950273 303.59380632 75.75842873 304.453125 78.23776245 C308.78884655 90.73844318 313.24984871 103.19290521 317.72993469 115.64242554 C319.29943164 120.00943827 320.86815697 124.37672811 322.43696594 128.74398804 C323.02513344 130.38069206 323.02513344 130.38069206 323.62518311 132.05046082 C326.63155069 140.41897652 329.62678151 148.79144098 332.61665344 157.16586304 C332.95023896 158.1001948 332.95023896 158.1001948 333.29056358 159.05340195 C335.24563165 164.52967278 337.19994855 170.00621151 339.1537323 175.48294067 C340.33773263 178.80181822 341.52205974 182.12057897 342.70649719 185.43930054 C343.27060051 187.02063278 343.27060051 187.02063278 343.84609985 188.63391113 C345.84589279 194.23503075 347.85550125 199.83244625 349.87739563 205.42562866 C350.29645172 206.58556335 350.71550781 207.74549805 351.14726257 208.94058228 C351.95330163 211.16990236 352.76072341 213.39872309 353.56968689 215.62698364 C353.92727081 216.61595642 354.28485474 217.6049292 354.65327454 218.62387085 C354.97065582 219.49892273 355.28803711 220.37397461 355.61503601 221.27554321 C356.29243469 223.26742554 356.29243469 223.26742554 356.29243469 224.26742554 C200.53243469 224.26742554 44.77243469 224.26742554 -115.70756531 224.26742554 C-107.67315075 200.16418187 -107.67315075 200.16418187 -104.57084656 191.59164429 C-104.21371078 190.59808426 -103.85657501 189.60452423 -103.48861694 188.58085632 C-102.72826333 186.46631554 -101.96684368 184.35215779 -101.20439148 182.2383728 C-99.54737611 177.64443347 -97.89584923 173.04852603 -96.24391174 168.45275879 C-95.37513673 166.03589552 -94.50609576 163.61912784 -93.63679504 161.20245361 C-89.38939651 149.39221664 -85.16832397 137.57263952 -80.95088196 125.75167847 C-78.4645931 118.78413786 -75.97543191 111.81762277 -73.48687744 104.85089111 C-71.34845246 98.86376093 -69.21114372 92.8762357 -67.07572937 86.88803101 C-64.91025025 80.81576945 -62.74195328 74.7445205 -60.57182312 68.67391968 C-59.75863092 66.39759028 -58.94642325 64.12090888 -58.13529968 61.84384155 C-40.87547323 13.39666901 -40.87547323 13.39666901 -20.73490906 3.52914429 C-13.61097318 0.56007166 -7.66221923 -0.02395053 0 0 Z "
          fill={color}
          transform="translate(135.7075653076172,47.732574462890625)"
        />
        <path
          d="M0 0 C158.4 0 316.8 0 480 0 C480 132.32355542 480 132.32355542 465.6875 147.75 C453.96014598 158.84910291 441.03963999 160.4510466 425.540802 160.38768005 C423.87430077 160.39185743 422.20780239 160.39733715 420.54130924 160.40399987 C415.98077717 160.41842026 411.42042635 160.41422041 406.8598845 160.40683305 C401.93406887 160.40209524 397.00829211 160.41461666 392.08248901 160.42485046 C382.43972228 160.44203181 372.79702072 160.44287984 363.15424265 160.43763756 C355.31514141 160.43359337 347.4760594 160.43504238 339.63695908 160.44038582 C338.52053271 160.44113424 337.40410634 160.44188266 336.25384887 160.44265376 C333.98570498 160.44418527 331.7175611 160.44572362 329.44941722 160.44726873 C308.18719919 160.46099139 286.92501796 160.45555774 265.66280077 160.44408352 C246.22019511 160.43415156 226.77768462 160.44709161 207.33509363 160.47101771 C187.36061001 160.49541274 167.3861707 160.50501557 147.41167247 160.49836498 C136.20184046 160.49487365 124.99208163 160.49709922 113.78226089 160.51461601 C104.23956118 160.52934302 94.69698619 160.52996685 85.1542889 160.5125451 C80.28796433 160.50403687 75.42186253 160.50188131 70.55554962 160.51719666 C66.09565576 160.53105886 61.6361752 160.52626091 57.17630516 160.50695742 C55.56805785 160.50320181 53.95977732 160.50610667 52.35155965 160.51656907 C36.20985978 160.61406383 24.25585894 156.82641508 12.25 145.6875 C-20.52238303 111.06007642 0 18.12280274 0 0 Z M82.625 55.625 C80.42092107 60.20270239 80.2028361 64.00552411 81 69 C83.32968931 73.83858548 86.16141452 76.67031069 91 79 C95.99447589 79.7971639 99.79729761 79.57907893 104.375 77.375 C107.95907713 74.13226355 110.59535241 71.53524104 111.3828125 66.6015625 C111.63883919 60.91777006 110.93626932 57.35061345 107 53 C102.64938655 49.06373068 99.08222994 48.36116081 93.3984375 48.6171875 C88.46475896 49.40464759 85.86773645 52.04092287 82.625 55.625 Z M146.625 55.625 C144.42092107 60.20270239 144.2028361 64.00552411 145 69 C147.32968931 73.83858548 150.16141452 76.67031069 155 79 C159.99447589 79.7971639 163.79729761 79.57907893 168.375 77.375 C171.95907713 74.13226355 174.59535241 71.53524104 175.3828125 66.6015625 C175.63883919 60.91777006 174.93626932 57.35061345 171 53 C166.64938655 49.06373068 163.08222994 48.36116081 157.3984375 48.6171875 C152.46475896 49.40464759 149.86773645 52.04092287 146.625 55.625 Z "
          fill={color}
          transform="translate(16,304)"
        />
      </svg>
    );
  }
  // Outline version (you can add it if needed)
  return null;
};

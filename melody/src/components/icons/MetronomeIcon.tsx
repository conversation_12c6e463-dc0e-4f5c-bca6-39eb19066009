import React from "react";

interface MetronomeProps {
  size?: number;
}

export const MetronomeIcon: React.FC<MetronomeProps> = ({ size = 24 }) => {
  return (
    <svg
      fill="#94a3b8"
      height={size}
      width={size}
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
    >
      <path
        d="M471.344,106.117c-12.078-12.906-32.332-13.555-45.23-1.461l-26.941,25.26l-8.543-8.541c-12.5-12.5-32.758-12.5-45.258,0
	c-12.496,12.492-12.496,32.758,0,45.25l7.086,7.085l-35.289,33.081L294.215,28.03C292.16,12.017,278.176,0,261.598,0H122.402
	C105.824,0,91.84,12.017,89.785,28.03l-57.527,448C29.805,495.13,45.105,512,64.875,512h254.25c19.77,0,35.07-16.87,32.617-35.97
	l-24.52-190.939l70.512-66.104l11.637,11.638c6.25,6.25,14.438,9.375,22.629,9.375s16.379-3.125,22.629-9.375
	c12.496-12.492,12.496-32.758,0-45.25l-10.184-10.182l25.441-23.85C482.781,139.258,483.434,119.008,471.344,106.117z M104.039,320
	H96l32-256h4.805h118.391H256l22.391,179.144L224,294.138V160c0-17.674-14.328-32-32-32s-32,14.326-32,32v160h-0.871H104.039z"
      />
    </svg>
  );
};

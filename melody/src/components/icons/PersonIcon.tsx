import React from "react";

interface PersonIconProps {
  size?: number;
}

export const PersonIcon: React.FC<PersonIconProps> = ({ size }) => {
  return (
    <svg
      className="svg-icon"
      style={{
        width: `${size}px`,
        height: `${size}px`,
        verticalAlign: "middle",
        overflow: "hidden",
      }}
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      fill="#94a3b8"
    >
      <path d="M512 460c114.875 0 208-93.125 208-208S626.875 44 512 44 304 137.125 304 252s93.125 208 208 208zm0 84c-186.667 0-469.333 93.333-469.333 280v136c0 22.092 17.908 40 40 40h858.666c22.092 0 40-17.908 40-40V824c0-186.667-282.666-280-469.333-280z" />
    </svg>
  );
};

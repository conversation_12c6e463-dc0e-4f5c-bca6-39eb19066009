import {
  Close,
  Pause,
  PlayArrow,
  VolumeDown,
  VolumeUp,
} from "@mui/icons-material";
import { Box, IconButton, Slider } from "@mui/material";
import { useCallback, useEffect, useRef, useState } from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "../base/BaseText";

interface AudioPlayerProps {
  fileUrl: string;
  fileName: string;
  onClose: () => void;
  waveformData: number[]; // <-- Add this prop
}

export const AudioPlayer = ({
  fileUrl,
  fileName,
  onClose,
  waveformData,
}: AudioPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.7);

  const audioRef = useRef<HTMLAudioElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [displayWaveform, setDisplayWaveform] = useState<number[]>([]);

  // Downsample or average waveformData to fit the number of bars
  const getResampledWaveform = useCallback(
    (targetBars: number) => {
      if (!waveformData || waveformData.length === 0) return [];
      if (waveformData.length <= targetBars) return waveformData;

      const bars: number[] = [];
      const groupSize = waveformData.length / targetBars;
      for (let i = 0; i < targetBars; i++) {
        const start = Math.floor(i * groupSize);
        const end = Math.floor((i + 1) * groupSize);
        const avg =
          waveformData.slice(start, end).reduce((sum, v) => sum + v, 0) /
          Math.max(end - start, 1);
        bars.push(avg);
      }
      return bars;
    },
    [waveformData]
  );

  // Update displayWaveform on resize or waveformData change
  useEffect(() => {
    function updateBars() {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const width = canvas.parentElement
        ? canvas.parentElement.clientWidth
        : window.innerWidth;
      const bars = Math.max(Math.floor(width / 2), 16); // 2px per bar, min 16 bars
      setDisplayWaveform(getResampledWaveform(bars));
    }
    updateBars();
    window.addEventListener("resize", updateBars);
    return () => window.removeEventListener("resize", updateBars);
  }, [waveformData, getResampledWaveform]);

  // Helper function to draw rounded rectangles
  const drawRoundedRect = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ) => {
    const r = Math.min(radius, width / 2, height / 2);

    if (r < 1) {
      // If radius is too small, just draw a regular rectangle
      ctx.fillRect(x, y, width, height);
      return;
    }

    ctx.beginPath();
    // Top edge
    ctx.moveTo(x + r, y);
    ctx.lineTo(x + width - r, y);
    // Top-right corner
    ctx.arc(x + width - r, y + r, r, -Math.PI / 2, 0);
    // Right edge
    ctx.lineTo(x + width, y + height - r);
    // Bottom-right corner
    ctx.arc(x + width - r, y + height - r, r, 0, Math.PI / 2);
    // Bottom edge
    ctx.lineTo(x + r, y + height);
    // Bottom-left corner
    ctx.arc(x + r, y + height - r, r, Math.PI / 2, Math.PI);
    // Left edge
    ctx.lineTo(x, y + r);
    // Top-left corner
    ctx.arc(x + r, y + r, r, Math.PI, -Math.PI / 2);

    ctx.closePath();
    ctx.fill();
  };

  // Draw waveform on canvas with rounded rectangles
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || displayWaveform.length === 0) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);

    canvas.style.width = rect.width + "px";
    canvas.style.height = rect.height + "px";

    const { width: displayWidth, height: displayHeight } = rect;
    const barWidth = displayWidth / displayWaveform.length;
    const progressRatio = duration > 0 ? currentTime / duration : 0;
    const borderRadius = Math.min(barWidth / 2, 4);

    ctx.clearRect(0, 0, displayWidth, displayHeight);
    ctx.shadowColor = "#4338ca";

    displayWaveform.forEach((value, index) => {
      const barHeight = Math.max(value * displayHeight * 0.8, 3);
      const x = index * barWidth;
      const y = (displayHeight - barHeight) / 2;
      const actualBarWidth = Math.max(barWidth - 1, 1);

      const isPlayed = index < progressRatio * displayWaveform.length;
      ctx.fillStyle = isPlayed ? "#4338ca" : "#475569";
      ctx.shadowBlur = isPlayed ? 10 : 0;

      drawRoundedRect(ctx, x, y, actualBarWidth, barHeight, borderRadius);
    });
  }, [displayWaveform, currentTime, duration]);

  // Handle waveform click for seeking
  const handleWaveformClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas || !audioRef.current || duration === 0) return;

    const rect = canvas.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickRatio = clickX / rect.width; // Use rect.width instead of canvas.width
    const newTime = clickRatio * duration;

    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  useEffect(() => {
    drawWaveform();
  }, [drawWaveform]);

  useEffect(() => {
    if (audioRef.current) {
      // Reset current time when file changes
      setCurrentTime(0);
      setIsPlaying(true);

      // Try to play the audio
      audioRef.current.play().catch((error) => {
        console.error("Auto-play failed:", error);
        setIsPlaying(false);
      });
    }
  }, [fileUrl]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVolumeChange = (_: Event, newValue: number | number[]) => {
    if (typeof newValue === "number") {
      setVolume(newValue);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  return (
    <Box className="bg-bgdark px-4 py-4 rounded-t-lg flex flex-col gap-2 w-full shadow-xl">
      <Box className="flex justify-between items-center">
        <BaseText variant={Fonts.BodySemibold} className="text-slate-300">
          {fileName}
        </BaseText>
        <IconButton onClick={onClose} size="small" className="text-slate-400">
          <Close className="text-slate-300" />
        </IconButton>
      </Box>

      {/* Controls and Waveform Section */}
      <Box className="flex items-center gap-4">
        <IconButton
          onClick={handlePlayPause}
          className="text-indigo-200 hover:text-indigo-300"
        >
          {isPlaying ? (
            <Pause className="text-indigo-200 hover:text-indigo-300" />
          ) : (
            <PlayArrow className="text-indigo-200 hover:text-indigo-300" />
          )}
        </IconButton>

        {/* Waveform */}
        <Box sx={{ flex: 1, minWidth: 0, p: 2, height: "75px" }}>
          {waveformData.length === 0 ? (
            <Box className="flex items-center justify-center h-full">
              <BaseText
                variant={Fonts.FootnoteRegular}
                className="text-slate-400"
              >
                Loading waveform...
              </BaseText>
            </Box>
          ) : (
            <canvas
              ref={canvasRef}
              onClick={handleWaveformClick}
              style={{
                width: "100%",
                height: "100%",
                cursor: "pointer",
                display: "block",
              }}
            />
          )}
        </Box>

        <Box className="flex items-center gap-2">
          <VolumeDown className="text-slate-400" />
          <Slider
            value={volume}
            min={0}
            max={1}
            step={0.01}
            onChange={handleVolumeChange}
            aria-label="Volume"
            sx={{
              color: "#4338ca",
              width: 80,
              "& .MuiSlider-thumb": {
                width: 12,
                height: 12,
              },
            }}
          />
          <VolumeUp className="text-slate-400" />
        </Box>
      </Box>

      {/* Time display */}
      <Box className="flex justify-between px-12 -mt-3">
        <BaseText
          variant={Fonts.FootnoteRegular}
          className="text-slate-400 pl-3"
        >
          {formatTime(currentTime)}
        </BaseText>
        <BaseText
          variant={Fonts.FootnoteRegular}
          className="text-slate-400 pr-28"
        >
          {formatTime(duration)}
        </BaseText>
      </Box>

      <audio
        ref={audioRef}
        src={fileUrl}
        onTimeUpdate={() => setCurrentTime(audioRef.current?.currentTime || 0)}
        onLoadedMetadata={() => setDuration(audioRef.current?.duration || 0)}
        onEnded={() => setIsPlaying(false)}
      />
    </Box>
  );
};

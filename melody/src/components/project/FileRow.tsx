// src/components/project/FileRow.tsx

import {
  DeleteRounded,
  Download,
  InfoOutlined,
  InsertDriveFile,
  PlayArrowRounded,
} from "@mui/icons-material";
import { Box, Button, CircularProgress } from "@mui/material";
import { useState } from "react";
import { Fonts } from "../../constants/typology";
import { File } from "../../models/file";
import {
  useDeleteFileMutation,
  useGetFileContentsMutation,
} from "../../routes/file";
import { buttonStyles } from "../../utils/buttonStyles";
import { formatFileSize } from "../../utils/fileUtils";
import { BaseText } from "../base/BaseText";
import Modal from "../base/Modal";

interface FileRowProps {
  file: File;
  indent?: number;
  isLoading: boolean;
  versionId: number;
  fileDownloading: File | null;
  setFileDownloading: (file: File | null) => void;
  onPlayFile?: (file: File, url: string, waveform: number[]) => void;
  isReferenced?: boolean;
}

interface DeleteFileModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: File;
  deleteFile: (id: number) => void;
}

const DeleteFileModal = ({
  isOpen,
  onClose,
  file,
  deleteFile,
}: DeleteFileModalProps) => {
  return (
    <Modal title="Delete File?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        {`Are you sure you want to delete "${file.name}"?`}
      </BaseText>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            deleteFile(file.id);
            onClose();
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

export const FileRow = ({
  file,
  isLoading,
  indent = 0,
  fileDownloading,
  setFileDownloading,
  onPlayFile,
  isReferenced = false,
}: FileRowProps) => {
  const [hovered, setHovered] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const { mutate: deleteFile } = useDeleteFileMutation(file?.versionId);
  const { mutate: downloadFile, isPending: pendingDownloadFile } =
    useGetFileContentsMutation();
  const { mutate: getFileForPlaying, isPending: pendingGetFileForPlaying } =
    useGetFileContentsMutation();

  const handlePlayClick = () => {
    if (file && isAudioFile(file.fileType)) {
      getFileForPlaying(
        { id: file.id, fileName: file.name, forPlayback: true },
        {
          onSuccess: (data) => {
            if (data && "signedUrl" in data && data.signedUrl) {
              // Use the signed URL directly
              if (onPlayFile) {
                onPlayFile(file, data.signedUrl, data.waveform || []);
              }
            }
          },
        }
      );
    }
  };

  const isAudioFile = (fileType: string) => {
    return (
      fileType.includes("audio") ||
      fileType.includes("mp3") ||
      fileType.includes("wav") ||
      fileType.includes("flac")
    );
  };

  if (isLoading) {
    return (
      <Box className="bg-bglight p-4 rounded-lg">
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          Loading files...
        </BaseText>
      </Box>
    );
  }

  if (!file) {
    return (
      <Box className="bg-bglight p-4 rounded-lg">
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          No files in this version of the project.
        </BaseText>
      </Box>
    );
  }

  return (
    <>
      <Box
        className={`bg-bglight rounded-lg flex flex-col gap-y-2`}
        style={{ marginLeft: `${indent + 13}px` }}
      >
        <Box
          key={file.id}
          className={`flex items-center justify-between px-4 py-1 transition-all duration-200 ease-in-out hover:bg-[#1a202c] hover:border-l-8 hover:rounded-md hover:border-l-[#4338ca] hover:translate-x-1 ${
            indent > 0 ? "border-l-2 border-[#2d3748]" : "border-l-[#4338ca]"
          } group ${
            isReferenced ? "bg-blue-500 bg-opacity-30 file-highlight" : ""
          }`}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <Box className="flex items-center">
            <InsertDriveFile className="text-indigo-300 mr-1" />
            {isAudioFile(file.fileType) && (
              <>
                {pendingGetFileForPlaying ? (
                  <CircularProgress className="mx-2" size={16} />
                ) : (
                  <PlayArrowRounded
                    className="text-green-400 hover:text-green-600 cursor-pointer mr-1"
                    onClick={handlePlayClick}
                  />
                )}
              </>
            )}
            <BaseText variant={Fonts.BodySemibold} className="text-slate-300">
              {file.name}
            </BaseText>
            {hovered && (
              <>
                {pendingDownloadFile && fileDownloading?.id === file.id ? (
                  <CircularProgress className="ml-2" size={16} />
                ) : (
                  <Download
                    className="text-slate-400 hover:text-blue-400 cursor-pointer ml-1"
                    sx={{
                      fontSize: 20,
                    }}
                    onClick={() => {
                      setFileDownloading(file);
                      downloadFile({ id: file.id, fileName: file.name });
                    }}
                  />
                )}
              </>
            )}
          </Box>
          <Box className="flex items-center gap-x-2 justify-between">
            <Box className="bg-[#24385a] rounded-md p-1">
              <BaseText
                variant={Fonts.BodyRegular}
                className="text-slate-300 ml-auto"
              >
                {formatFileSize(file.fileSize)}
              </BaseText>
            </Box>
            <Box className="bg-[#24304b] rounded-md p-1">
              <BaseText
                variant={Fonts.BodyRegular}
                className="text-slate-300 ml-auto"
              >
                {file.fileType}
              </BaseText>
            </Box>
            {false && (
              <InfoOutlined
                className="text-cyan-500 hover:text-cyan-600 cursor-pointer invisible group-hover:visible"
                sx={{ fontSize: 22 }}
              />
            )}
            <DeleteRounded
              className="text-rose-400 hover:text-red-500 cursor-pointer invisible group-hover:visible"
              fontSize="small"
              onClick={() => setIsDeleteModalOpen(true)}
            />
          </Box>
        </Box>
      </Box>

      <DeleteFileModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        file={file}
        deleteFile={(id) => {
          deleteFile(id, {
            onError: (error) => {
              console.error("Error deleting file:", error);
              alert("Failed to delete file. Please try again.");
            },
          });
        }}
      />
    </>
  );
};

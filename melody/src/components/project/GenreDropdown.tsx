import { useEffect, useState } from "react";
import { Fonts } from "../../constants/typology";
import { MusicGenre } from "../../models/project";
import { BaseText } from "../base/BaseText";

interface GenreDropdownProps {
  value: string;
  onChange: (genre: string) => void;
}

const GenreDropdown = ({ value, onChange }: GenreDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClick = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClick);
    }

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [isOpen]);

  return (
    <div className="relative inline-block w-full">
      {isOpen && (
        <div className="absolute top-8 z-10 w-full mt-1 bg-[#21262d] rounded-md shadow-lg max-h-[216px] overflow-y-auto">
          {Object.values(MusicGenre)
            .sort()
            .map((genre) => (
              <div
                key={genre}
                className={`flex gap-1 px-6 py-2 text-gray-200 hover:bg-[#30363d] cursor-pointer ${
                  genre === value ? "bg-[#30363d]" : ""
                } `}
                onClick={() => {
                  onChange(genre);
                  setIsOpen(false);
                }}
              >
                <BaseText
                  variant={Fonts.SubheadlineSemibold}
                  className={
                    genre === value ? "text-[#948cf0]" : "text-gray-200"
                  }
                >
                  {genre}
                </BaseText>
              </div>
            ))}
        </div>
      )}

      <div
        className={`flex items-center w-full px-2 bg-[#21262d] hover:bg-[#30363d] rounded-lg py-2 cursor-pointer`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-gray-300 text-sm font-semibold ml-4">Genre:</span>
        <span className="text-gray-200 text-sm font-semibold pl-2">
          {value || "Select genre"}
        </span>
        <svg
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform duration-150 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
};

export default GenreDropdown;

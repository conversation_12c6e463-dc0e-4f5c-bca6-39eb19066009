import { Divider } from "@mui/material";
import { useEffect, useState } from "react";
import { Fonts } from "../../constants/typology";
import { KeyMode, KeyNote } from "../../models/project";
import { BaseText } from "../base/BaseText";

interface KeyDropdownProps {
  value: string;
  onChange: (key: string) => void;
}

const KeyDropdown = ({ value, onChange }: KeyDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<string>("");
  const [selectedMode, setSelectedMode] = useState<string>("");

  // Parse initial value into note and mode
  useEffect(() => {
    if (value) {
      const parts = value.split(" ");
      if (parts.length >= 2) {
        setSelectedNote(parts[0]);
        setSelectedMode(parts[1]);
      }
    }
  }, [value]);

  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      // Get a reference to the dropdown container
      const dropdownContainer = document.getElementById(
        "key-dropdown-container"
      );

      // Only close if the click is outside the dropdown container
      if (
        isOpen &&
        dropdownContainer &&
        !dropdownContainer.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClick);
    }

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [isOpen]);

  const handleNoteChange = (note: string) => {
    setSelectedNote(note);
    const newMode = selectedMode || KeyMode.MAJOR;
    onChange(`${note} ${newMode}`);
    setSelectedMode(newMode);
  };

  const handleModeChange = (mode: string) => {
    setSelectedMode(mode);
    if (selectedNote) {
      onChange(`${selectedNote} ${mode}`);
    }
  };

  return (
    <div className="relative inline-block w-full" id="key-dropdown-container">
      {isOpen && (
        <div className="absolute max-h-[240px] overflow-y-auto top-8 z-10 w-full mt-1 bg-[#21262d] rounded-md shadow-lg">
          <div className="flex flex-col p-2">
            <div className="mb-2">
              <BaseText
                variant={Fonts.BodySemibold}
                className="text-gray-400 pl-1 mb-1"
              >
                Note
              </BaseText>
              <div className="grid grid-cols-3 gap-1">
                {Object.values(KeyNote).map((note) => (
                  <div
                    key={note}
                    className={`px-2 py-1 text-gray-200 hover:bg-[#30363d] cursor-pointer rounded ${
                      note === selectedNote ? "bg-[#30363d] text-[#948cf0]" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleNoteChange(note);
                    }}
                  >
                    <BaseText variant={Fonts.SubheadlineSemibold}>
                      {note}
                    </BaseText>
                  </div>
                ))}
              </div>
            </div>
            <Divider className="w-full bg-white opacity-40" />
            <div>
              <BaseText
                variant={Fonts.BodySemibold}
                className="text-gray-400 pl-1 pt-2 mb-1"
              >
                Mode
              </BaseText>
              <div className="grid grid-cols-2 gap-1">
                {Object.values(KeyMode).map((mode) => (
                  <div
                    key={mode}
                    className={`px-2 py-1 text-gray-200 hover:bg-[#30363d] cursor-pointer rounded ${
                      mode === selectedMode ? "bg-[#30363d] text-[#948cf0]" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleModeChange(mode);
                    }}
                  >
                    <BaseText variant={Fonts.SubheadlineSemibold}>
                      {mode}
                    </BaseText>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div
        className={`flex items-center w-full px-2 bg-[#21262d] hover:bg-[#30363d] rounded-lg py-2 cursor-pointer`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-gray-300 text-sm font-semibold ml-4">Key:</span>
        <span className="text-gray-200 text-sm font-semibold pl-2">
          {value || "Select key"}
        </span>
        <svg
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform duration-150 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
};

export default KeyDropdown;

import { BaseText } from "@/components/base/BaseText";
import Modal from "@/components/base/Modal";
import { Fonts } from "@/constants/typology";
import { File } from "@/models/file";
import { Folder } from "@/models/folder";
import { Project } from "@/models/project";
import { Task, TaskStatus, UpdateTaskBody } from "@/models/task";
import { User } from "@/models/user";
import { FileService } from "@/routes/file";
import { FolderService } from "@/routes/folder";
import { useGetVersionsByProjectQuery } from "@/routes/version";
import { buttonStyles } from "@/utils/buttonStyles";
import {
  Folder as FolderIcon,
  History,
  InsertDriveFile,
  KeyboardArrowDown,
  KeyboardArrowRight,
} from "@mui/icons-material";
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  MenuItem,
  Select,
} from "@mui/material";
import { UseMutateFunction } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import DatePicker from "react-datepicker";

const statusColors: Record<TaskStatus, string> = {
  [TaskStatus.NOT_STARTED]: "gray",
  [TaskStatus.IN_PROGRESS]: "#ca8a04",
  [TaskStatus.ON_HOLD]: "#92400e",
  [TaskStatus.IN_REVIEW]: "#a855f7",
  [TaskStatus.DONE]: "green",
  [TaskStatus.CLOSED]: "#991b1b",
};

const taskStatusDisplayNames: Record<TaskStatus, string> = {
  [TaskStatus.NOT_STARTED]: "Not Started",
  [TaskStatus.IN_PROGRESS]: "In Progress",
  [TaskStatus.ON_HOLD]: "On Hold",
  [TaskStatus.IN_REVIEW]: "In Review",
  [TaskStatus.DONE]: "Done",
  [TaskStatus.CLOSED]: "Closed",
};

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task;
  editedTaskName: string;
  setEditedTaskName: (name: string) => void;
  editedDueDate: Date | null;
  setEditedDueDate: (date: Date | null) => void;
  editedAssignee: string;
  setEditedAssignee: (name: string) => void;
  setAssigneeQuery: (query: string) => void;
  updateTask: UseMutateFunction<
    Task,
    Error,
    { id: number } & UpdateTaskBody,
    unknown
  >;
  refetchTasks: () => void;
  setTaskToDelete: (id: number | null) => void;
  setIsDeleteTaskModalOpen: (isOpen: boolean) => void;
  searchedUsers: User[] | undefined;
  searchingUsers: boolean;
  project: Project | undefined;
}

const TaskModal = ({
  isOpen,
  onClose,
  task,
  editedTaskName,
  setEditedTaskName,
  editedDueDate,
  setEditedDueDate,
  editedAssignee,
  setEditedAssignee,
  setAssigneeQuery,
  updateTask,
  refetchTasks,
  setTaskToDelete,
  setIsDeleteTaskModalOpen,
  searchedUsers,
  searchingUsers,
  project,
}: TaskModalProps) => {
  // Add state for tracking status changes
  const [editedStatus, setEditedStatus] = useState<TaskStatus>(
    task.status as TaskStatus
  );

  // Add state for tracking assignee ID changes
  const [editedAssigneeId, setEditedAssigneeId] = useState<number | null>(
    task.assigneeId || null
  );
  // Add state to control the dropdown visibility
  const [showAssigneeDropdown, setShowAssigneeDropdown] = useState(false);
  // Add state for reference tracking
  const [referenceType, setReferenceType] = useState<
    "version" | "folder" | "file" | null
  >(
    task.fileId
      ? "file"
      : task.folderId
      ? "folder"
      : task.versionId
      ? "version"
      : null
  );
  const [selectedReferenceId, setSelectedReferenceId] = useState<number | null>(
    task.fileId || task.folderId || task.versionId || null
  );
  // Add state for tracking the selected version ID
  const [selectedVersionId, setSelectedVersionId] = useState<number | null>(
    task.versionId || null
  );

  // Get all versions for the project
  const { data: versions } = useGetVersionsByProjectQuery(project?.id ?? 0);

  // Create state to store all folders and files by version
  const [allFolders, setAllFolders] = useState<Record<number, Folder[]>>({});
  const [allFiles, setAllFiles] = useState<Record<number, File[]>>({});
  const [loadingData, setLoadingData] = useState(false);

  // Add state to track expanded items
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );

  // Initialize expanded state for versions when they load - all collapsed by default
  useEffect(() => {
    if (versions) {
      const initialExpandedState: Record<string, boolean> = {};

      // First, find which version contains our referenced file (if any)
      let fileVersion: number | undefined;
      let fileFolder: number | undefined;

      if (task.fileId) {
        // Search through all versions and their files
        for (const version of versions) {
          const versionFiles = allFiles[version.id] || [];
          const foundFile = versionFiles.find((f) => f.id === task.fileId);
          if (foundFile) {
            fileVersion = version.id;
            fileFolder = foundFile.folderId || undefined;
            break;
          }
        }
      }

      // Now set expanded state
      versions.forEach((version) => {
        // Expand version if it contains our referenced item or is directly referenced
        const shouldExpandVersion =
          task.versionId === version.id ||
          fileVersion === version.id ||
          (task.folderId &&
            allFolders[version.id]?.some((f) => f.id === task.folderId));

        initialExpandedState[`version-${version.id}`] = !!shouldExpandVersion;
      });

      // If we have a folder reference or a file in a folder, expand all parent folders
      if (task.folderId || fileFolder) {
        const targetFolderId = task.folderId || fileFolder;

        // Find all parent folders and expand them
        const expandParentFolders = (folderId: number | undefined) => {
          if (!folderId) return;

          // Mark this folder as expanded
          initialExpandedState[`folder-${folderId}`] = true;

          // Find its parent and expand it too
          for (const versionId in allFolders) {
            const folders = allFolders[Number(versionId)];
            const folder = folders.find((f) => f.id === folderId);
            if (folder && folder.parentId) {
              expandParentFolders(folder.parentId);
            }
          }
        };

        expandParentFolders(targetFolderId);
      }

      setExpandedItems(initialExpandedState);
    }
  }, [versions, allFolders, allFiles, task]);

  // Function to toggle expansion of an item
  const toggleItemExpansion = (
    type: string,
    id: number,
    e: React.MouseEvent
  ) => {
    e.stopPropagation(); // Prevent selecting the item when clicking the toggle
    setExpandedItems((prev) => ({
      ...prev,
      [`${type}-${id}`]: !prev[`${type}-${id}`],
    }));
  };

  // Fetch folders and files for all versions when versions load
  useEffect(() => {
    if (!versions || versions.length === 0) return;

    const fetchFoldersAndFiles = async () => {
      setLoadingData(true);
      const foldersByVersion: Record<number, Folder[]> = {};
      const filesByVersion: Record<number, File[]> = {};

      // Use Promise.all to fetch data for all versions in parallel
      await Promise.all(
        versions.map(async (version) => {
          try {
            // Fetch folders for this version
            const foldersResponse = await FolderService.getFoldersByVersion(
              version.id
            );
            foldersByVersion[version.id] = foldersResponse;

            // Fetch files for this version
            const filesResponse = await FileService.getFilesByVersion(
              version.id
            );
            filesByVersion[version.id] = filesResponse;
          } catch (error) {
            console.error(
              `Error fetching data for version ${version.id}:`,
              error
            );
          }
        })
      );

      setAllFolders(foldersByVersion);
      setAllFiles(filesByVersion);
      setLoadingData(false);
    };

    fetchFoldersAndFiles();
  }, [versions]);

  const handleUpdateTask = () => {
    updateTask(
      {
        id: task.id,
        title: editedTaskName,
        dueDate: editedDueDate?.toISOString(),
        status: editedStatus,
        assigneeId: editedAssigneeId === 0 ? 0 : editedAssigneeId,
        // Set all reference fields to null if no reference is selected
        versionId: referenceType ? selectedVersionId : null,
        folderId: referenceType === "folder" ? selectedReferenceId : null,
        fileId: referenceType === "file" ? selectedReferenceId : null,
      },
      {
        onSuccess: () => {
          refetchTasks();
          onClose();
        },
        onError: (error) => {
          console.error("Error updating task:", error);
          alert("Failed to update task. Please try again.");
        },
      }
    );
  };

  // Build hierarchical reference options with visibility based on expanded state
  const buildReferenceOptions = () => {
    const options: {
      id: number;
      name: string;
      type: string;
      depth: number;
      visible: boolean;
      hasChildren: boolean;
      isExpanded: boolean;
    }[] = [];

    // Add version options
    if (versions) {
      versions.forEach((version) => {
        // Check if this version has folders or files
        const versionFolders = allFolders[version.id] || [];
        const versionFiles = allFiles[version.id] || [];
        const hasChildren =
          versionFolders.length > 0 || versionFiles.length > 0;
        const isExpanded = !!expandedItems[`version-${version.id}`];

        // Add the version
        options.push({
          id: version.id,
          name: `Version ${version.number}`,
          type: "version",
          depth: 0,
          visible: true, // Versions are always visible
          hasChildren,
          isExpanded,
        });

        // Add folders and files for this version if expanded
        if (isExpanded && hasChildren) {
          // Process root folders first (those with no parent)
          const rootFolders = versionFolders.filter((f) => !f.parentId);

          // Add root folders
          rootFolders.forEach((folder) => {
            addFolderWithChildren(
              folder,
              version.id,
              versionFolders,
              versionFiles,
              options,
              1
            );
          });

          // Add root files (files with no folder)
          const rootFiles = versionFiles.filter((f) => !f.folderId);
          rootFiles.forEach((file) => {
            options.push({
              id: file.id,
              name: file.name,
              type: "file",
              depth: 1, // +1 to indent after version
              visible: true,
              hasChildren: false,
              isExpanded: false,
            });
          });
        }
      });
    }

    return options;
  };

  // Helper function to recursively add a folder and its children
  const addFolderWithChildren = (
    folder: Folder,
    versionId: number,
    allVersionFolders: Folder[],
    allVersionFiles: File[],
    options: {
      id: number;
      name: string;
      type: string;
      depth: number;
      visible: boolean;
      hasChildren: boolean;
      isExpanded: boolean;
    }[],
    depth: number
  ) => {
    const folderFiles = allVersionFiles.filter((f) => f.folderId === folder.id);
    const subFolders = allVersionFolders.filter(
      (f) => f.parentId === folder.id
    );
    const hasChildren = folderFiles.length > 0 || subFolders.length > 0;
    const isExpanded = !!expandedItems[`folder-${folder.id}`];

    // Add the folder
    options.push({
      id: folder.id,
      name: folder.name,
      type: "folder",
      depth: depth,
      visible: true,
      hasChildren,
      isExpanded,
    });

    // If expanded, add subfolders and files
    if (isExpanded && hasChildren) {
      // Add subfolders first
      subFolders.forEach((subFolder) => {
        addFolderWithChildren(
          subFolder,
          versionId,
          allVersionFolders,
          allVersionFiles,
          options,
          depth + 1
        );
      });

      // Then add files
      folderFiles.forEach((file) => {
        options.push({
          id: file.id,
          name: file.name,
          type: "file",
          depth: depth + 1,
          visible: true,
          hasChildren: false,
          isExpanded: false,
        });
      });
    }
  };

  const referenceOptions = buildReferenceOptions();

  return (
    <Modal title="Task Details" isOpen={isOpen} onClose={onClose}>
      <Box className="flex flex-col gap-4 min-w-[500px] p-4">
        {/* Task Name */}
        <Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="mb-2 text-slate-400"
          >
            Task Name
          </BaseText>
          <input
            type="text"
            value={editedTaskName}
            onChange={(e) => setEditedTaskName(e.target.value)}
            className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </Box>

        {/* Task Status */}
        <Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="mb-2 text-slate-400"
          >
            Status
          </BaseText>
          <Box className="flex gap-2">
            {Object.values(TaskStatus).map((status) => (
              <Button
                key={status}
                variant="contained"
                className="text-sm normal-case"
                sx={{
                  backgroundColor:
                    editedStatus === status
                      ? statusColors[status]
                      : "transparent",
                  color:
                    editedStatus === status ? "#fff" : statusColors[status],
                  border: `1px solid ${statusColors[status]}`,
                  "&:hover": {
                    backgroundColor: statusColors[status],
                    color: "#fff",
                    opacity: 0.9,
                  },
                }}
                onClick={() => setEditedStatus(status)}
              >
                {taskStatusDisplayNames[status]}
              </Button>
            ))}
          </Box>
        </Box>

        {/* Due Date */}
        <Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="mb-2 text-slate-400"
          >
            Due Date
          </BaseText>
          <DatePicker
            selected={editedDueDate}
            onChange={(date) => setEditedDueDate(date)}
            className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </Box>

        {/* Assignee */}
        <Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="mb-2 text-slate-400"
          >
            Assigned To
          </BaseText>
          <Box className="relative">
            <input
              type="text"
              placeholder="Search username..."
              value={editedAssignee}
              onChange={(e) => {
                setEditedAssignee(e.target.value);
                setAssigneeQuery(e.target.value);
                setShowAssigneeDropdown(e.target.value.length >= 2);
              }}
              onFocus={() => {
                if (editedAssignee.length >= 2) {
                  setShowAssigneeDropdown(true);
                }
              }}
              className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />

            {/* User Search Dropdown */}
            {showAssigneeDropdown && (
              <Box className="absolute w-full mt-1 bg-[#1f2937] border border-slate-600 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
                {searchingUsers ? (
                  <Box className="p-2 text-slate-400">Searching...</Box>
                ) : searchedUsers && searchedUsers.length > 0 ? (
                  searchedUsers
                    .filter((user) =>
                      project?.contributors.some(
                        (contributor) => contributor.user?.id === user.id
                      )
                    )
                    .map((user) => (
                      <Box
                        key={user.id}
                        className="p-2 hover:bg-slate-700 cursor-pointer text-slate-300"
                        onClick={() => {
                          setEditedAssignee(user.name);
                          setEditedAssigneeId(Number(user.id));
                          setShowAssigneeDropdown(false);
                        }}
                      >
                        <Box className="flex items-center gap-2">
                          <Avatar
                            src={user.profileImg}
                            sx={{
                              bgcolor: "#4338ca",
                              color: "white",
                              width: "24px",
                              height: "24px",
                              fontSize: "12px",
                            }}
                          >
                            {user.name.charAt(0).toUpperCase()}
                          </Avatar>
                          <BaseText
                            variant={Fonts.BodySemibold}
                            className="mr-2"
                          >
                            {user.name}
                          </BaseText>
                        </Box>
                      </Box>
                    ))
                ) : (
                  <Box className="p-2 text-slate-400">
                    No contributors found
                  </Box>
                )}

                {/* Option to set to "Everyone" */}
                <Box
                  className="p-2 hover:bg-slate-700 cursor-pointer text-slate-300 border-t border-slate-600"
                  onClick={() => {
                    setEditedAssignee("Everyone");
                    setEditedAssigneeId(0);
                    setShowAssigneeDropdown(false);
                  }}
                >
                  <Box className="flex items-center gap-2">
                    <BaseText variant={Fonts.BodySemibold} className="mr-2">
                      Everyone
                    </BaseText>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Box>

        {/* Reference Selection */}
        <Box>
          <BaseText
            variant={Fonts.BodySemibold}
            className="mb-2 text-slate-400"
          >
            Reference
          </BaseText>
          {loadingData ? (
            <Box className="flex items-center justify-center p-2">
              <CircularProgress size={24} />
              <BaseText
                variant={Fonts.BodyRegular}
                className="ml-2 text-slate-400"
              >
                Loading references...
              </BaseText>
            </Box>
          ) : (
            <Select
              value={
                selectedReferenceId && referenceType
                  ? `${referenceType}-${selectedReferenceId}`
                  : ""
              }
              onChange={(e) => {
                const selectedValue = e.target.value as string;
                if (!selectedValue) {
                  setSelectedReferenceId(null);
                  setReferenceType(null);
                  return;
                }

                const [type, idStr] = selectedValue.split("-");
                const id = Number(idStr);

                setSelectedReferenceId(id);
                setReferenceType(type as "version" | "folder" | "file");

                // If selecting a file or folder, we need to find its version ID
                if (type === "file") {
                  // Find which version contains this file
                  for (const versionId in allFiles) {
                    const files = allFiles[Number(versionId)];
                    if (files.some((file) => file.id === id)) {
                      // We found the version that contains this file
                      setSelectedVersionId(Number(versionId));
                      break;
                    }
                  }
                } else if (type === "folder") {
                  // Find which version contains this folder
                  for (const versionId in allFolders) {
                    const folders = allFolders[Number(versionId)];
                    if (folders.some((folder) => folder.id === id)) {
                      // We found the version that contains this folder
                      setSelectedVersionId(Number(versionId));
                      break;
                    }
                  }
                } else if (type === "version") {
                  // If selecting a version directly, set the version ID
                  setSelectedVersionId(id);
                }
              }}
              displayEmpty
              className="w-full bg-[#1f2937] text-slate-300 border border-slate-600 h-12"
              renderValue={(selected) => {
                if (!selected)
                  return (
                    <BaseText
                      variant={Fonts.SubheadlineSemibold}
                      className="text-slate-300 pt-0.5"
                    >
                      No reference
                    </BaseText>
                  );

                const [type, idStr] = (selected as string).split("-");
                const id = Number(idStr);
                const option = referenceOptions.find(
                  (opt) => opt.type === type && opt.id === id
                );
                return option ? (
                  <>
                    {option.type === "version" && (
                      <History
                        className="text-teal-400 mr-2"
                        sx={{ fontSize: 20 }}
                      />
                    )}
                    {option.type === "folder" && (
                      <FolderIcon
                        className="text-yellow-400 mr-2"
                        sx={{ fontSize: 20 }}
                      />
                    )}
                    {option.type === "file" && (
                      <InsertDriveFile
                        className="text-indigo-300 mr-2"
                        sx={{ fontSize: 20 }}
                      />
                    )}
                    <BaseText
                      inline
                      variant={Fonts.SubheadlineSemibold}
                      className="text-slate-300 pt-0.5"
                    >
                      {option.name}
                    </BaseText>
                  </>
                ) : (
                  <BaseText
                    variant={Fonts.SubheadlineSemibold}
                    className="text-slate-300 pt-0.5"
                  >
                    No reference
                  </BaseText>
                );
              }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: "50%",
                    overflow: "auto",
                    backgroundColor: "#1f2937",
                    border: "1px solid #374151",
                    borderRadius: "8px",
                    padding: "8px",
                  },
                },
              }}
            >
              <MenuItem value="">
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-400"
                >
                  <em>No reference</em>
                </BaseText>
              </MenuItem>
              {referenceOptions.map((option) => (
                <MenuItem
                  key={`${option.type}-${option.id}`}
                  value={`${option.type}-${option.id}`}
                  sx={{
                    pl: option.depth * 6,
                    display: option.visible ? "flex" : "none",
                    alignItems: "center",
                    height: "35px",
                    "&:hover": {
                      borderRadius: "8px",
                      backgroundColor: "#263040",
                    },
                    "&.Mui-selected": {
                      borderRadius: "8px",
                      backgroundColor: "#374151",
                    },
                  }}
                >
                  {option.hasChildren && (
                    <Box
                      component="span"
                      onClick={(e) =>
                        toggleItemExpansion(option.type, option.id, e)
                      }
                      sx={{
                        display: "inline-flex",
                        marginRight: 1,
                        cursor: "pointer",
                      }}
                    >
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-400"
                      >
                        {option.isExpanded ? (
                          <KeyboardArrowDown fontSize="small" />
                        ) : (
                          <KeyboardArrowRight fontSize="small" />
                        )}
                      </BaseText>
                    </Box>
                  )}
                  {!option.hasChildren && (
                    <Box
                      component="span"
                      sx={{ width: 24, marginRight: 0.5 }}
                    />
                  )}
                  {option.type === "version" && (
                    <History
                      className="text-teal-400 mr-2"
                      sx={{ fontSize: 20 }}
                    />
                  )}
                  {option.type === "folder" && (
                    <FolderIcon
                      className="text-yellow-400 mr-2"
                      sx={{ fontSize: 20 }}
                    />
                  )}
                  {option.type === "file" && (
                    <InsertDriveFile
                      className="text-indigo-300 mr-2"
                      sx={{ fontSize: 20 }}
                    />
                  )}
                  <BaseText
                    variant={Fonts.SubheadlineSemibold}
                    className="text-slate-300 pt-0.5"
                  >
                    {option.name}
                  </BaseText>
                </MenuItem>
              ))}
            </Select>
          )}
        </Box>

        {/* Action Buttons */}
        <Box className="flex justify-between mt-4">
          <Button
            variant="outlined"
            color="secondary"
            className="text-md normal-case"
            sx={buttonStyles.outlinedDangerButtonStyles}
            onClick={() => {
              setTaskToDelete(task.id);
              setIsDeleteTaskModalOpen(true);
              onClose();
            }}
          >
            Delete
          </Button>
          <Box className="flex gap-2">
            <Button
              variant="outlined"
              className="text-md normal-case"
              sx={buttonStyles.subtleButtonStyles}
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              className="text-md normal-case"
              sx={buttonStyles.primaryButtonStyles}
              onClick={handleUpdateTask}
            >
              Save Changes
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default TaskModal;

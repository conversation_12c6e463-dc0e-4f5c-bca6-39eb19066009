// melody/src/components/project/UploadFolderModal.tsx

import { useUploadProgress } from "@/hooks/useUploadProgress";
import { useGetFolderQuery, useUploadFolderMutation } from "@/routes/folder";
import { FINAL_ALLOWED_EXTENSIONS, getFileType } from "@/utils/fileTypes";
import {
  Archive,
  Folder as FolderIcon,
  InsertDriveFile,
} from "@mui/icons-material";
import {
  Box,
  Button,
  LinearProgress,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import JSZip from "jszip";
import { useEffect, useState } from "react";
import { Fonts } from "../../constants/typology";
import { Version } from "../../models/version";
import { buttonStyles } from "../../utils/buttonStyles";
import { BaseText } from "../base/BaseText";
import Modal from "../base/Modal";

interface UploadFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  version: Version | null;
  targetFolderId: number | null;
  setTargetFolderId: (id: number | null) => void;
}

export const UploadFolderModal = ({
  isOpen,
  onClose,
  version,
  targetFolderId,
  setTargetFolderId,
}: UploadFolderModalProps) => {
  const [folderName, setFolderName] = useState<string>("");
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadMode, setUploadMode] = useState<"folder" | "zip">("folder");
  const [isExtractingZip, setIsExtractingZip] = useState<boolean>(false);
  const { uploads } = useUploadProgress();
  const [folderProgress, setFolderProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "completed" | "error"
  >("idle");

  // Helper function to check if a file is a ZIP archive
  const isZipFile = (file: File): boolean => {
    const zipExtensions = [".zip", ".rar", ".7z"];
    const zipMimeTypes = [
      "application/zip",
      "application/x-zip-compressed",
      "application/x-rar-compressed",
      "application/x-7z-compressed",
    ];

    return (
      zipExtensions.some((ext) => file.name.toLowerCase().endsWith(ext)) ||
      zipMimeTypes.includes(file.type)
    );
  };

  // Helper function to create a File object from ZIP entry
  const createFileFromZipEntry = (
    fileName: string,
    content: Uint8Array,
    relativePath: string
  ): File => {
    const file = new File([content], fileName, {
      type: getFileType(fileName),
    });

    // Add webkitRelativePath property to mimic folder structure
    Object.defineProperty(file, "webkitRelativePath", {
      value: relativePath,
      writable: false,
    });

    return file;
  };

  // Calculate combined progress for the folder
  useEffect(() => {
    if (isUploading && selectedFiles && selectedFiles.length > 0) {
      const relevantUploads = Object.values(uploads).filter(
        (upload) =>
          // Only include uploads that started after we began the folder upload
          upload.status !== "idle"
      );

      if (relevantUploads.length === 0) return;

      // Calculate average progress across all files
      const totalProgress = relevantUploads.reduce(
        (sum, upload) => sum + upload.progress,
        0
      );
      const avgProgress = totalProgress / relevantUploads.length;

      setFolderProgress(avgProgress);

      // Check if all uploads are complete or if any have errors
      const hasError = relevantUploads.some(
        (upload) => upload.status === "error"
      );
      const allComplete = relevantUploads.every(
        (upload) => upload.status === "completed"
      );

      if (hasError) {
        setUploadStatus("error");
      } else if (
        allComplete &&
        relevantUploads.length >= selectedFiles.length
      ) {
        setUploadStatus("completed");
      } else {
        setUploadStatus("uploading");
      }
    }
  }, [uploads, isUploading, selectedFiles]);

  const { mutate: uploadFolder } = useUploadFolderMutation();

  const { data: targetFolder } = useGetFolderQuery(targetFolderId || 0);

  // Enhanced handleFolderSelect function with ZIP support
  const handleFolderSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const filesArray = Array.from(files);
    const processedFiles: File[] = [];
    let extractedFolderName = "";

    // Check if we have ZIP files to extract
    const zipFiles = filesArray.filter(isZipFile);
    const regularFiles = filesArray.filter((file) => !isZipFile(file));

    // Process ZIP files
    if (zipFiles.length > 0) {
      setIsExtractingZip(true);

      for (const zipFile of zipFiles) {
        try {
          const zip = new JSZip();
          const zipContent = await zip.loadAsync(zipFile);

          // Set folder name from ZIP file name (remove extension)
          if (!extractedFolderName) {
            extractedFolderName = zipFile.name.replace(/\.(zip|rar|7z)$/i, "");
          }

          // Extract files from ZIP
          const zipPromises: Promise<File>[] = [];

          zipContent.forEach((relativePath, zipEntry) => {
            // Skip directories
            if (zipEntry.dir) return;

            const promise = zipEntry.async("uint8array").then((content) => {
              const fileName = relativePath.split("/").pop() || relativePath;
              return createFileFromZipEntry(fileName, content, relativePath);
            });

            zipPromises.push(promise);
          });

          const extractedFiles = await Promise.all(zipPromises);
          processedFiles.push(...extractedFiles);
        } catch (error) {
          console.error(`Error extracting ZIP file ${zipFile.name}:`, error);
          alert(`Failed to extract ZIP file: ${zipFile.name}`);
        }
      }

      setIsExtractingZip(false);
    }

    // Add regular files (from folder selection)
    processedFiles.push(...regularFiles);

    // Filter allowed files
    const allowedFiles = processedFiles.filter((file) =>
      FINAL_ALLOWED_EXTENSIONS.some((ext) =>
        file.name.toLowerCase().endsWith(ext)
      )
    );

    // Set the files
    setSelectedFiles(allowedFiles as unknown as FileList);

    // Determine folder name
    if (extractedFolderName) {
      // ZIP file case
      setFolderName(extractedFolderName);
    } else if (regularFiles.length > 0 && regularFiles[0].webkitRelativePath) {
      // Regular folder selection case
      const folderPath = regularFiles[0].webkitRelativePath.split("/");
      if (folderPath.length > 0) {
        setFolderName(folderPath[0]);
      }
    } else if (allowedFiles.length > 0) {
      // Fallback: use "Selected Files" or similar
      setFolderName("Selected Files");
    }
  };

  const handleUploadFolder = () => {
    if (!selectedFiles || !version || !folderName) {
      return;
    }

    setIsUploading(true);
    setUploadStatus("uploading");
    setFolderProgress(0);

    const formData = new FormData();
    formData.append("name", folderName);
    formData.append("versionId", version.id.toString());

    // Add all files from the folder with their relative paths
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      // Preserve the relative path information
      formData.append("files", file);
      // Add the relative path information separately
      formData.append("paths[]", file.webkitRelativePath);
    }

    if (targetFolderId) {
      formData.append("parentId", targetFolderId.toString());
    }

    uploadFolder(formData, {
      onSuccess: () => {
        setIsUploading(false);
        setSelectedFiles(null);
        setTargetFolderId(null);
        setFolderName("");
        onClose();
      },
      onError: (error) => {
        console.error("Error uploading folder:", error);
        setIsUploading(false);
        setTargetFolderId(null);
        alert("Failed to upload folder. Please try again.");
      },
    });
  };

  let isFileInputClicked = false;

  const handleChooseFolderClick = () => {
    if (isFileInputClicked) return; // Prevent multiple clicks
    isFileInputClicked = true;
  };

  const handleModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newMode: "folder" | "zip"
  ) => {
    if (newMode !== null) {
      setUploadMode(newMode);
      // Reset selection when changing modes
      setSelectedFiles(null);
      setFolderName("");
    }
  };

  return (
    <Modal title="Upload Folder" isOpen={isOpen} onClose={onClose}>
      <BaseText
        variant={Fonts.BodyRegular}
        className="mb-4 mt-2 text-slate-300"
      >
        Select a folder or ZIP archive to upload to{" "}
        {targetFolder ? `"${targetFolder.name}"` : "this version"}. All files
        and subfolder structure will be preserved.
      </BaseText>

      <Box className="flex flex-col gap-4">
        {/* Mode Toggle */}
        <ToggleButtonGroup
          value={uploadMode}
          exclusive
          onChange={handleModeChange}
          aria-label="upload mode"
          sx={{
            "& .MuiToggleButton-root": {
              color: "rgb(148 163 184)",
              borderColor: "rgb(71 85 105)",
              "&.Mui-selected": {
                backgroundColor: "rgb(67 56 202)",
                color: "white",
                borderColor: "rgb(67 56 202)",
                "&:hover": {
                  backgroundColor: "rgb(67 56 202)",
                },
              },
              "&:hover": {
                backgroundColor: "rgba(67, 56, 202, 0.1)",
              },
              textTransform: "none",
            },
          }}
        >
          <ToggleButton value="folder" aria-label="folder upload">
            <FolderIcon sx={{ mr: 1 }} />
            Folder
          </ToggleButton>
          <ToggleButton value="zip" aria-label="zip upload">
            <Archive sx={{ mr: 1 }} />
            ZIP Archive
          </ToggleButton>
        </ToggleButtonGroup>

        <Button
          variant="contained"
          component="label"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={handleChooseFolderClick}
          disabled={isExtractingZip}
        >
          {isExtractingZip
            ? "Extracting ZIP..."
            : uploadMode === "folder"
            ? "Choose Folder"
            : "Choose ZIP File"}

          {/* Folder Input */}
          {uploadMode === "folder" && (
            <input
              type="file"
              hidden
              {...({
                webkitdirectory: "",
                directory: "",
                multiple: true,
              } as React.InputHTMLAttributes<HTMLInputElement>)}
              onChange={handleFolderSelect}
            />
          )}

          {/* ZIP Input */}
          {uploadMode === "zip" && (
            <input
              type="file"
              hidden
              accept=".zip,.rar,.7z,application/zip,application/x-zip-compressed,application/x-rar-compressed,application/x-7z-compressed"
              multiple
              onChange={handleFolderSelect}
            />
          )}
        </Button>

        <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
          {selectedFiles ? (
            <div className="flex flex-col">
              <span className="font-bold text-slate-200 ">Selected:</span>
              <span className="inline-flex items-center mt-2 ml-2">
                {uploadMode === "zip" ? (
                  <Archive
                    className="text-purple-400 mr-2 mb-[1px]"
                    sx={{ fontSize: 27 }}
                  />
                ) : (
                  <FolderIcon
                    className="text-yellow-400 mr-2 mb-[1px]"
                    sx={{ fontSize: 27 }}
                  />
                )}
                <span className="font-semibold text-slate-200">
                  {folderName}
                </span>
              </span>
              <span className="inline-flex items-center mt-2 ml-4">
                <InsertDriveFile className="text-blue-300 ml-1 mr-2" />
                <div className="text-slate-200 font-medium space-x-1">
                  {" "}
                  {selectedFiles.length} files
                </div>
              </span>
            </div>
          ) : (
            `No ${uploadMode === "folder" ? "folder" : "ZIP file"} chosen`
          )}
        </BaseText>

        <Box className="flex flex-row gap-4 justify-end mt-4">
          <Button
            variant="outlined"
            className="text-md normal-case"
            sx={buttonStyles.outlinedDangerButtonStyles}
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            className="text-md normal-case"
            disabled={isUploading || !selectedFiles || isExtractingZip}
            sx={buttonStyles.primaryButtonStyles}
            onClick={handleUploadFolder}
          >
            {isUploading
              ? "Uploading..."
              : isExtractingZip
              ? "Processing..."
              : "Upload Folder"}
          </Button>
        </Box>

        {/* Folder upload progress */}
        {isUploading && (
          <Box className="w-full p-3 bg-slate-800 rounded-md mt-4">
            <Box className="flex justify-between mb-1">
              <BaseText variant={Fonts.BodySemibold} className="text-slate-300">
                {folderName}
              </BaseText>
              <BaseText
                variant={Fonts.FootnoteRegular}
                className="text-slate-400"
              >
                {folderProgress.toFixed(0)}%
              </BaseText>
            </Box>
            <LinearProgress
              variant="determinate"
              value={folderProgress}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: "rgba(30, 41, 59, 0.5)",
                "& .MuiLinearProgress-bar": {
                  backgroundColor:
                    uploadStatus === "error" ? "#ef4444" : "#4338ca",
                },
              }}
            />
            <BaseText
              variant={Fonts.FootnoteRegular}
              className="text-slate-400 mt-1"
            >
              {uploadStatus === "completed"
                ? "Upload complete"
                : uploadStatus === "error"
                ? "Upload failed"
                : `Uploading ${selectedFiles?.length} files...`}
            </BaseText>
          </Box>
        )}

        {/* ZIP extraction progress indicator */}
        {isExtractingZip && (
          <Box className="w-full p-3 bg-slate-800 rounded-md">
            <BaseText
              variant={Fonts.BodySemibold}
              className="text-slate-300 mb-2"
            >
              Extracting ZIP archive...
            </BaseText>
            <LinearProgress
              sx={{
                height: 4,
                borderRadius: 2,
                backgroundColor: "rgba(30, 41, 59, 0.5)",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#8b5cf6",
                },
              }}
            />
          </Box>
        )}
      </Box>
    </Modal>
  );
};

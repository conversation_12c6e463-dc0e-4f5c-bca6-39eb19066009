import { useEffect, useState } from "react";
import { Fonts } from "../../constants/typology";
import { BaseText } from "../base/BaseText";

export const SOCIAL_PLATFORMS = [
  { value: "twitter", label: "Twitter/X" },
  { value: "instagram", label: "Instagram" },
  { value: "facebook", label: "Facebook" },
  { value: "tiktok", label: "TikTok" },
  { value: "youtube", label: "YouTube" },
  { value: "linkedin", label: "LinkedIn" },
  { value: "github", label: "GitHub" },
  { value: "soundcloud", label: "SoundCloud" },
  { value: "spotify", label: "Spotify" },
  { value: "bandcamp", label: "Bandcamp" },
];

interface SocialDropdownProps {
  value: string;
  onChange: (platform: string) => void;
}

const SocialDropdown = ({ value, onChange }: SocialDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClick = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClick);
    }

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [isOpen]);

  // Find the label for the current value
  const selectedLabel =
    SOCIAL_PLATFORMS.find((p) => p.value === value)?.label || value;

  return (
    <div className="relative inline-block w-full">
      {isOpen && (
        <div className="absolute top-8 z-10 w-full mt-1 bg-bgdark rounded-md shadow-lg max-h-[216px] overflow-y-auto">
          {SOCIAL_PLATFORMS.map((platform) => (
            <div
              key={platform.value}
              className={`flex gap-1 px-6 py-2 text-gray-200 hover:bg-[#30363d] cursor-pointer ${
                platform.value === value ? "bg-[#30363d]" : ""
              } `}
              onClick={() => {
                onChange(platform.value);
                setIsOpen(false);
              }}
            >
              <BaseText
                variant={Fonts.SubheadlineSemibold}
                className={
                  platform.value === value ? "text-[#948cf0]" : "text-gray-200"
                }
              >
                {platform.label}
              </BaseText>
            </div>
          ))}
        </div>
      )}

      <div
        className={`flex items-center w-full px-2 bg-bgdark hover:bg-[#30363d] rounded-lg py-2 cursor-pointer`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-gray-300 text-sm font-semibold ml-4">
          Platform:
        </span>
        <span className="text-gray-200 text-sm font-semibold pl-2">
          {selectedLabel || "Select platform"}
        </span>
        <svg
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform duration-150 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
};

export default SocialDropdown;

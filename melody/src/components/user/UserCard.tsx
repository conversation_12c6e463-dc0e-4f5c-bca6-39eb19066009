import { Avatar, Box, Tooltip } from "@mui/material";
import Link from "next/link";
import React from "react";
import { Fonts } from "../../constants/typology";
import { User } from "../../models/user";
import { BaseText } from "../base/BaseText";

interface UserCardProps {
  user: User;
  isCurrentUser: boolean;
}

export const UserCard: React.FC<UserCardProps> = ({ user, isCurrentUser }) => {
  return (
    <Link href={`/users/${user.name}`} style={{ textDecoration: "none" }}>
      <Box
        className="relative flex flex-col items-center p-5 m-2 rounded-xl transition-all duration-300"
        sx={{
          width: "200px",
          height: "280px", // Fixed height for consistency
          background: "linear-gradient(145deg, #151b2d, #0f1420)",
          boxShadow: "0 8px 16px rgba(0, 0, 0, 0.3)",
          border: "1px solid rgba(255, 255, 255, 0.05)",
          "&:hover": {
            transform: "translateY(-5px)",
            boxShadow: "0 12px 20px rgba(0, 0, 0, 0.4)",
          },
        }}
      >
        {/* Current user indicator */}
        {isCurrentUser && (
          <Box
            className="absolute top-3 right-3 px-2 py-1 rounded-full"
            sx={{
              backgroundColor: "#4338ca",
              fontSize: "0.6rem",
              color: "white",
              fontWeight: "bold",
            }}
          >
            YOU
          </Box>
        )}

        {/* Avatar with glow effect for current user */}
        <Avatar
          src={user.profileImg}
          sx={{
            width: 80,
            height: 80,
            fontSize: 40,
            marginBottom: "16px",
            bgcolor: "#4338ca",
            color: "white",
            border: isCurrentUser ? "3px solid #4338ca" : "3px solid #1e293b",
            boxShadow: isCurrentUser
              ? "0 0 15px rgba(67, 56, 202, 0.5)"
              : "0 4px 8px rgba(0, 0, 0, 0.2)",
            objectFit: "cover",
          }}
        >
          {user.name.charAt(0).toUpperCase()}
        </Avatar>

        {/* Username */}
        <BaseText
          variant={Fonts.HeadlineSemibold}
          className="text-center text-slate-200 mb-2 w-[95%] truncate"
        >
          {user.name}
        </BaseText>

        {/* Top Genres Pills - Fixed height container */}
        <Box className="flex flex-wrap justify-center gap-1 mt-2 mb-4 min-h-[32px]">
          {user.topGenres && user.topGenres.length > 0
            ? user.topGenres.map((genreData, index) => (
                <Box
                  key={index}
                  className="flex px-2 py-1 rounded-full text-xs items-center align-middle"
                  sx={{
                    backgroundColor: "#1e293b",
                    color: "white",
                    fontSize: "0.6rem",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                  }}
                >
                  <BaseText
                    variant={Fonts.Caption100Regular}
                    className="text-white translate-y-[0.5px]"
                  >
                    {genreData.genre}
                  </BaseText>
                </Box>
              ))
            : null}
        </Box>

        {/* Bio with tooltip for overflow */}
        <Tooltip title={user.bio || "No bio provided"} arrow placement="top">
          <Box sx={{ width: "100%", height: "40px" }}>
            {user.bio ? (
              <BaseText
                variant={Fonts.Caption100Regular}
                className="text-center text-slate-400 line-clamp-2"
              >
                {user.bio}
              </BaseText>
            ) : (
              <BaseText
                variant={Fonts.Caption100Regular}
                className="text-center text-slate-400 italic"
              >
                No bio provided
              </BaseText>
            )}
          </Box>
        </Tooltip>

        {/* Bottom accent line */}
        <Box
          className="absolute bottom-0 left-0 right-0 h-1 rounded-b-xl"
          sx={{
            background: isCurrentUser
              ? "linear-gradient(90deg, #4338ca, #6366f1)"
              : "linear-gradient(90deg, #1e293b, #334155)",
          }}
        />
      </Box>
    </Link>
  );
};

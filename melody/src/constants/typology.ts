/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */

export type FontParams = {
  size: string;
  leading: string;
  weight: string;
};

const FONT_SIZES = {
  S72: "72px",
  S48: "48px",
  S40: "40px",
  S32: "32px",
  S22: "22px",
  S16: "16px",
  S14: "14px",
  S13: "13px",
  S12: "12px",
  S11: "11px",
  S10: "10px",
};

const FONT_LEADING = {
  L75: "75px",
  L60: "60px",
  L50: "50px",
  L40: "40px",
  L35: "35px",
  L24: "24px",
  L20: "20px",
  L18: "18px",
  L16: "16px",
  L15: "15px",
  L14: "14px",
};

const FONT_WEIGHTS = {
  W700: "700",
  W600: "600",
  W400: "400",
};

export enum Fonts {
  Display200,
  Display100,
  Title200,
  Title100,
  HeadlineSemibold,
  HeadlineRegular,
  BodySemibold,
  BodyRegular,
  SubheadlineSemibold,
  SubheadlineRegular,
  FootnoteSemibold,
  FootnoteRegular,
  Caption200Semibold,
  Caption200Regular,
  Caption100Semibold,
  Caption100Regular,
  Caption50Semibold,
  Caption50Regular,
}

export const FontConfigs: { [key in Fonts]: FontParams } = {
  [Fonts.Display200]: {
    size: FONT_SIZES.S72,
    leading: FONT_LEADING.L75,
    weight: FONT_WEIGHTS.W700,
  },
  [Fonts.Display100]: {
    size: FONT_SIZES.S48,
    leading: FONT_LEADING.L60,
    weight: FONT_WEIGHTS.W700,
  },
  [Fonts.Title200]: {
    size: FONT_SIZES.S40,
    leading: FONT_LEADING.L50,
    weight: FONT_WEIGHTS.W700,
  },
  [Fonts.Title100]: {
    size: FONT_SIZES.S32,
    leading: FONT_LEADING.L40,
    weight: FONT_WEIGHTS.W700,
  },
  [Fonts.HeadlineSemibold]: {
    size: FONT_SIZES.S22,
    leading: FONT_LEADING.L35,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.HeadlineRegular]: {
    size: FONT_SIZES.S22,
    leading: FONT_LEADING.L35,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.BodySemibold]: {
    size: FONT_SIZES.S16,
    leading: FONT_LEADING.L24,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.BodyRegular]: {
    size: FONT_SIZES.S16,
    leading: FONT_LEADING.L24,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.SubheadlineSemibold]: {
    size: FONT_SIZES.S14,
    leading: FONT_LEADING.L20,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.SubheadlineRegular]: {
    size: FONT_SIZES.S14,
    leading: FONT_LEADING.L20,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.FootnoteSemibold]: {
    size: FONT_SIZES.S13,
    leading: FONT_LEADING.L18,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.FootnoteRegular]: {
    size: FONT_SIZES.S13,
    leading: FONT_LEADING.L18,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.Caption200Semibold]: {
    size: FONT_SIZES.S12,
    leading: FONT_LEADING.L16,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.Caption200Regular]: {
    size: FONT_SIZES.S12,
    leading: FONT_LEADING.L16,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.Caption100Semibold]: {
    size: FONT_SIZES.S11,
    leading: FONT_LEADING.L15,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.Caption100Regular]: {
    size: FONT_SIZES.S11,
    leading: FONT_LEADING.L15,
    weight: FONT_WEIGHTS.W400,
  },
  [Fonts.Caption50Semibold]: {
    size: FONT_SIZES.S10,
    leading: FONT_LEADING.L14,
    weight: FONT_WEIGHTS.W600,
  },
  [Fonts.Caption50Regular]: {
    size: FONT_SIZES.S10,
    leading: FONT_LEADING.L14,
    weight: FONT_WEIGHTS.W400,
  },
};

/* eslint-enable @typescript-eslint/no-unused-vars, no-unused-vars */

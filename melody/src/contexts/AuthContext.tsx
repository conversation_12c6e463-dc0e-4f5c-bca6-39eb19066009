"use client";

import { usePathname } from "next/navigation";
import { useRouter } from "next/router";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { User } from "../models/user";

// Type definitions for the auth response
export interface SbUserIdentity {
  identity_id: string;
  id: string;
  user_id: string;
  identity_data: {
    email: string;
    email_verified: boolean;
    phone_verified: boolean;
    sub: string;
  };
  provider: string;
  last_sign_in_at: string;
  created_at: string;
  updated_at: string;
  email: string;
}

export interface SbUser extends User {
  aud: string;
  role: string;
  email: string;
  email_confirmed_at: string;
  phone: string;
  confirmed_at: string;
  last_sign_in_at: string;
  app_metadata: {
    provider: string;
    providers: string[];
  };
  user_metadata: Record<string, unknown>;
  identities: SbUserIdentity[];
  created_at: string;
  updated_at: string;
  is_anonymous: boolean;
}

interface AuthContextType {
  user: SbUser | null;
  id: number | null;
  loading: boolean;
  setAuthData: (data: { user: SbUser; id: number } | null) => void;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  id: null,
  loading: true,
  setAuthData: () => {},
  logout: () => {},
  isAuthenticated: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<SbUser | null>(null);
  const [id, setId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  const isAuthPage =
    pathname?.includes("/login") || pathname?.includes("/register");

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Skip auth check on auth pages to prevent infinite loops
        if (isAuthPage) {
          setLoading(false);
          return;
        }

        // Check for stored user data on mount
        const storedUser = localStorage.getItem("sbUser");
        const storedId = localStorage.getItem("userId");

        if (storedUser && storedId) {
          setUser(JSON.parse(storedUser));
          setId(parseInt(storedId));
        }

        setLoading(false);
      } catch (error) {
        console.error("Auth check failed:", error);
        setUser(null);
        setLoading(false);
      }
    };

    checkAuth();
  }, [isAuthPage]);

  const setAuthData = (data: { user: SbUser; id: number } | null) => {
    if (data) {
      setUser(data.user);
      setId(data.id);
      // Store minimal data in localStorage
      localStorage.setItem("sbUser", JSON.stringify(data.user));
      localStorage.setItem("userId", data.id.toString());
    } else {
      setUser(null);
      setId(null);
      localStorage.removeItem("sbUser");
      localStorage.removeItem("userId");
    }
  };

  const logout = () => {
    setAuthData(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        id,
        loading,
        setAuthData,
        logout,
        isAuthenticated: !!user && !!id,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Custom hook for protected routes
export const useRequireAuth = (redirectUrl: string = "/login") => {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push(redirectUrl);
    }
  }, [isAuthenticated, loading, redirectUrl, router]);

  return { user, loading, isAuthenticated };
};

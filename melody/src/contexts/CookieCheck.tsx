"use client";

import { BaseText } from "@/components/base/BaseText";
import { Fonts } from "@/constants/typology";
import {
  <PERSON>ieOutlined,
  SettingsOutlined,
  ShieldOutlined,
} from "@mui/icons-material";
import { Box, Card, CardContent } from "@mui/material";
import { useEffect, useState } from "react";
import { BeatleafBanner } from "../components/icons/BeatleafBanner";

const CookieWarningScreen = () => (
  <Box
    sx={{
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background:
        "linear-gradient(135deg, #020617 0%, #131728 50%, #020617 100%)",
      zIndex: 50,
      "&::before": {
        content: '""',
        position: "absolute",
        inset: 0,
        opacity: 0.05,
        background: `
          radial-gradient(circle at 10% 10%, #4338ca 0%, transparent 25%),
          radial-gradient(circle at 80% 20%, #312e81 0%, transparent 25%),
          radial-gradient(circle at 25% 80%, #4338ca 0%, transparent 25%),
          radial-gradient(circle at 70% 70%, #312e81 0%, transparent 25%)
        `,
      },
    }}
  >
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
        p: 3,
        textAlign: "center",
        position: "relative",
      }}
    >
      <Box sx={{ maxWidth: 400, width: "100%", position: "relative" }}>
        {/* Logo/Brand */}
        <Box
          className="flex flex-col justify-center"
          sx={{ position: "relative", mb: 4 }}
        >
          <BeatleafBanner className="mx-auto mb-2" />
          <Box
            sx={{
              width: 80,
              height: 4,
              background: "linear-gradient(90deg, #4338ca, #312e81)",
              mx: "auto",
              borderRadius: 2,
            }}
          />
        </Box>

        {/* Cookie icon with glow effect */}
        <Box sx={{ position: "relative", mb: 4 }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              mx: "auto",
              background: "linear-gradient(135deg, #4338ca, #312e81)",
              borderRadius: 3,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 25px 50px -12px rgba(16, 185, 129, 0.25)",
              position: "relative",
              zIndex: 1,
            }}
          >
            <CookieOutlined
              sx={{ fontSize: 40, opacity: 0.8 }}
              className="text-white"
            />
          </Box>
          <Box
            sx={{
              position: "absolute",
              inset: 0,
              width: 80,
              height: 80,
              mx: "auto",
              background: "#4338ca",
              borderRadius: 3,
              filter: "blur(24px)",
              opacity: 0.5,
              animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
            }}
          />
        </Box>

        {/* Main heading */}
        <Box sx={{ mb: 4 }}>
          <BaseText
            variant={Fonts.HeadlineSemibold}
            className="text-2xl font-bold text-white mb-2"
          >
            Cookies Required
          </BaseText>
          <BaseText
            variant={Fonts.BodyRegular}
            className="text-slate-400 text-lg"
          >
            Enable cookies to continue using beatleaf
          </BaseText>
        </Box>

        {/* Information cards */}
        <Box sx={{ mb: 4 }}>
          <Card
            sx={{
              background: "rgba(30, 41, 59, 0.5)",
              backdropFilter: "blur(8px)",
              border: "1px solid rgba(51, 65, 85, 0.5)",
              borderRadius: 3,
              mb: 2,
              "&:hover": {
                background: "rgba(30, 41, 59, 0.7)",
              },
              transition: "background-color 0.3s ease",
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1.5,
                  textAlign: "left",
                }}
              >
                <ShieldOutlined
                  sx={{ fontSize: 24 }}
                  className="text-[#4338ca] mt-0.5 flex-shrink-0"
                />
                <Box>
                  <BaseText
                    variant={Fonts.BodySemibold}
                    className="text-white font-medium mb-1"
                  >
                    Essential Functionality
                  </BaseText>
                  <BaseText
                    variant={Fonts.SubheadlineRegular}
                    className="text-slate-300"
                  >
                    We use essential cookies to enable core website
                    functionality and user authentication.
                  </BaseText>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card
            sx={{
              background: "rgba(30, 41, 59, 0.5)",
              backdropFilter: "blur(8px)",
              border: "1px solid rgba(51, 65, 85, 0.5)",
              borderRadius: 3,
              "&:hover": {
                background: "rgba(30, 41, 59, 0.7)",
              },
              transition: "background-color 0.3s ease",
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1.5,
                  textAlign: "left",
                }}
              >
                <SettingsOutlined
                  sx={{ fontSize: 24 }}
                  className="text-[#4338ca] mt-0.5 flex-shrink-0"
                />
                <Box>
                  <BaseText
                    variant={Fonts.BodySemibold}
                    className="text-white font-medium mb-1"
                  >
                    Necessary Only
                  </BaseText>
                  <BaseText
                    variant={Fonts.SubheadlineRegular}
                    className="text-slate-300"
                  >
                    These cookies are required for the website to function and
                    cannot be disabled.
                  </BaseText>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Footer note */}
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-xs text-slate-500"
        >
          By continuing, you acknowledge that cookies are necessary for the
          proper functioning of this application.
        </BaseText>
      </Box>
    </Box>
  </Box>
);

export default function CookieCheck({
  children,
}: {
  children: React.ReactNode;
}) {
  const [cookiesBlocked, setCookiesBlocked] = useState(true);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkCookieSupport = async () => {
      try {
        // Add a minimum delay of 1000ms (1 second)
        await new Promise((resolve) => setTimeout(resolve, 1000));

        document.cookie = "cookieTest=1; SameSite=Strict";
        const cookieEnabled = document.cookie.indexOf("cookieTest") !== -1;

        // Clean up test cookie
        document.cookie =
          "cookieTest=1; SameSite=Strict; expires=Thu, 01 Jan 1970 00:00:01 GMT";

        setCookiesBlocked(!cookieEnabled);
      } catch (e) {
        setCookiesBlocked(true);
      } finally {
        setIsChecking(false);
      }
    };

    checkCookieSupport();
  }, []);

  // Don't show anything while checking
  if (isChecking) {
    return null;
  }

  if (cookiesBlocked) {
    return <CookieWarningScreen />;
  }

  return <>{children}</>;
}

"use client";

import { BaseText } from "@/components/base/BaseText";
import { Fonts } from "@/constants/typology";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import {
  deviceDetect,
  isDesktop,
  isIPad13,
  isMobile,
  isTablet,
} from "react-device-detect";

// Mobile context
const MobileContext = createContext({
  isMobile: false,
  isTablet: false,
  isIPad: false,
  isDesktopDevice: false,
  deviceInfo: {},
  // Add screen size information
  screenWidth: 0,
  screenHeight: 0,
  orientation: "portrait" as "portrait" | "landscape",
});

// Mobile splash screen component
const MobileSplashScreen = () => (
  <div className="fixed top-0 inset-0 bg-bgdark z-50 flex flex-col items-center justify-center p-6 text-center">
    <BaseText
      variant={Fonts.Title200}
      className="text-2xl font-bold text-white mb-4"
    >
      beatleaf
    </BaseText>
    <BaseText variant={Fonts.BodyRegular} className="text-slate-300 mb-2">
      Mobile phone support coming soon!
    </BaseText>
    <BaseText variant={Fonts.BodyRegular} className="text-slate-400 text-sm">
      Use a computer, laptop, or tablet
      <br /> for the best experience.
      <br />
      <br />{" "}
      <em>
        Alternatively, request the desktop version
        <br /> on your phone.
      </em>
    </BaseText>
  </div>
);

export const MobileProvider = ({ children }: { children: ReactNode }) => {
  const [isMobileView, setIsMobileView] = useState(false);
  const [isTabletDevice, setIsTabletDevice] = useState(false);
  const [isIPadDevice, setIsIPadDevice] = useState(false);
  const [isDesktopDevice, setIsDesktopDevice] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState({});
  const [screenWidth, setScreenWidth] = useState(0);
  const [screenHeight, setScreenHeight] = useState(0);
  const [orientation, setOrientation] = useState<"portrait" | "landscape">(
    "portrait"
  );

  useEffect(() => {
    // Get device info
    const deviceData = deviceDetect(window.navigator.userAgent);
    setDeviceInfo(deviceData);

    // Check device types
    setIsIPadDevice(isIPad13);
    setIsTabletDevice(isTablet);
    setIsDesktopDevice(isDesktop);

    // Update screen dimensions and orientation
    const updateDimensions = () => {
      setScreenWidth(window.innerWidth);
      setScreenHeight(window.innerHeight);
      setOrientation(
        window.innerWidth > window.innerHeight ? "landscape" : "portrait"
      );
    };

    // Initial update
    updateDimensions();

    // Check if device is mobile
    const checkMobile = () => {
      // iPads should not be treated as mobile
      if (isIPad13) {
        setIsMobileView(false);
        return;
      }

      const isMobileSize =
        window.innerWidth < 640 ||
        (window.innerWidth < 900 && window.innerHeight < 450);

      // Only set as mobile if it's a mobile size AND not a tablet
      setIsMobileView((isMobileSize || isMobile) && !isTablet);

      // Update dimensions and orientation on resize
      updateDimensions();
    };

    // Initial check
    checkMobile();

    // Add resize listener
    window.addEventListener("resize", checkMobile);

    // Add device-specific classes to body
    const body = document.body;
    if (isIPad13) body.classList.add("is-ipad");
    if (isTablet) body.classList.add("is-tablet");
    if (isMobile) body.classList.add("is-mobile-device");
    if (isDesktop) body.classList.add("is-desktop");

    // Cleanup
    return () => {
      window.removeEventListener("resize", checkMobile);
      body.classList.remove(
        "is-ipad",
        "is-tablet",
        "is-mobile-device",
        "is-desktop"
      );
    };
  }, []);

  return (
    <MobileContext.Provider
      value={{
        isMobile: isMobileView,
        isTablet: isTabletDevice,
        isIPad: isIPadDevice,
        isDesktopDevice,
        deviceInfo,
        screenWidth,
        screenHeight,
        orientation,
      }}
    >
      {isMobileView && <MobileSplashScreen />}
      {!isMobileView && children}
    </MobileContext.Provider>
  );
};

export const useMobile = () => useContext(MobileContext);

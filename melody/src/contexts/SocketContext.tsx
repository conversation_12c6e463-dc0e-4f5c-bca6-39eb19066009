"use client";

import { useQueryClient } from "@tanstack/react-query";
import { createContext, useContext, useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";
import { useAuth } from "./AuthContext";

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  joinProjectRoom: (projectId: number) => void;
  leaveProjectRoom: (projectId: number) => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  joinProjectRoom: () => {},
  leaveProjectRoom: () => {},
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user) return;

    const socketInstance = io(
      process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200",
      {
        transports: ["websocket"],
        autoConnect: true,
      }
    );

    socketInstance.on("connect", () => {
      console.log("Socket connected:", socketInstance.id);
      setIsConnected(true);
      socketInstance.emit("join", user.id);
    });

    // Listen for project updates
    socketInstance.on("comment", (data: { projectId: number }) => {
      queryClient.refetchQueries({
        queryKey: ["projects", data.projectId, "comments"],
        exact: true,
      });
    });

    socketInstance.on("task", (data: { projectId: number }) => {
      queryClient.refetchQueries({
        queryKey: ["projects", data.projectId, "tasks"],
        exact: true,
      });
    });

    socketInstance.on("project", (data: { projectId: number }) => {
      queryClient.refetchQueries({ queryKey: ["projects", data.projectId] });
    });

    socketInstance.on("invite", () => {
      queryClient.refetchQueries({
        queryKey: ["users", user.id, "invites"],
        exact: true,
      });
    });

    socketInstance.on("message", (data: { conversationId: string }) => {
      // Invalidate the specific conversation query
      queryClient.invalidateQueries({
        queryKey: ["conversations", data.conversationId],
      });
      
      // Invalidate the user's conversations list to update the message previews
      queryClient.invalidateQueries({
        queryKey: ["users", user?.name, "conversations"],
      });
      
      // Invalidate unread count
      queryClient.invalidateQueries({
        queryKey: ["unreadMessagesCount"],
      });
      
      // Force an immediate refetch of the conversations list to update the UI
      queryClient.refetchQueries({
        queryKey: ["users", user?.name, "conversations"],
      });
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, [user, queryClient]);

  const joinProjectRoom = (projectId: number) => {
    if (socket) {
      socket.emit("join_project", projectId);
      console.log(`Joined project room: ${projectId}`);
    }
  };

  const leaveProjectRoom = (projectId: number) => {
    if (socket) {
      socket.emit("leave_project", projectId);
      console.log(`Left project room: ${projectId}`);
    }
  };

  return (
    <SocketContext.Provider
      value={{ socket, isConnected, joinProjectRoom, leaveProjectRoom }}
    >
      {children}
    </SocketContext.Provider>
  );
}

// theme/theme.ts
"use client";

import { createTheme } from "@mui/material/styles";

export const theme = createTheme({
  typography: {
    fontFamily: "var(--font-instrument-sans), sans-serif",
  },
  components: {
    MuiTextField: {
      styleOverrides: {
        root: {
          fontFamily: "var(--font-instrument-sans)",
        },
      },
    },
  },
  palette: {
    primary: {
      main: "#4338ca",
    },
    secondary: {
      main: "#14b8a6",
    },
  },
});

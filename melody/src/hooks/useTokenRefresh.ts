// src/hooks/useTokenRefresh.ts

"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useRefreshTokenMutation } from "../routes/auth";

// Default refresh interval: 50 minutes (more aggressive than 55)
const DEFAULT_REFRESH_INTERVAL = 50 * 60 * 1000;

export const useTokenRefresh = (refreshInterval = DEFAULT_REFRESH_INTERVAL) => {
  const { isAuthenticated } = useAuth();
  const refreshMutation = useRefreshTokenMutation();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const pathname = usePathname();

  // Check if we're on an auth page (login, register, etc.)
  const isAuthPage =
    pathname?.includes("/login") || pathname?.includes("/register");

  useEffect(() => {
    // Only set up refresh if user is authenticated and not on auth pages
    if (!isAuthenticated || isAuthPage) {
      return;
    }

    // Clear any existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Set up periodic token refresh
    timerRef.current = setInterval(() => {
      refreshMutation.mutate();
    }, refreshInterval);

    // Clean up on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isAuthenticated, refreshInterval, refreshMutation, isAuthPage]);

  // Return the refresh function for manual refreshes
  return {
    refreshToken: () => {
      // Only attempt refresh if not on auth pages
      if (!isAuthPage) {
        refreshMutation.mutate();
      }
    },
    isRefreshing: refreshMutation.isPending,
  };
};

import { useSocket } from "@/contexts/SocketContext";
import { useEffect, useState } from "react";

interface UploadProgress {
  id: string;
  fileName: string;
  progress: number;
  status: "started" | "uploading" | "completed" | "error" | "idle";
  bytesUploaded?: number;
  bytesTotal?: number;
  error?: string;
  url?: string;
}

export function useUploadProgress() {
  const [uploads, setUploads] = useState<Record<string, UploadProgress>>({});
  const { socket } = useSocket();

  useEffect(() => {
    if (!socket) return;

    socket.on("upload-progress", (data: UploadProgress) => {
      setUploads((prev) => ({
        ...prev,
        [data.id]: data,
      }));

      // Remove completed uploads after a delay
      if (data.status === "completed" || data.status === "error") {
        setTimeout(() => {
          setUploads((prev) => {
            const newUploads = { ...prev };
            delete newUploads[data.id];
            return newUploads;
          });
        }, 3000); // Remove after 3 seconds
      }
    });

    return () => {
      socket.off("upload-progress");
    };
  }, [socket]);

  return { uploads, socket };
}

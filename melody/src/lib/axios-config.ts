import axios from "axios";

export const API_URL =
  process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

// Add this function to your axios-config.ts file
export const getAuthToken = (): string => {
  // Try multiple sources for the token
  // 1. Check localStorage
  const localToken = localStorage.getItem("token");
  if (localToken) return localToken;

  // 2. Check cookies
  const cookieToken = document.cookie
    .split("; ")
    .find((row) => row.startsWith("auth_token="))
    ?.split("=")[1];
  if (cookieToken) return cookieToken;

  // 3. Return empty string if no token found
  return "";
};

// Configure axios defaults
axios.defaults.baseURL = API_URL;
axios.defaults.withCredentials = true;

// Add request interceptor for CSRF tokens
axios.interceptors.request.use(
  async (config) => {
    // Skip for GET, HEAD, OPTIONS requests (they don't need CSRF protection)
    if (["get", "head", "options"].includes(config.method || "")) {
      return config;
    }

    // Try to get CSRF token from cookie first
    let csrfToken = document.cookie
      .split("; ")
      .find((row) => row.startsWith("csrf_token="))
      ?.split("=")[1];

    // If no cookie, try localStorage
    if (!csrfToken) {
      csrfToken = localStorage.getItem("csrf_token") || "";
    }

    // Add CSRF token to headers if available
    if (csrfToken) {
      config.headers = config.headers || {};
      config.headers["X-CSRF-Token"] = csrfToken;
    } else {
      // If no token found, try to fetch a new one
      try {
        const response = await axios.get(`${API_URL}/csrf-token`, {
          withCredentials: true,
        });
        csrfToken = response.data.csrfToken;

        if (!csrfToken) {
          throw new Error("Failed to fetch CSRF token");
        }

        // Store in localStorage as fallback
        localStorage.setItem("csrf_token", csrfToken);

        config.headers = config.headers || {};
        config.headers["X-CSRF-Token"] = csrfToken;
      } catch (error) {
        console.error("Failed to fetch CSRF token:", error);
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle CSRF errors
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    // If error is due to CSRF token, try to refresh and retry once
    if (
      error.response?.status === 403 &&
      error.response?.data?.error === "CSRF verification failed" &&
      !error.config._retry
    ) {
      error.config._retry = true;

      try {
        // Fetch a new CSRF token
        const { data } = await axios.get(`${API_URL}/csrf-token`, {
          withCredentials: true,
        });
        const newCsrfToken = data.csrfToken;

        // Store in localStorage
        localStorage.setItem("csrf_token", newCsrfToken);

        // Try to get from cookie first
        const cookieToken = document.cookie
          .split("; ")
          .find((row) => row.startsWith("csrf_token="))
          ?.split("=")[1];

        // Use cookie token if available, otherwise use localStorage token
        error.config.headers["X-CSRF-Token"] = cookieToken || newCsrfToken;

        // Retry the original request
        return axios(error.config);
      } catch (retryError) {
        return Promise.reject(retryError);
      }
    }

    return Promise.reject(error);
  }
);

// For requests to signed URLs, don't send credentials
axios.interceptors.request.use(
  (config) => {
    // Check if this is a request to a Supabase signed URL
    if (config.url?.includes("supabase") || config.url?.includes("token=")) {
      // Don't send credentials for signed URL requests
      config.withCredentials = false;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add auth token to every request
axios.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add error logging interceptor
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 Unauthorized errors by redirecting to login
    if (error.response?.status === 401) {
      console.error(
        "Authentication error:",
        error.response?.data?.message || "Session expired"
      );

      // Clear any stored auth data
      localStorage.removeItem("sbUser");
      localStorage.removeItem("userId");
      localStorage.removeItem("accessToken");

      // Only redirect if we're in the browser environment and not already on the login page
      if (
        typeof window !== "undefined" &&
        !window.location.pathname.includes("/login") &&
        !window.location.pathname.includes("/register")
      ) {
        // Store the current path to redirect back after login
        localStorage.setItem("redirectAfterLogin", window.location.pathname);
        window.location.href = "/login";
      }
    }

    return Promise.reject(error);
  }
);

// Export configured axios
export default axios;

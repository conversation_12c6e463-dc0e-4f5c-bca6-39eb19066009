// Use dynamic imports and conditional logic for browser-only code
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let streamSaver: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let createWriteStreamFn: any = null;

// Only initialize in browser environments
if (typeof window !== "undefined") {
  // Dynamic import to prevent server-side execution
  const initStreamSaver = async () => {
    try {
      const streamSaverLib = await import("streamsaver");
      streamSaver = streamSaverLib.default || streamSaverLib;
      // Configure StreamSaver to use a local mitm.html file
      streamSaver.mitm = "/mitm.html";
      // Set a larger chunk size for better performance
      streamSaver.writableStrategy = { highWaterMark: 1024 * 1024 }; // 1MB chunks
      createWriteStreamFn = streamSaver.createWriteStream;
    } catch (error) {
      console.error("Failed to initialize StreamSaver:", error);
    }
  };

  // Initialize but don't block rendering
  initStreamSaver();
}

// Export a safe version that checks for browser environment
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createWriteStream = (...args: any[]) => {
  if (typeof window === "undefined") {
    console.warn("createWriteStream called in server environment");
    return null;
  }
  return createWriteStreamFn ? createWriteStreamFn(...args) : null;
};

// Type export
export type { WritableStream } from "streamsaver";

import { SbUser } from "../contexts/AuthContext";

export type UserRole = "user" | "admin" | "moderator";

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignUpCredentials extends LoginCredentials {
  name?: string;
  termsPrivacyTime?: string;
  termsPrivacyContent?: string;
  ndaTime?: string;
  ndaContent?: string;
}

export interface AuthenticationError {
  message: string;
  field?: "email" | "password" | "general";
}

export class AuthenticationException extends Error {
  public readonly errors: AuthenticationError[];

  constructor(errors: AuthenticationError[]) {
    super(errors[0]?.message || "Authentication failed");
    this.name = "AuthenticationException";
    this.errors = errors;
  }
}

export interface AuthResponse {
  data: {
    user: SbUser;
    id: number;
    // token removed since we're using cookies now
  };
}

import { Project, User } from "@/models";

// Conversation model interface
export interface Conversation {
  id: string;
  authorId: bigint;
  createdAt: Date;
  updatedAt: Date;
  name?: string;
  description?: string;
  projectId?: bigint | null;
  project?: Project;
  participants?: ConversationParticipant[];
  messages?: Message[];
}

// ConversationParticipant model interface
export interface ConversationParticipant {
  id: string;
  conversationId: string;
  userId: bigint;
  joinedAt: Date;
  lastSeenAt: Date;
  user: User;
}

// Message model interface
export interface Message {
  id: string;
  content: string;
  conversationId: string;
  senderId: bigint;
  createdAt: Date;
  updatedAt: Date;
  sender: User;
}

// Body interfaces

// Create Conversation
export interface CreateConversationBody {
  name: string;
  description?: string;
  projectId?: number | null;
  participantIds?: number[];
  fromProfile?: boolean;
}

// Update Conversation
export interface UpdateConversationBody {
  name?: string;
  description?: string;
  participantIds?: number[];
  participantLastSeen?: boolean;
}

// Create Message
export interface CreateMessageBody {
  conversationId: string;
  content: string;
}

// Update Message
export interface UpdateMessageBody {
  content?: string;
}

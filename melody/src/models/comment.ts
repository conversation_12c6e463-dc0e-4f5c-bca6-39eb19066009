import { Project } from "./project";
import { User } from "./user";

export interface Comment {
  id: number; // Unique identifier for the comment
  authorId: number; // ID of the user who authored the comment
  projectId: number; // ID of the project to which the comment belongs
  content: string; // The content of the comment
  createdAt: string; // Timestamp when the comment was created (ISO date string)
  updatedAt: string; // Timestamp when the comment was last updated (ISO date string)
  author: User; // The user object representing the author of the comment
  project: Project; // The project object to which the comment belongs
}

export interface CreateCommentBody {
  // Request body for creating a new comment
  content: string; // The content of the comment
  projectId: number; // The ID of the project to which the comment will be associated
}

export interface UpdateCommentBody {
  // Request body for updating an existing comment
  content?: string; // The updated content of the comment (optional)
}

// melody/src/models/file.ts
export interface File {
  id: number;
  name: string;
  versionId: number;
  folderId?: number;
  s3Path: string;
  fileType: string;
  fileSize: number;
  metadata?: unknown;
  createdAt: string;
  updatedAt: string;
}

// Create Project Request Interface
export interface CreateFileBody {
  name: string;
  versionId: number;
  s3Path: string;
  fileType: string;
  fileSize: number;
  metadata?: object;
  file: File | Blob;
}

export interface UpdateFileBody {
  name?: string;
  fileType?: string;
  fileSize?: number;
  metadata?: object;
}

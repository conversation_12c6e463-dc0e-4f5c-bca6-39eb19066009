import { Conversation, ProjectUser, Task, User, Version } from "@/models";

export interface CreateProjectBody {
  name: string;
  genre: string;
  description?: string;
  tempo?: number;
  key?: string;
}

// Update Project Request Interface
export interface UpdateProjectBody {
  name?: string;
  genre?: string;
  description?: string;
  tempo?: number;
  key?: string;
}

// Project Response Interface
export interface Project {
  id: number;
  name: string;
  genre: string;
  description?: string;
  tempo?: number;
  key?: string;
  createdAt: string;
  updatedAt: string;
  author: User;
  contributors: ProjectUser[];
  versions: Version[];
  tasks: Task[];
  conversations: Conversation[];
}

export interface AddContributorBody {
  userName: string;
}

export interface RemoveContributorBody {
  userName: string;
}

export enum MusicGenre {
  POP = "Pop",
  HYPERPOP = "Hyper Pop",
  ROCK = "Rock",
  ALT_ROCK = "Alternative Rock",
  PUNK_ROCK = "Punk Rock",
  INDIE_ROCK = "Indie Rock",
  METAL = "Metal",
  HIP_HOP = "Hip Hop",
  TRAP = "Trap",
  R_AND_B = "R&B",
  ELECTRONIC = "Electronic",
  EDM = "EDM",
  JAZZ = "Jazz",
  CLASSICAL = "Classical",
  COUNTRY = "Country",
  FOLK = "Folk",
  REGGAE = "Reggae",
  AFROBEATS = "Afrobeats",
  BLUES = "Blues",
  FUNK = "Funk",
  LATIN = "Latin",
  LOFI = "Lo-Fi",
  AMBIENT = "Ambient",
}

export enum KeyNote {
  C = "C",
  CS_DF = "C# / Db",
  D = "D",
  DS_EF = "D# / Eb",
  E = "E",
  F = "F",
  FS_GF = "F# / Gb",
  G = "G",
  GS_AF = "G# / Ab",
  A = "A",
  AS_BF = "A# / Bb",
  B = "B",
}

export enum KeyMode {
  MAJOR = "Major",
  MINOR = "Minor",
}

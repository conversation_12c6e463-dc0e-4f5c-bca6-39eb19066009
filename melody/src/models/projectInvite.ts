import { Project } from "./project";
import { User } from "./user";

export type ProjectInviteStatus = "pending" | "accepted" | "declined";

export interface ProjectInvite {
  id: number;
  projectId: number;
  userId: number;
  status: ProjectInviteStatus;
  createdAt: string;
  updatedAt: string;
  project: Project;
  user: User;
}

// Request body for creating a new project invite
export interface CreateProjectInviteBody {
  projectId: number;
  userId: number;
}

// Request body for updating an existing project invite
export interface UpdateProjectInviteBody {
  status: ProjectInviteStatus;
}

// Response for invite actions (accept/decline/revoke)
export interface ProjectInviteActionResponse {
  success: boolean;
  message: string;
}
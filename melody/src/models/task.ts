import { User } from "./user";

export interface Task {
  id: number;
  title: string;
  description?: string;
  priority?: string;
  status?: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  assigneeId?: number; // ID of the user assigned to this task
  authorId: number; // ID of the user who created this task
  author: User; // The user who created this task
  assignee?: User;
  versionId?: number | null; // Reference to a version
  folderId?: number | null; // Reference to a folder
  fileId?: number | null; // Reference to a file
}

// Type for creating a new task
export interface CreateTaskBody {
  projectId: number;
  title: string;
  assigneeId?: number | null;
  description?: string;
  category?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: Date; // ISO 8601 format (e.g., "2025-04-01T12:00:00Z")
}

// Type for updating an existing task
export interface UpdateTaskBody {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assigneeId?: number | null;
  versionId?: number | null;
  folderId?: number | null;
  fileId?: number | null;
}

export enum TaskStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  ON_HOLD = "on_hold",
  IN_REVIEW = "in_review",
  DONE = "done",
  CLOSED = "closed",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

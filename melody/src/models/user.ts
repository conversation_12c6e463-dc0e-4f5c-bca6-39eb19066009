export interface User {
  id: number;
  name: string;
  email: string;
  profileImg?: string;
  bio?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
  socials?: Array<{ platform: string; url: string }>;
  topGenres?: Array<{ genre: string; count: number }>;
}

export interface AudioSample {
  id: number;
  userId: number;
  name: string;
  s3Path: string;
  featured: boolean;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

// src/routes/auth.ts

"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { SbUser, useAuth } from "../contexts/AuthContext";
import axios from "../lib/axios-config";
import {
  AuthenticationException,
  AuthResponse,
  LoginCredentials,
  SignUpCredentials,
} from "../models/auth";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

// Query Keys
const AUTH_QUERY_KEYS = {
  currentUser: ["auth", "current-user"],
  csrfToken: ["auth", "csrf-token"],
};

// Authentication Service
const AuthService = {
  async signUp(credentials: SignUpCredentials): Promise<AuthResponse> {
    try {
      const { data } = await axios.post<AuthResponse>(
        `${API_URL}/auth/register`,
        credentials
      );
      return data;
    } catch (error) {
      throw axios.isAxiosError(error)
        ? new AuthenticationException(
            error.response?.data?.errors || [
              { message: "Registration failed", field: "general" },
            ]
          )
        : error;
    }
  },

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { data } = await axios.post<AuthResponse>(
        `${API_URL}/auth/login`,
        credentials
      );
      return data;
    } catch (error) {
      throw axios.isAxiosError(error)
        ? new AuthenticationException(
            error.response?.data?.errors || [
              {
                message: "Login failed, please check your credentials.",
                field: "general",
              },
            ]
          )
        : error;
    }
  },

  async logout(): Promise<void> {
    await axios.post(`${API_URL}/auth/logout`);
    localStorage.clear();
  },

  async getCurrentUser(): Promise<SbUser | null> {
    try {
      const { data } = await axios.get<SbUser>(`${API_URL}/me`);
      return data;
    } catch {
      return null;
    }
  },

  async refreshToken(): Promise<void> {
    try {
      // Try to refresh with cookies first (preferred method)
      await axios.post(
        `${API_URL}/auth/refresh`,
        {},
        {
          withCredentials: true,
        }
      );
    } catch (error) {
      // If that fails, try with the refresh token from localStorage or cookies
      const refreshToken =
        localStorage.getItem("refresh_token") ||
        document.cookie
          .split("; ")
          .find((row) => row.startsWith("refresh_token="))
          ?.split("=")[1];

      if (refreshToken) {
        await axios.post(
          `${API_URL}/auth/refresh`,
          { refresh_token: refreshToken },
          { withCredentials: true }
        );
      } else {
        throw error;
      }
    }
  },
};

// React Query Hooks with Auth Context
export const useSignUpMutation = () => {
  const queryClient = useQueryClient();
  const { setAuthData } = useAuth();
  return useMutation<AuthResponse, AuthenticationException, SignUpCredentials>({
    mutationFn: AuthService.signUp,
    onSuccess: async (data) => {
      // No need to store token in localStorage since we're using HTTP-only cookies
      setAuthData({
        user: data.data.user,
        id: data.data.id,
      });
      queryClient.setQueryData(AUTH_QUERY_KEYS.currentUser, data.data.user);

      // Fetch a fresh CSRF token after signup
      const csrfToken = await fetchCsrfToken();
      if (csrfToken) {
        queryClient.setQueryData(AUTH_QUERY_KEYS.csrfToken, csrfToken);
      }
    },
  });
};

export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  const { setAuthData } = useAuth();
  return useMutation<AuthResponse, AuthenticationException, LoginCredentials>({
    mutationFn: AuthService.login,
    onSuccess: async (data) => {
      // Store user data in context
      setAuthData({
        user: data.data.user,
        id: data.data.id,
      });

      // Update query cache
      queryClient.setQueryData(AUTH_QUERY_KEYS.currentUser, data.data.user);

      // Fetch a fresh CSRF token after login
      const csrfToken = await fetchCsrfToken();
      if (csrfToken) {
        queryClient.setQueryData(AUTH_QUERY_KEYS.csrfToken, csrfToken);
      }
    },
  });
};

export const useLogoutMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error>({
    mutationFn: AuthService.logout,
    onSuccess: () => {
      localStorage.clear();
      queryClient.removeQueries({ queryKey: AUTH_QUERY_KEYS.currentUser });
    },
  });
};

export const useCurrentUserQuery = () => {
  return useQuery<SbUser | null>({
    queryKey: AUTH_QUERY_KEYS.currentUser,
    queryFn: AuthService.getCurrentUser,
    // No longer check for token in localStorage since we're using cookies
    enabled: true,
    retry: 1,
  });
};

export const useRefreshTokenMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error>({
    mutationFn: async () => {
      try {
        console.log("Attempting to refresh token...");

        // Try to refresh with cookies
        const response = await axios.post(
          `${API_URL}/auth/refresh`,
          {},
          {
            withCredentials: true,
          }
        );

        console.log("Token refresh successful");

        // If we get user data back, update it
        if (response.data?.data?.user) {
          queryClient.setQueryData(
            AUTH_QUERY_KEYS.currentUser,
            response.data.data.user
          );
        }
      } catch (error) {
        console.error("Error refreshing token:", error);

        // Try fallback method with explicit refresh token
        try {
          // Get refresh token from multiple sources
          const refreshToken =
            document.cookie
              .split("; ")
              .find((row) => row.startsWith("refresh_token="))
              ?.split("=")[1] || localStorage.getItem("refresh_token");

          if (!refreshToken) {
            console.error("No refresh token found in cookies or localStorage");
            throw new Error("No refresh token available");
          }

          console.log("Attempting refresh with explicit token");

          const response = await axios.post(
            `${API_URL}/auth/refresh`,
            { refresh_token: refreshToken },
            { withCredentials: true }
          );

          console.log("Fallback token refresh successful");

          // If we get user data back, update it
          if (response.data?.data?.user) {
            queryClient.setQueryData(
              AUTH_QUERY_KEYS.currentUser,
              response.data.data.user
            );
          }
        } catch (fallbackError) {
          console.error("Fallback refresh failed:", fallbackError);
          throw fallbackError;
        }
      }
    },
  });
};

// Add this function to fetch a CSRF token
const fetchCsrfToken = async (): Promise<string | null> => {
  try {
    const { data } = await axios.get(`${API_URL}/csrf-token`, {
      withCredentials: true,
    });

    // Store in localStorage as fallback
    if (data.csrfToken) {
      localStorage.setItem("csrf_token", data.csrfToken);
    }

    return data.csrfToken;
  } catch (error) {
    console.error("Failed to fetch CSRF token:", error);
    return null;
  }
};

// Add this hook to use in components
export const useCsrfToken = () => {
  return useQuery<string | null>({
    queryKey: AUTH_QUERY_KEYS.csrfToken,
    queryFn: fetchCsrfToken,
    staleTime: 30 * 60 * 1000, // Consider token stale after 30 minutes
    refetchOnWindowFocus: false,
  });
};

// Axios Interceptor to attach tokens
axios.interceptors.request.use(
  (config) => {
    // Set withCredentials to true to send cookies with requests
    config.withCredentials = true;

    // Add CSRF token to headers for non-GET requests
    if (config.method !== "get") {
      const csrfToken = document.cookie
        .split("; ")
        .find((row) => row.startsWith("csrf_token="))
        ?.split("=")[1];

      if (csrfToken) {
        config.headers["X-CSRF-Token"] = csrfToken;
      }
    }

    // Keep the token logic for backward compatibility
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Create a token refresh interceptor
let isRefreshing = false;
let refreshPromise: Promise<void> | null = null;

// Add axios response interceptor to handle token refresh
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If the error is not 401 or we've already tried to refresh or we're on an auth page, reject
    if (
      error.response?.status !== 401 ||
      originalRequest._retry ||
      error.config.url?.includes("login") ||
      error.config.url?.includes("register")
    ) {
      return Promise.reject(error);
    }

    // Mark this request as retried
    originalRequest._retry = true;

    // If we're already refreshing, wait for that to complete
    if (isRefreshing) {
      if (refreshPromise) {
        try {
          await refreshPromise;
          // Retry the original request
          return axios(originalRequest);
        } catch (refreshError) {
          // If refresh fails, redirect to login
          window.location.href = "/login";
          return Promise.reject(refreshError);
        }
      }
    }

    // Start refreshing
    isRefreshing = true;

    try {
      // Create a refresh promise
      refreshPromise = AuthService.refreshToken();
      await refreshPromise;

      // Retry the original request
      return axios(originalRequest);
    } catch (refreshError) {
      console.error("Token refresh failed in interceptor:", refreshError);
      // If refresh fails, redirect to login
      window.location.href = "/login";
      return Promise.reject(refreshError);
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  }
);

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import {
  Conversation,
  CreateConversationBody,
  CreateMessageBody,
  Message,
  UpdateConversationBody,
  UpdateMessageBody,
} from "../models/chat";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const ChatService = {
  async getConversation(
    id: string,
    page: number
  ): Promise<Conversation & { messages: Message[]; hasMoreMessages: boolean }> {
    const { data } = await axios.get<{
      data: Conversation & { messages: Message[]; hasMoreMessages: boolean };
    }>(`${API_URL}/chat/conversations/${id}?page=${page}`);
    return data.data;
  },
  async getMessage(id: string): Promise<Message> {
    const { data } = await axios.get<{ data: Message }>(
      `${API_URL}/chat/messages/${id}`
    );
    return data.data;
  },
  async createConversation(
    body: CreateConversationBody
  ): Promise<Conversation> {
    const { data } = await axios.post<{ data: Conversation }>(
      `${API_URL}/chat/conversations`,
      body
    );
    return data.data;
  },
  async updateConversation(
    id: string,
    body: UpdateConversationBody
  ): Promise<Conversation> {
    const { data } = await axios.put<{ data: Conversation }>(
      `${API_URL}/chat/conversations/${id}`,
      body
    );
    return data.data;
  },
  async deleteConversation(id: string): Promise<void> {
    await axios.delete(`${API_URL}/chat/conversations/${id}`);
  },
  async createMessage(body: CreateMessageBody): Promise<Message> {
    const { data } = await axios.post<{ data: Message }>(
      `${API_URL}/chat/messages`,
      body
    );
    return data.data;
  },
  async updateMessage(id: string, body: UpdateMessageBody): Promise<Message> {
    const { data } = await axios.put<{ data: Message }>(
      `${API_URL}/chat/messages/${id}`,
      body
    );
    return data.data;
  },
  async deleteMessage(id: string): Promise<void> {
    await axios.delete(`${API_URL}/chat/messages/${id}`);
  },
  async getUnreadMessagesCount(): Promise<number> {
    const { data } = await axios.get<{ data: { unreadCount: number } }>(
      `${API_URL}/chat/unread`
    );
    return data.data.unreadCount || 0;
  },
};

export const useGetConversationQuery = (id: string, page: number = 1) => {
  return useQuery<
    Conversation & { messages: Message[]; hasMoreMessages: boolean },
    Error
  >({
    queryKey: ["conversations", id, page],
    queryFn: () => ChatService.getConversation(id, page),
    enabled: !!id,
  });
};

export const useGetMessageQuery = (id: string) => {
  return useQuery<Message, Error>({
    queryKey: ["messages", id],
    queryFn: () => ChatService.getMessage(id),
    enabled: !!id,
  });
};

export const useCreateConversationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Conversation, Error, CreateConversationBody>({
    mutationFn: (body) => ChatService.createConversation(body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });
};

export const useUpdateConversationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Conversation,
    Error,
    { id: string; body: UpdateConversationBody }
  >({
    mutationFn: ({ id, body }) => ChatService.updateConversation(id, body),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["conversations", id] });
    },
  });
};

export const useDeleteConversationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (id) => ChatService.deleteConversation(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ["conversations", id] });
    },
  });
};

export const useCreateMessageMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Message, Error, CreateMessageBody>({
    mutationFn: (body) => ChatService.createMessage(body),
    onSuccess: (msg) => {
      queryClient.invalidateQueries({
        queryKey: ["conversations", msg.conversationId],
      });
    },
  });
};

export const useUpdateMessageMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Message, Error, { id: string; body: UpdateMessageBody }>({
    mutationFn: ({ id, body }) => ChatService.updateMessage(id, body),
    onSuccess: (msg) => {
      queryClient.invalidateQueries({
        queryKey: ["conversations", msg.conversationId],
      });
    },
  });
};

export const useDeleteMessageMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, { id: string; conversationId: string }>({
    mutationFn: ({ id }) => ChatService.deleteMessage(id),
    onSuccess: (_, { conversationId }) => {
      queryClient.invalidateQueries({
        queryKey: ["conversations", conversationId],
      });
    },
  });
};

export const useGetUnreadMessagesCountQuery = () => {
  return useQuery<number, Error>({
    queryKey: ["unreadMessagesCount"],
    queryFn: () => ChatService.getUnreadMessagesCount(),
    refetchInterval: 60000, // Refetch every minute
  });
};

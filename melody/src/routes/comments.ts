import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import {
  Comment,
  CreateCommentBody,
  UpdateCommentBody,
} from "../models/comment";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const CommentService = {
  async createComment(body: CreateCommentBody): Promise<Comment> {
    const { data } = await axios.post<Comment>(`${API_URL}/comments`, body);
    return data;
  },

  async getComment(id: number): Promise<Comment> {
    const { data } = await axios.get<{ data: Comment }>(
      `${API_URL}/comments/${id}`
    );
    return data.data;
  },

  async getCommentsByProject(projectId: number): Promise<Comment[]> {
    const { data } = await axios.get<{ data: Comment[] }>(
      `${API_URL}/projects/${projectId}/comments`
    );
    return data.data;
  },

  async updateComment(id: number, body: UpdateCommentBody): Promise<Comment> {
    const { data } = await axios.put<Comment>(
      `${API_URL}/comments/${id}`,
      body
    );
    return data;
  },

  async deleteComment(id: number): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/comments/${id}`
    );
    return data;
  },
};

// Query Keys
const COMMENT_QUERY_KEYS = {
  all: ["comments"] as const,
  details: (id: number) => ["comments", id] as const,
  byProject: (projectId: number) =>
    ["projects", projectId, "comments"] as const,
};

// Mutation Hooks
export const useCreateCommentMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Comment, Error, CreateCommentBody>({
    mutationFn: CommentService.createComment,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: COMMENT_QUERY_KEYS.byProject(variables.projectId),
      });
    },
  });
};

export const useUpdateCommentMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Comment, Error, { id: number } & UpdateCommentBody>({
    mutationFn: ({ id, ...data }) => CommentService.updateComment(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: COMMENT_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: COMMENT_QUERY_KEYS.all });
    },
  });
};

export const useDeleteCommentMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean }, Error, number>({
    mutationFn: CommentService.deleteComment,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: COMMENT_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: COMMENT_QUERY_KEYS.all });
    },
  });
};

// Query Hooks
export const useGetCommentQuery = (id: number) => {
  return useQuery<Comment, Error>({
    queryKey: COMMENT_QUERY_KEYS.details(id),
    queryFn: () => CommentService.getComment(id),
    enabled: !!id,
  });
};

export const useGetCommentsByProjectQuery = (projectId: number) => {
  return useQuery<Comment[], Error>({
    queryKey: COMMENT_QUERY_KEYS.byProject(projectId),
    queryFn: () => CommentService.getCommentsByProject(projectId),
    enabled: !!projectId,
  });
};

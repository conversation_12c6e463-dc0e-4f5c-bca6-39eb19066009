// melody/src/routes/file.ts

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import { createWriteStream } from "../lib/stream-config";
import { File, UpdateFileBody } from "../models/file";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const FileService = {
  async createFile(formData: FormData): Promise<File> {
    const { data } = await axios.post<{ data: File }>(
      `${API_URL}/files`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return data.data;
  },

  async getFile(id: number): Promise<File> {
    const { data } = await axios.get<{ data: File }>(`${API_URL}/files/${id}`);
    return data.data;
  },

  async getFilesByVersion(id: number): Promise<File[]> {
    const { data } = await axios.get<{ data: File[] }>(
      `${API_URL}/versions/${id}/files`
    );
    return data.data;
  },

  async updateFile(id: number, body: UpdateFileBody): Promise<File> {
    const { data } = await axios.put<{ data: File }>(
      `${API_URL}/files/${id}`,
      body
    );
    return data.data;
  },

  async deleteFile(id: number): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/files/${id}`
    );
    return data;
  },

  async getFileContents(
    id: number
  ): Promise<{
    signedUrl: string;
    fileName: string;
    fileType: string;
    waveform: number[];
  }> {
    const { data } = await axios.get(`${API_URL}/files/${id}/contents`);
    return data.data;
  },
};
// Query Keys
const FILE_QUERY_KEYS = {
  all: ["files"] as const,
  details: (id: number) => ["files", id] as const,
  byVersion: (versionId: number) => ["versions", versionId, "files"] as const,
};

// Mutation Hooks
export const useCreateFileMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<File, Error, FormData>({
    mutationFn: FileService.createFile,
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: FILE_QUERY_KEYS.byVersion(data.versionId),
      });
    },
  });
};

export const useUpdateFileMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<File, Error, { id: number } & UpdateFileBody>({
    mutationFn: ({ id, ...data }) => FileService.updateFile(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: FILE_QUERY_KEYS.details(data.id),
      });
      queryClient.invalidateQueries({
        queryKey: FILE_QUERY_KEYS.byVersion(data.versionId), // Use data.versionId instead of data.id
      });
    },
  });
};

export const useDeleteFileMutation = (versionId: number) => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean }, Error, number>({
    mutationFn: FileService.deleteFile,
    onSuccess: () => {
      // Invalidate files by version
      queryClient.invalidateQueries({
        queryKey: FILE_QUERY_KEYS.byVersion(versionId),
      });
      
      // Force refetch
      queryClient.refetchQueries({
        queryKey: FILE_QUERY_KEYS.byVersion(versionId),
      });
    },
  });
};

// Query Hooks
export const useGetFileQuery = (id: number) => {
  return useQuery<File, Error>({
    queryKey: FILE_QUERY_KEYS.details(id),
    queryFn: () => FileService.getFile(id),
    enabled: !!id,
  });
};

export const useGetFilesByVersionQuery = (id: number) => {
  return useQuery<File[], Error>({
    queryKey: FILE_QUERY_KEYS.byVersion(id),
    queryFn: () => FileService.getFilesByVersion(id),
    enabled: !!id,
  });
};

export const useGetFileContentsMutation = () => {
  return useMutation<
    void | { signedUrl?: string; waveform: number[] },
    Error,
    { id: number; fileName: string; forPlayback?: boolean }
  >({
    mutationFn: async ({ id, fileName, forPlayback }) => {
      const response = await FileService.getFileContents(id);
      const { signedUrl, fileName: decodedFileName, waveform } = response;

      if (forPlayback) {
        // Return the signed URL directly for playback
        return { signedUrl, waveform };
      } else {
        // For downloads, use streaming with StreamSaver
        const downloadName = decodedFileName || fileName;

        // Create a write stream using StreamSaver with optimized settings
        const fileStream = createWriteStream(downloadName, {
          size: 0, // Let the browser determine size from response
          writableStrategy: { highWaterMark: 1024 * 1024 }, // 1MB buffer
        });

        // Fetch the file as a stream with optimized settings
        const fetchResponse = await fetch(signedUrl, {
          credentials: "omit", // Don't send credentials to Supabase
          headers: {
            Range: "bytes=0-", // Request the full file
          },
        });

        if (!fetchResponse.ok) throw new Error("Download failed");

        // Pipe the response to the write stream
        if (window.WritableStream && fetchResponse.body?.pipeTo) {
          // For browsers that support pipeTo
          return fetchResponse.body.pipeTo(fileStream);
        } else {
          // For browsers that don't support pipeTo
          const reader = fetchResponse.body?.getReader();
          const writer = fileStream.getWriter();

          if (!reader) throw new Error("Stream reading not supported");

          // Optimized pump function with larger chunks
          const pump = async () => {
            try {
              while (true) {
                const { done, value } = await reader.read();
                if (done) {
                  writer.close();
                  break;
                }
                await writer.write(value);
              }
            } catch (err) {
              writer.abort(err);
              throw err;
            }
          };

          return pump();
        }
      }
    },
  });
};

// melody/src/routes/folder.ts

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import { File } from "../models/file";
import { CreateFolderBody, Folder, UpdateFolderBody } from "../models/folder";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const FolderService = {
  async createFolder(body: CreateFolderBody): Promise<Folder> {
    const { data } = await axios.post<{ data: Folder }>(
      `${API_URL}/folders`,
      body
    );
    return data.data;
  },
  async getFolder(id: number): Promise<Folder> {
    const { data } = await axios.get<{ data: Folder }>(
      `${API_URL}/folders/${id}`
    );
    return data.data;
  },
  async getFoldersByVersion(id: number): Promise<Folder[]> {
    const { data } = await axios.get<{ data: Folder[] }>(
      `${API_URL}/versions/${id}/folders`
    );
    return data.data;
  },
  async updateFolder(id: number, body: UpdateFolderBody): Promise<Folder> {
    const { data } = await axios.put<{ data: Folder }>(
      `${API_URL}/folders/${id}`,
      body
    );
    return data.data;
  },

  async uploadFolder(formData: FormData): Promise<{
    folder: Folder;
    files: File[];
    totalUploaded: number;
    totalFailed: number;
  }> {
    const { data } = await axios.post<{
      data: {
        folder: Folder;
        files: File[];
        totalUploaded: number;
        totalFailed: number;
      };
    }>(`${API_URL}/folders/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return data.data;
  },

  async deleteFolder(id: number): Promise<{ success: boolean }> {
    const data = await axios.delete<{ success: boolean }>(
      `${API_URL}/folders/${id}`
    );
    return data.data;
  },

  async getFolderContents(id: number) {
    return axios.get(`${API_URL}/folders/${id}/download`, {
      responseType: "blob",
    });
  },
};

const FOLDER_QUERY_KEYS = {
  all: ["folders"] as const,
  details: (id: number) => ["folders", id] as const,
  byVersion: (versionId: number) => ["versions", versionId, "folders"] as const,
};

export const useGetFolderQuery = (id: number) => {
  return useQuery<Folder, Error>({
    queryKey: FOLDER_QUERY_KEYS.details(id), // check #2 for cached query key, returns imdtly if found
    queryFn: () => FolderService.getFolder(id), // if no cache, make this API call to backend
    enabled: !!id, // initial check #1 if id param exists, prevents unnecessary API call
  });
};

export const useGetFoldersByVersionQuery = (versionId: number) => {
  return useQuery<Folder[], Error>({
    queryKey: FOLDER_QUERY_KEYS.byVersion(versionId),
    queryFn: () => FolderService.getFoldersByVersion(versionId),
    enabled: !!versionId,
  });
};

export const useCreateFolderMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Folder, Error, CreateFolderBody>({
    mutationFn: FolderService.createFolder,
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: FOLDER_QUERY_KEYS.byVersion(data.versionId),
      });
    },
  });
};

export const useUpdateFolderMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Folder, Error, { id: number } & UpdateFolderBody>({
    mutationFn: ({ id, ...data }) => FolderService.updateFolder(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: FOLDER_QUERY_KEYS.byVersion(data.versionId),
      });
    },
  });
};

export const useUploadFolderMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ folder: Folder; files: File[] }, Error, FormData>({
    mutationFn: FolderService.uploadFolder,
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: FOLDER_QUERY_KEYS.byVersion(data.folder.versionId),
      });
    },
  });
};

export const useDeleteFolderMutation = (versionId: number) => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean }, Error, number>({
    mutationFn: FolderService.deleteFolder,
    onSuccess: () => {
      // Invalidate folders by version
      queryClient.invalidateQueries({
        queryKey: FOLDER_QUERY_KEYS.byVersion(versionId),
      });
      
      // Also invalidate files by version since folder deletion might affect files
      queryClient.invalidateQueries({
        queryKey: ["versions", versionId, "files"],
      });
      
      // Force refetch
      queryClient.refetchQueries({
        queryKey: FOLDER_QUERY_KEYS.byVersion(versionId),
      });
    },
  });
};

export const useGetFolderContentsMutation = () => {
  return useMutation<void, Error, { id: number; folderName: string }>({
    mutationFn: async ({ id, folderName }) => {
      const response = await FolderService.getFolderContents(id);
      const url = window.URL.createObjectURL(response.data);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${folderName}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    },
  });
};

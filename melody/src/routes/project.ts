// src/routes/project.ts

import axios, { getAuthToken } from "@/lib/axios-config";
import { createWriteStream } from "@/lib/stream-config";
import {
  AddContributorBody,
  Conversation,
  CreateProjectBody,
  Project,
  ProjectUser,
  RemoveContributorBody,
  UpdateProjectBody,
} from "@/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const ProjectService = {
  async createProject(body: CreateProjectBody): Promise<Project> {
    const { data } = await axios.post<{ data: Project }>(
      `${API_URL}/projects`,
      body
    );
    return data.data;
  },

  async getProject(id: number): Promise<Project> {
    const { data } = await axios.get<{ data: Project }>(
      `${API_URL}/projects/${id}`
    );
    return data.data;
  },

  async updateProject(id: number, body: UpdateProjectBody): Promise<Project> {
    const { data } = await axios.put<Project>(
      `${API_URL}/projects/${id}`,
      body
    );
    return data;
  },

  async deleteProject(id: number): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/projects/${id}`
    );
    return data;
  },

  async addContributor(
    id: number,
    body: AddContributorBody
  ): Promise<ProjectUser> {
    const { data } = await axios.post<ProjectUser>(
      `${API_URL}/projects/${id}/contributors`,
      body
    );
    return data;
  },

  async removeContributor(
    id: number,
    body: RemoveContributorBody
  ): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/projects/${id}/contributors`,
      { data: body }
    );
    return data;
  },

  async downloadProject(id: number, versionId?: number): Promise<Blob> {
    const url = versionId
      ? `${API_URL}/projects/${id}/download?versionId=${versionId}`
      : `${API_URL}/projects/${id}/download`;

    const response = await axios.get(url, {
      responseType: "blob",
    });
    return response.data;
  },

  async getProjectConversations(id: number): Promise<Conversation[]> {
    const { data } = await axios.get<{ data: Conversation[] }>(
      `${API_URL}/projects/${id}/conversations`
    );
    return data.data;
  },
};

// Query Keys
// It is a history of what requests have been made to prevent re-runs
// Reduces latency
const PROJECT_QUERY_KEYS = {
  all: ["projects"] as const,
  details: (id: number) => ["projects", id] as const,
};

// Mutation Hooks
export const useCreateProjectMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Project, Error, CreateProjectBody>({
    mutationFn: ProjectService.createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PROJECT_QUERY_KEYS.all });
    },
  });
};

export const useUpdateProjectMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Project, Error, { id: number } & UpdateProjectBody>({
    mutationFn: ({ id, ...data }) => ProjectService.updateProject(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: PROJECT_QUERY_KEYS.all });
    },
  });
};

export const useDeleteProjectMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean }, Error, number>({
    mutationFn: ProjectService.deleteProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PROJECT_QUERY_KEYS.all });
    },
  });
};

export const useAddContributorMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<ProjectUser, Error, { id: number } & AddContributorBody>({
    mutationFn: ({ id, ...data }) => ProjectService.addContributor(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_QUERY_KEYS.details(id),
      });
    },
  });
};

export const useRemoveContributorMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    { success: boolean },
    Error,
    { id: number } & RemoveContributorBody
  >({
    mutationFn: ({ id, ...data }) => ProjectService.removeContributor(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_QUERY_KEYS.details(id),
      });
    },
  });
};

export const useDownloadProjectMutation = () => {
  return useMutation<
    void,
    Error,
    { id: number; projectName: string; versionId?: number }
  >({
    mutationFn: async ({ id, projectName, versionId }) => {
      const downloadName = `${projectName.replaceAll(" ", "_")}.zip`;

      // Create a write stream using our configured version with optimized settings
      const fileStream = createWriteStream(downloadName, {
        size: 0, // Let the browser determine size from response
        writableStrategy: { highWaterMark: 4 * 1024 * 1024 }, // 4MB buffer for larger project files
      });

      // Build the URL
      const downloadUrl = versionId
        ? `${API_URL}/projects/${id}/download?versionId=${versionId}`
        : `${API_URL}/projects/${id}/download`;

      // Fetch the file as a stream with optimized settings
      const response = await fetch(downloadUrl, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          Range: "bytes=0-", // Request the full file
        },
        credentials: "include", // Include cookies in the request
        priority: "high",
      });

      if (!response.ok) throw new Error("Download failed");

      // Pipe the response to the write stream
      if (window.WritableStream && response.body?.pipeTo) {
        // For browsers that support pipeTo
        return response.body.pipeTo(fileStream);
      } else {
        // For browsers that don't support pipeTo
        const reader = response.body?.getReader();
        const writer = fileStream.getWriter();

        if (!reader) throw new Error("Stream reading not supported");

        // Optimized pump function with larger chunks
        const pump = async () => {
          try {
            // Process in larger chunks for better performance
            while (true) {
              const { done, value } = await reader.read();
              if (done) {
                writer.close();
                break;
              }

              await writer.write(value);
            }
          } catch (err) {
            writer.abort(err);
            throw err;
          }
        };

        return pump();
      }
    },
  });
};

// Query Hooks
export const useGetProjectQuery = (id: number) => {
  return useQuery<Project, Error>({
    queryKey: PROJECT_QUERY_KEYS.details(id),
    queryFn: () => ProjectService.getProject(id),
    enabled: !!id,
  });
};

export const useGetProjectConversationsQuery = (id: number) => {
  return useQuery<Conversation[], Error>({
    queryKey: ["projects", id, "conversations"],
    queryFn: () => ProjectService.getProjectConversations(id),
    enabled: !!id,
  });
};

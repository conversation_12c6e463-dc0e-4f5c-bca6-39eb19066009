import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "../contexts/AuthContext";
import axios from "../lib/axios-config";
import { ProjectInvite } from "../models/projectInvite";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const ProjectInviteService = {
  async createProjectInvite(body: {
    projectId: number;
    userId: number;
  }): Promise<ProjectInvite> {
    const { data } = await axios.post<ProjectInvite>(
      `${API_URL}/project-invites`,
      body
    );
    return data;
  },

  async acceptProjectInvite(
    id: number
  ): Promise<{ success: boolean; message: string }> {
    const { data } = await axios.post<{ success: boolean; message: string }>(
      `${API_URL}/project-invites/${id}/accept`
    );
    return data;
  },

  async declineProjectInvite(
    id: number
  ): Promise<{ success: boolean; message: string }> {
    const { data } = await axios.post<{ success: boolean; message: string }>(
      `${API_URL}/project-invites/${id}/decline`
    );
    return data;
  },

  async revokeProjectInvite(
    id: number
  ): Promise<{ success: boolean; message: string }> {
    const { data } = await axios.delete<{ success: boolean; message: string }>(
      `${API_URL}/project-invites/${id}`
    );
    return data;
  },

  async getProjectInvites(projectId: number): Promise<ProjectInvite[]> {
    const { data } = await axios.get<{ data: ProjectInvite[] }>(
      `${API_URL}/projects/${projectId}/invites`
    );
    return data.data;
  },

  async getUserInvites(): Promise<ProjectInvite[]> {
    const { data } = await axios.get<{ data: ProjectInvite[] }>(
      `${API_URL}/users/invites`
    );
    return data.data;
  },
};

// Query Keys
const PROJECT_INVITE_QUERY_KEYS = {
  all: ["projectInvites"] as const,
  details: (id: number) => ["projectInvites", id] as const,
  byProject: (projectId: number) => ["projects", projectId, "invites"] as const,
  byUser: (userId: number) => ["users", userId, "invites"] as const,
  userInvites: ["user", "invites"] as const,
};

// Mutation Hooks
export const useCreateProjectInviteMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ProjectInvite,
    Error,
    { projectId: number; userId: number }
  >({
    mutationFn: ProjectInviteService.createProjectInvite,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.byProject(variables.projectId),
      });
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.byUser(variables.userId),
      });
    },
  });
};

export const useAcceptProjectInviteMutation = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean; message: string }, Error, number>({
    mutationFn: ProjectInviteService.acceptProjectInvite,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.userInvites,
      });
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.byUser(user?.id || 0),
      });
    },
  });
};

export const useDeclineProjectInviteMutation = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean; message: string }, Error, number>({
    mutationFn: ProjectInviteService.declineProjectInvite,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.userInvites,
      });
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.byUser(user?.id || 0),
      });
    },
  });
};

export const useRevokeProjectInviteMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean; message: string }, Error, number>({
    mutationFn: ProjectInviteService.revokeProjectInvite,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: PROJECT_INVITE_QUERY_KEYS.all,
      });
    },
  });
};

// Query Hooks
export const useGetProjectInvitesQuery = (
  projectId: number,
  options: { enabled: boolean } = { enabled: true }
) => {
  return useQuery<ProjectInvite[], Error>({
    queryKey: PROJECT_INVITE_QUERY_KEYS.byProject(projectId),
    queryFn: () => ProjectInviteService.getProjectInvites(projectId),
    enabled: !!projectId && options.enabled,
  });
};

export const useGetUserInvitesQuery = () => {
  const { user } = useAuth();
  return useQuery<ProjectInvite[], Error>({
    queryKey: PROJECT_INVITE_QUERY_KEYS.byUser(user?.id || 0),
    queryFn: () => ProjectInviteService.getUserInvites(),
  });
};

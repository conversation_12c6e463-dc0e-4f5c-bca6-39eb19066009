import { useMutation, useQuery, useQ<PERSON>y<PERSON>lient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import { CreateTaskBody, Task, UpdateTaskBody } from "../models/task";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

// Task Service
export const TaskService = {
  async createTask(body: CreateTaskBody): Promise<Task> {
    const { data } = await axios.post<{ data: Task }>(`${API_URL}/tasks`, body);
    return data.data;
  },

  async getTask(id: number): Promise<Task> {
    const { data } = await axios.get<{ data: Task }>(`${API_URL}/tasks/${id}`);
    return data.data;
  },

  async getTasksByProject(projectId: number): Promise<Task[]> {
    const { data } = await axios.get<{ data: Task[] }>(
      `${API_URL}/projects/${projectId}/tasks`
    );
    return data.data;
  },

  async updateTask(id: number, body: UpdateTaskBody): Promise<Task> {
    const { data } = await axios.put<Task>(`${API_URL}/tasks/${id}`, body);
    return data;
  },

  async deleteTask(id: number): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/tasks/${id}`
    );
    return data;
  },
};

// Query Keys
const TASK_QUERY_KEYS = {
  all: ["tasks"] as const,
  details: (id: number) => ["tasks", id] as const,
  byProject: (projectId: number) => ["projects", projectId, "tasks"] as const,
};

// Mutation Hooks
export const useCreateTaskMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, CreateTaskBody>({
    mutationFn: TaskService.createTask,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: TASK_QUERY_KEYS.byProject(variables.projectId),
      });
    },
  });
};

export const useUpdateTaskMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, { id: number } & UpdateTaskBody>({
    mutationFn: ({ id, ...data }) => TaskService.updateTask(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: TASK_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.byProject });
    },
  });
};

export const useDeleteTaskMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ success: boolean }, Error, number>({
    mutationFn: TaskService.deleteTask,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: TASK_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.byProject });
    },
  });
};

// Query Hooks
export const useGetTaskQuery = (id: number) => {
  return useQuery<Task, Error>({
    queryKey: TASK_QUERY_KEYS.details(id),
    queryFn: () => TaskService.getTask(id),
    enabled: !!id,
  });
};

export const useGetTasksByProjectQuery = (projectId: number) => {
  return useQuery<Task[], Error>({
    queryKey: TASK_QUERY_KEYS.byProject(projectId),
    queryFn: () => TaskService.getTasksByProject(projectId),
    enabled: !!projectId,
  });
};

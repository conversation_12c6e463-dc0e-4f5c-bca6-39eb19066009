// src/routes/user.ts

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";
import { Conversation } from "../models/chat";
import { Project } from "../models/project";
import { AudioSample, User } from "../models/user";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

// Add these types for pagination
interface PaginationParams {
  page: number;
  limit: number;
}

interface PaginatedUsersResponse {
  users: User[];
  totalPages: number;
  currentPage: number;
  totalUsers: number;
}

export const UserService = {
  async getUserProjects(name: string): Promise<Project[]> {
    const { data } = await axios.get<{ data: Project[] }>(
      `${API_URL}/users/${name}/projects`
    );
    return data.data;
  },
  async getUser(name: string): Promise<User> {
    const { data } = await axios.get<{ data: User }>(
      `${API_URL}/users/${name}`
    );
    return data.data;
  },
  async updateUser(name: string, userData: Partial<User>): Promise<User> {
    const { data } = await axios.put<{ data: User }>(
      `${API_URL}/users/${name}`,
      userData
    );
    return data.data;
  },
  async deleteUser(
    name: string,
    email: string,
    password: string
  ): Promise<void> {
    await axios.delete(`${API_URL}/users/${name}`, {
      data: { email, password },
    });
  },
  async searchUsers(query: string, includeTopGenres = false): Promise<User[]> {
    const { data } = await axios.get<{ data: User[] }>(
      `${API_URL}/users/search?q=${query}&includeTopGenres=${includeTopGenres}`
    );
    return data.data;
  },
  async getAllUsers(): Promise<User[]> {
    const { data } = await axios.get<{ data: User[] }>(`${API_URL}/users`);
    return data.data;
  },
  async getPaginatedUsers({
    page,
    limit,
  }: PaginationParams): Promise<PaginatedUsersResponse> {
    const { data } = await axios.get<{ data: PaginatedUsersResponse }>(
      `${API_URL}/users?page=${page}&limit=${limit}`
    );
    return data.data;
  },
  async getUserLeaflets(name: string): Promise<AudioSample[]> {
    const { data } = await axios.get<{ data: AudioSample[] }>(
      `${API_URL}/users/${name}/audio`
    );
    return data.data;
  },
  async getLeaflet(id: string): Promise<AudioSample> {
    const { data } = await axios.get<{ data: AudioSample }>(
      `${API_URL}/users/audio/${id}`
    );
    return data.data;
  },
  async createLeaflet(name: string, file: File): Promise<AudioSample> {
    const formData = new FormData();
    formData.append("name", name);
    formData.append("file", file);
    const { data } = await axios.post<{ data: AudioSample }>(
      `${API_URL}/users/audio`,
      formData
    );
    return data.data;
  },
  async updateLeaflet(
    id: string,
    name?: string,
    featured?: boolean
  ): Promise<AudioSample> {
    const { data } = await axios.put<{ data: AudioSample }>(
      `${API_URL}/users/audio/${id}`,
      { name, featured }
    );
    return data.data;
  },
  async deleteLeaflet(id: string): Promise<void> {
    await axios.delete(`${API_URL}/users/audio/${id}`);
  },
  async getLeafletContent(
    id: string
  ): Promise<{ signedUrl: string; fileName: string; waveform: number[] }> {
    const { data } = await axios.get<{
      data: { signedUrl: string; fileName: string; waveform: number[] };
    }>(`${API_URL}/users/audio/${id}/content`);
    return data.data;
  },
  async updateProfileImage(
    name: string,
    file: File
  ): Promise<{ profileImg: string }> {
    if (!file || !(file instanceof File)) {
      throw new Error("Invalid file provided for profile image update.");
    }
    if (file.size > 5 * 1024 * 1024) {
      throw new Error("File size exceeds 5MB limit for profile image.");
    }
    if (!file.type.startsWith("image/")) {
      throw new Error("Invalid file type. Only image files are allowed.");
    }
    const formData = new FormData();
    formData.append("file", file);
    const { data } = await axios.put<{ data: { profileImg: string } }>(
      `${API_URL}/users/${name}/profile-image`,
      formData
    );
    return data.data;
  },
  async getUserConversations(
    name: string,
    page: number = 1,
    pageSize: number = 20,
    type?: "all" | "created" | "linked"
  ): Promise<{
    conversations: Conversation[];
    total: number;
    hasMore: boolean;
  }> {
    const { data } = await axios.get<{
      data: { conversations: Conversation[]; total: number; hasMore: boolean };
    }>(
      `${API_URL}/users/${name}/conversations?page=${page}&pageSize=${pageSize}&type=${type}`
    );
    return data.data;
  },
};

export const useGetUserProjectsQuery = (name: string) => {
  return useQuery<Project[], Error>({
    queryKey: ["users", name, "projects"],
    queryFn: () => UserService.getUserProjects(name),
    enabled: !!name,
  });
};

export const useGetUserQuery = (
  name: string,
  options: { enabled: boolean } = { enabled: true } // Allow enabling/disabling the query
) => {
  return useQuery<User, Error>({
    queryKey: ["users", name],
    queryFn: () => UserService.getUser(name),
    enabled: options.enabled && !!name,
  });
};

export const useUpdateUserMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<User, Error, { name: string; userData: Partial<User> }>({
    mutationFn: ({ name, userData }) => UserService.updateUser(name, userData),
    onSuccess: (_, { name }) => {
      queryClient.invalidateQueries({ queryKey: ["users", name] });
    },
  });
};

export const useDeleteUserMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    void,
    Error,
    { name: string; email: string; password: string }
  >({
    mutationFn: ({ name, email, password }) =>
      UserService.deleteUser(name, email, password),
    onSuccess: (_, name) => {
      queryClient.invalidateQueries({ queryKey: ["users", name] });
    },
  });
};

export const useSearchUsersQuery = (
  query: string,
  includeTopGenres = false
) => {
  return useQuery<User[], Error>({
    queryKey: ["users", "search", query],
    queryFn: () => UserService.searchUsers(query, includeTopGenres),
    enabled: query.length >= 1, // Only search when user types 1 or more characters
  });
};

export const useGetAllUsersQuery = () => {
  return useQuery<User[], Error>({
    queryKey: ["users", "all"],
    queryFn: () => UserService.getAllUsers(),
  });
};

export const useGetPaginatedUsersQuery = ({
  page,
  limit,
}: PaginationParams) => {
  return useQuery<PaginatedUsersResponse, Error>({
    queryKey: ["users", "paginated", page, limit],
    queryFn: () => UserService.getPaginatedUsers({ page, limit }),
    enabled: page > 0 && limit > 0,
  });
};

export const useGetUserLeafletsQuery = (name: string) => {
  return useQuery<AudioSample[], Error>({
    queryKey: ["users", name, "audio"],
    queryFn: () => UserService.getUserLeaflets(name),
    enabled: !!name,
  });
};

export const useGetLeafletQuery = (id: string) => {
  return useQuery<AudioSample, Error>({
    queryKey: ["users", "audio", id],
    queryFn: () => UserService.getLeaflet(id),
    enabled: !!id,
  });
};

export const useCreateLeafletMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<AudioSample, Error, { name: string; file: File }>({
    mutationFn: ({ name, file }) => UserService.createLeaflet(name, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "audio"] });
    },
  });
};

export const useUpdateLeafletMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    AudioSample,
    Error,
    { id: string; name?: string; featured?: boolean }
  >({
    mutationFn: ({ id, name, featured }) =>
      UserService.updateLeaflet(id, name, featured),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["users", "audio", id] });
    },
  });
};

export const useDeleteLeafletMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (id) => UserService.deleteLeaflet(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ["users", "audio", id] });
    },
  });
};

export const useGetLeafletContentMutation = () => {
  return useMutation<
    { signedUrl: string; fileName: string; waveform: number[] },
    Error,
    string
  >({
    mutationFn: (id: string) => UserService.getLeafletContent(id),
  });
};

export const useUpdateProfileImageMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    { profileImg: string },
    Error,
    { name: string; file: File }
  >({
    mutationFn: ({ name, file }) => UserService.updateProfileImage(name, file),
    onSuccess: (_, { name }) => {
      queryClient.invalidateQueries({ queryKey: ["users", name] });
    },
  });
};

export const useGetUserConversationsQuery = (
  name: string,
  page: number = 1,
  pageSize: number = 20,
  type: "all" | "created" | "linked" = "all"
) => {
  return useQuery<
    { conversations: Conversation[]; total: number; hasMore: boolean },
    Error
  >({
    queryKey: ["users", name, "conversations", page, pageSize, type],
    queryFn: () => UserService.getUserConversations(name, page, pageSize, type),
    enabled: !!name,
  });
};

// export const useGetAudioSampleContentQuery = (id: string) => {
//   return useQuery<{ signedUrl: string; fileName: string }, Error>({
//     queryKey: ["users", "audio", id, "content"],
//     queryFn: () => UserService.getAudioSampleContent(id),
//     enabled: !!id,
//   });
// };

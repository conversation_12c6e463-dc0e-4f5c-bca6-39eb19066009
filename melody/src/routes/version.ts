// melody/src/routes/version.ts

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "../lib/axios-config";

import {
  CreateVersionBody,
  UpdateVersionBody,
  Version,
} from "../models/version";

const API_URL = process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3200";

export const VersionService = {
  async createVersion(body: CreateVersionBody): Promise<Version> {
    const { data } = await axios.post<Version>(`${API_URL}/versions`, body);
    return data;
  },

  async getVersion(id: number): Promise<Version> {
    const { data } = await axios.get<{ data: Version }>(
      `${API_URL}/versions/${id}`
    );
    return data.data;
  },

  async getVersionsByProject(projectId: number): Promise<Version[]> {
    const { data } = await axios.get<{ data: Version[] }>(
      `${API_URL}/projects/${projectId}/versions`
    );
    return data.data;
  },
  async updateVersion(id: number, body: UpdateVersionBody): Promise<Version> {
    const { data } = await axios.put<Version>(
      `${API_URL}/versions/${id}`,
      body
    );
    return data;
  },

  async deleteVersion(id: number): Promise<{ success: boolean }> {
    const { data } = await axios.delete<{ success: boolean }>(
      `${API_URL}/versions/${id}`
    );
    return data;
  },
};

// Query Keys
const VERSION_QUERY_KEYS = {
  all: ["versions"] as const,
  details: (id: number) => ["versions", id] as const,
  byProject: (projectId: number) =>
    ["projects", projectId, "versions"] as const,
};

// Mutation Hooks
export const useCreateVersionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Version, Error, CreateVersionBody>({
    mutationFn: VersionService.createVersion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: VERSION_QUERY_KEYS.all });
    },
  });
};

export const useUpdateVersionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Version, Error, { id: number } & UpdateVersionBody>({
    mutationFn: ({ id, ...data }) => VersionService.updateVersion(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: VERSION_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: VERSION_QUERY_KEYS.all });
    },
  });
};

export const useDeleteVersionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    { success: boolean },
    Error,
    { id: number; projectId: number }
  >({
    mutationFn: ({ id }) => VersionService.deleteVersion(id),
    onSuccess: (_, { id, projectId }) => {
      // Add these invalidations
      queryClient.invalidateQueries({
        queryKey: VERSION_QUERY_KEYS.details(id),
      });
      queryClient.invalidateQueries({ queryKey: VERSION_QUERY_KEYS.all });
      // Also invalidate the project-specific versions query
      queryClient.invalidateQueries({
        queryKey: ["projects", projectId, "versions"],
      });
      // Invalidate the project details since versions are included in project data
      queryClient.invalidateQueries({
        queryKey: ["projects"],
      });
    },
  });
};

// Query Hooks
export const useGetVersionQuery = (id: number) => {
  return useQuery<Version, Error>({
    queryKey: VERSION_QUERY_KEYS.details(id),
    queryFn: () => VersionService.getVersion(id),
    enabled: !!id,
  });
};

export const useGetVersionsByProjectQuery = (projectId: number) => {
  return useQuery<Version[], Error>({
    queryKey: ["projects", projectId, "versions"] as const,
    queryFn: () => VersionService.getVersionsByProject(projectId),
    enabled: !!projectId,
  });
};

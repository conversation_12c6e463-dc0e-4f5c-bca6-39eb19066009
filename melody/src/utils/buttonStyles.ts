// melody/src/utils/buttonStyles.ts
const primaryButtonStyles = {
  backgroundColor: "#4338ca",
  color: "#cbd5e1",
  textTransform: "none",
  fontSize: "16px",
  transition: "all 200ms ease-in-out",
  gap: "4px",
  "&:hover": {
    backgroundColor: "#3730a3",
    color: "#cbd5e1",
    scale: 1.05,
  },
};

const outlinedDangerButtonStyles = {
  fontSize: "16px",
  textTransform: "none",
  border: "1px #a81a1a solid",
  color: "#a81a1a",
  transition: "all 200ms ease-in-out",
  gap: "4px",
  "&:hover": {
    backgroundColor: "#a81a1a",
    color: "#cbd5e1",
    scale: 1.05,
  },
};

const textButtonStyles = {
  fontSize: "16px",
  textTransform: "none",
  color: "white",
  transition: "all 200ms ease-in-out",
  gap: "4px",
  "&:hover": {
    backgroundColor: "rgba(255, 255, 255, 0.08)",
    scale: 1.05,
  },
};

const subtleButtonStyles = {
  fontSize: "16px",
  textTransform: "none",
  color: "gray.400",
  transition: "all 200ms ease-in-out",
  gap: "4px",
  "&:hover": {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    scale: 1.05,
  },
};

export const buttonStyles = {
  primaryButtonStyles,
  outlinedDangerButtonStyles,
  textButtonStyles,
  subtleButtonStyles,
};

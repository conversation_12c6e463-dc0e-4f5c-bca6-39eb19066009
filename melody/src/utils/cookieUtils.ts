/**
 * Checks if cookies are enabled in the browser
 */
export const areCookiesEnabled = (): boolean => {
  try {
    // Try to set a test cookie
    document.cookie = "cookieTest=1; path=/; SameSite=Lax";
    const cookieEnabled = document.cookie.indexOf("cookieTest=") !== -1;

    // Clean up test cookie
    document.cookie =
      "cookieTest=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax";

    return cookieEnabled;
  } catch (e) {
    // In case of any errors, assume cookies are disabled
    return false;
  }
};

/**
 * Gets a cookie value by name
 */
export const getCookie = (name: string): string | undefined => {
  return document.cookie
    .split("; ")
    .find((row) => row.startsWith(`${name}=`))
    ?.split("=")[1];
};

/**
 * Gets a token from either cookie or localStorage
 */
export const getToken = (name: string): string | null => {
  // Try cookie first
  const cookieValue = getCookie(name);
  if (cookieValue) return cookieValue;

  // Fall back to localStorage
  return localStorage.getItem(name);
};

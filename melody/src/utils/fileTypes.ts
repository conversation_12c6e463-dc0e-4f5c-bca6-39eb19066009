// src/utils/fileTypes.ts

// ==================== DIGITAL AUDIO FILES ====================
// Uncompressed audio formats
const uncompressedAudioFormats: string[] = [
  ".wav", // Waveform Audio File Format
  ".aif", // Audio Interchange File Format
  ".aiff", // Audio Interchange File Format (full name)
  ".bwf", // Broadcast Wave Format
  ".sd2", // Sound Designer II
  ".au", // Unix Audio
  ".caf", // Core Audio Format (Apple)
];

// Lossy compression audio formats
const lossyCompressedAudioFormats: string[] = [
  ".mp3", // MPEG-1 Audio Layer 3
  ".m4a", // AAC container
  ".aac", // Advanced Audio Coding
  ".ogg", // Ogg Vorbis
  ".wma", // Windows Media Audio
  ".opus", // Opus Audio Format
];

// Lossless compression audio formats
const losslessCompressedAudioFormats: string[] = [
  ".flac", // Free Lossless Audio Codec
  ".alac", // Apple Lossless Audio Codec
  ".mqa", // Master Quality Authenticated
  ".dsd", // Direct Stream Digital (high-resolution)
];

// All digital audio formats combined
const digitalAudioFormats: string[] = [
  ...uncompressedAudioFormats,
  ...lossyCompressedAudioFormats,
  ...losslessCompressedAudioFormats,
];

// ==================== DAW PROJECT FILES ====================
// Ableton formats
const abletonFormats: string[] = [
  ".als", // Ableton Live Session
  ".alp", // Ableton Live Pack
];

// Apple DAW formats
const appleDAWFormats: string[] = [
  ".logicx", // Logic Pro X Project
  ".logic", // Logic Pro (older version)
  ".sng", // GarageBand Project
  ".band", // GarageBand Project (newer)
];

// Avid formats
const avidFormats: string[] = [
  ".ptx", // Pro Tools Session
  ".pts", // Pro Tools Session (older)
];

// Adobe formats
const adobeFormats: string[] = [
  ".sesx", // Adobe Audition Session
];

// Steinberg formats
const steinbergFormats: string[] = [
  ".cpr", // Cubase Project
  ".npr", // Nuendo Project
];

// Other major DAW formats
const otherMajorDAWFormats: string[] = [
  ".rpp", // REAPER Project
  ".rea", // REAPER Project (alternative)
  ".flp", // FL Studio Project
  ".aup", // Audacity Project
  ".reason", // Reason Project
  ".ardour", // Ardour Project
  ".bitwig", // Bitwig Studio Project
  ".sts", // Studio One Song
  ".dp", // Digital Performer Project
];

// Cakewalk/SONAR formats
const cakewalkFormats: string[] = [
  ".sonar", // SONAR Project
  ".son", // SONAR Project (older)
  ".sfl", // SONAR Project (older)
  ".sfw", // SONAR Project (older)
];

// Other DAW formats
const otherDAWFormats: string[] = [
  ".mmpz", // LMMS Project
  ".rns", // Renoise Song
];

// All DAW project formats combined
const dawProjectFormats: string[] = [
  ...abletonFormats,
  ...appleDAWFormats,
  ...avidFormats,
  ...adobeFormats,
  ...steinbergFormats,
  ...otherMajorDAWFormats,
  ...cakewalkFormats,
  ...otherDAWFormats,
];

// ==================== MIDI AND NOTATION ====================
// MIDI formats
const midiFormats: string[] = [
  ".mid", // Standard MIDI File
  ".midi", // Standard MIDI File (alternative extension)
  ".kar", // Karaoke MIDI
  ".gm", // General MIDI File
];

// Notation formats
const notationFormats: string[] = [
  ".musicxml", // Music XML (notation interchange)
  ".sib", // Sibelius Score
  ".mus", // Finale Score (older)
  ".musx", // Finale Score (newer)
  ".mscz", // MuseScore
];

// Guitar tablature formats
const tablatureFormats: string[] = [
  ".gp", // Guitar Pro
  ".gpx", // Guitar Pro 6+
];

// All MIDI and notation formats combined
const midiAndNotationFormats: string[] = [
  ...midiFormats,
  ...notationFormats,
  ...tablatureFormats,
];

// ==================== SAMPLER AND VIRTUAL INSTRUMENT FILES ====================
// Sample library formats
const sampleLibraryFormats: string[] = [
  ".sf2", // SoundFont 2
  ".sfz", // SFZ Format
  ".gig", // GigaSampler/GigaStudio Instrument
];

// DAW-specific instrument formats
const dawInstrumentFormats: string[] = [
  ".xrni", // Renoise Instrument
  ".exs", // EXS24 Sampler Instrument (Logic)
  ".patch", // Reason Patch
];

// Hardware sampler formats
const hardwareSamplerFormats: string[] = [
  ".akp", // Akai Program
  ".akm", // Akai MIDI Map
];

// Plugin preset formats
const pluginPresetFormats: string[] = [
  ".fxp", // Effect Preset
  ".fxb", // Effect Bank
];

// Native Instruments formats
const nativeInstrumentsFormats: string[] = [
  ".nki", // Kontakt Instrument
  ".nkm", // Kontakt Multi
  ".nkx", // Kontakt Monolith
];

// Ableton device formats
const abletonDeviceFormats: string[] = [
  ".adg", // Ableton Device Group
  ".adv", // Ableton Device Preset
  ".amxd", // Ableton Max for Live Device
];

// Plugin formats
const pluginFormats: string[] = [
  ".dll", // Windows Plugin/VST
  ".vst", // VST Plugin
  ".vst3", // VST3 Plugin
  ".component", // Audio Unit Plugin (macOS)
  ".aupreset", // Audio Unit Preset
  ".tlp", // TAL Preset
  ".vstpreset", // VST Preset
];

// All sampler and virtual instrument formats combined
const samplerAndInstrumentFormats: string[] = [
  ...sampleLibraryFormats,
  ...dawInstrumentFormats,
  ...hardwareSamplerFormats,
  ...pluginPresetFormats,
  ...nativeInstrumentsFormats,
  ...abletonDeviceFormats,
  ...pluginFormats,
];

// ==================== LOOP AND SAMPLE FILES ====================
// Loop formats
const loopFormats: string[] = [
  ".rex", // ReCycle Loop
  ".rx2", // ReCycle Loop (newer)
  ".acid", // ACID Loop
  ".groove", // Groove Template
];

// Sample service formats
const sampleServiceFormats: string[] = [
  ".splice", // Splice Sample
  ".samplepack", // Sample Pack Format
];

// All loop and sample formats combined
const loopAndSampleFormats: string[] = [
  ...loopFormats,
  ...sampleServiceFormats,
];

// ==================== STEMS AND SESSION INTERCHANGE ====================
// Industry standard interchange formats
const interchangeFormats: string[] = [
  ".omf", // Open Media Framework Interchange
  ".omfi", // Open Media Framework Interchange (alternative)
  ".aaf", // Advanced Authoring Format
  ".mtf", // Media Transfer Format
  ".xml", // Session interchange (various DAWs)
];

// Specialized interchange formats
const specializedInterchangeFormats: string[] = [
  ".stem", // Native Instruments STEM Format
];

// Archive formats commonly used for exchanging music projects
const archiveFormats: string[] = [
  ".zip", // ZIP Archive
  ".rar", // RAR Archive
  ".7z", // 7-Zip Archive
];

// All stems and session interchange formats combined
const stemsAndInterchangeFormats: string[] = [
  ...interchangeFormats,
  ...specializedInterchangeFormats,
  ...archiveFormats,
];

// ==================== FINAL COMBINED ARRAY ====================
// All music production file extensions combined
export const FINAL_ALLOWED_EXTENSIONS: string[] = [
  ...digitalAudioFormats,
  ...dawProjectFormats,
  ...midiAndNotationFormats,
  ...samplerAndInstrumentFormats,
  ...loopAndSampleFormats,
  ...stemsAndInterchangeFormats,
];

export const getFileType = (fileName: string): string => {
  const ext = fileName.toLowerCase().split(".").pop();
  const mimeTypes: { [key: string]: string } = {
    // ==================== DIGITAL AUDIO FILES ====================
    // Uncompressed audio formats
    wav: "audio/wav",
    aif: "audio/aiff",
    aiff: "audio/aiff",
    bwf: "audio/wav", // Broadcast Wave Format (WAV variant)
    sd2: "audio/x-sd2",
    au: "audio/basic",
    caf: "audio/x-caf",

    // Lossy compression audio formats
    mp3: "audio/mpeg",
    m4a: "audio/mp4",
    aac: "audio/aac",
    ogg: "audio/ogg",
    wma: "audio/x-ms-wma",
    opus: "audio/opus",

    // Lossless compression audio formats
    flac: "audio/flac",
    alac: "audio/mp4", // Apple Lossless in M4A container
    mqa: "audio/x-mqa",
    dsd: "audio/x-dsd",

    // ==================== DAW PROJECT FILES ====================
    // Ableton formats
    als: "application/x-ableton-live-session",
    alp: "application/x-ableton-live-pack",

    // Apple DAW formats
    logicx: "application/x-logic-pro-project",
    logic: "application/x-logic-pro-project",
    sng: "application/x-garageband-project",
    band: "application/x-garageband-project",

    // Avid formats
    ptx: "application/x-protools-session",
    pts: "application/x-protools-session",

    // Adobe formats
    sesx: "application/x-audition-session",

    // Steinberg formats
    cpr: "application/x-cubase-project",
    npr: "application/x-nuendo-project",

    // Other major DAW formats
    rpp: "application/x-reaper-project",
    rea: "application/x-reaper-project",
    flp: "application/x-fruityloops-project",
    aup: "application/x-audacity-project",
    reason: "application/x-reason-project",
    ardour: "application/x-ardour-project",
    bitwig: "application/x-bitwig-project",
    sts: "application/x-studio-one-song",
    dp: "application/x-digital-performer-project",

    // Cakewalk/SONAR formats
    sonar: "application/x-sonar-project",
    son: "application/x-sonar-project",
    sfl: "application/x-sonar-project",
    sfw: "application/x-sonar-project",

    // Other DAW formats
    mmpz: "application/x-lmms-project",
    rns: "application/x-renoise-song",

    // ==================== MIDI AND NOTATION ====================
    // MIDI formats
    mid: "audio/midi",
    midi: "audio/midi",
    kar: "audio/midi", // Karaoke MIDI
    gm: "audio/midi",

    // Notation formats
    musicxml: "application/vnd.recordare.musicxml+xml",
    sib: "application/x-sibelius-score",
    mus: "application/x-finale-score",
    musx: "application/x-finale-score",
    mscz: "application/x-musescore",

    // Guitar tablature formats
    gp: "application/x-guitar-pro",
    gpx: "application/x-guitar-pro",

    // ==================== SAMPLER AND VIRTUAL INSTRUMENT FILES ====================
    // Sample library formats
    sf2: "application/x-soundfont",
    sfz: "application/x-sfz",
    gig: "application/x-gigasampler",

    // DAW-specific instrument formats
    xrni: "application/x-renoise-instrument",
    exs: "application/x-exs24-instrument",
    patch: "application/x-reason-patch",

    // Hardware sampler formats
    akp: "application/x-akai-program",
    akm: "application/x-akai-midi-map",

    // Plugin preset formats
    fxp: "application/x-vst-preset",
    fxb: "application/x-vst-bank",

    // Native Instruments formats
    nki: "application/x-kontakt-instrument",
    nkm: "application/x-kontakt-multi",
    nkx: "application/x-kontakt-monolith",

    // Ableton device formats
    adg: "application/x-ableton-device-group",
    adv: "application/x-ableton-device-preset",
    amxd: "application/x-ableton-max-device",

    // Plugin formats
    dll: "application/x-msdownload", // Windows DLL
    vst: "application/x-vst-plugin",
    vst3: "application/x-vst3-plugin",
    component: "application/x-audio-unit", // macOS Audio Unit
    aupreset: "application/x-audio-unit-preset",
    tlp: "application/x-tal-preset",
    vstpreset: "application/x-vst-preset",

    // ==================== LOOP AND SAMPLE FILES ====================
    // Loop formats
    rex: "application/x-recycle-loop",
    rx2: "application/x-recycle-loop",
    acid: "application/x-acid-loop",
    groove: "application/x-groove-template",

    // Sample service formats
    splice: "application/x-splice-sample",
    samplepack: "application/x-sample-pack",

    // ==================== STEMS AND SESSION INTERCHANGE ====================
    // Industry standard interchange formats
    omf: "application/x-omf",
    omfi: "application/x-omf",
    aaf: "application/x-aaf",
    mtf: "application/x-media-transfer",
    xml: "application/xml",

    // Specialized interchange formats
    stem: "application/x-ni-stem",

    // Archive formats
    zip: "application/zip",
    rar: "application/x-rar-compressed",
    "7z": "application/x-7z-compressed",

    // ==================== GENERAL PURPOSE FILES ====================
    // Text and code files (keeping your original ones)
    txt: "text/plain",
    js: "text/javascript",
    ts: "text/typescript",
    json: "application/json",
    html: "text/html",
    css: "text/css",
    md: "text/markdown",
    py: "text/x-python",
    java: "text/x-java-source",
    cpp: "text/x-c++src",
    c: "text/x-csrc",
    h: "text/x-chdr",

    // Additional common file types that might be in music projects
    pdf: "application/pdf",
    doc: "application/msword",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    rtf: "application/rtf",
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    svg: "image/svg+xml",
    bmp: "image/bmp",
    tiff: "image/tiff",
    webp: "image/webp",
  };

  return mimeTypes[ext || ""] || "application/octet-stream";
};

/**
 * Decodes a file path from Supabase storage back to its original form
 * @param encodedPath Encoded path from Supabase storage
 * @returns Original path with special characters restored
 */
export function decodePath(encodedPath: string): string {
  // Restore original characters
  return encodedPath
    .split('/')
    .map(part => 
      part
        .replace(/_SPACE_/g, ' ')
        .replace(/_PLUS_/g, '+')
        .replace(/_PERCENT_/g, '%')
        .replace(/_HASH_/g, '#')
        .replace(/_QUESTION_/g, '?')
        .replace(/_AMP_/g, '&')
        .replace(/_EQUAL_/g, '=')
        .replace(/_COLON_/g, ':')
        .replace(/_AT_/g, '@')
        .replace(/_LT_/g, '<')
        .replace(/_GT_/g, '>')
        .replace(/_QUOTE_/g, '"')
        .replace(/_APOS_/g, '\'')
        .replace(/_BACKTICK_/g, '`')
        .replace(/_LCURLY_/g, '{')
        .replace(/_RCURLY_/g, '}')
        .replace(/_LBRACKET_/g, '[')
        .replace(/_RBRACKET_/g, ']')
        .replace(/_PIPE_/g, '|')
        .replace(/_BACKSLASH_/g, '\\')
        .replace(/_CARET_/g, '^')
        .replace(/_TILDE_/g, '~')
    )
    .join('/');
}
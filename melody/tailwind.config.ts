import type { Config } from "tailwindcss";
import { deviceDetectPlugin } from "./src/utils/tailwind-device-plugin";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        bgdark: "#0c0e19",
        bglight: "#131728",
        accent: "#4338ca",
      },
      fontFamily: {
        roboto: ["var(--font-roboto)"],
        "instrument-sans": ["var(--font-instrument-sans)"],
      },
    },
  },
  plugins: [deviceDetectPlugin],
};
export default config;
